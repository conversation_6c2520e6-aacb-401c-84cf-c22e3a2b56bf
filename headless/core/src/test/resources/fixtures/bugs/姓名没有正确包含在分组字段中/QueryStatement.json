{"dataSetId": 9, "sql": null, "errMsg": null, "structQueryParam": null, "sqlQueryParam": {"sql": "WITH _student_consumption_ AS (SELECT 消费地址, 消费商户名称, SUM(消费金额) AS _总消费金额_ FROM 昆工学生数据集 WHERE 姓名 = '张三' GROUP BY 消费地址, 消费商户名称) SELECT 姓名, 消费地址, 消费商户名称, _总消费金额_ FROM _student_consumption_", "table": null, "supportWith": true, "withAlias": true, "simplifiedSql": null}, "ontologyQueryParam": null, "status": 0, "isS2SQL": true, "enableOptimize": true, "minMaxTime": null, "ontology": {"metrics": [{"name": "QFJE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSSFXX_QFJE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSSFXX_QFJE", "fieldMetric": false}}, {"name": "TFJE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSSFXX_TFJE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSSFXX_TFJE", "fieldMetric": false}}, {"name": "YJJE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSSFXX_YJJE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSSFXX_YJJE", "fieldMetric": false}}, {"name": "SJJE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSSFXX_SJJE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSSFXX_SJJE", "fieldMetric": false}}, {"name": "JMJE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSSFXX_JMJE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSSFXX_JMJE", "fieldMetric": false}}, {"name": "DKJE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSSFXX_DKJE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSSFXX_DKJE", "fieldMetric": false}}, {"name": "JLJE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSJLZLB_JLJE", "agg": null, "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSJLZLB_JLJE", "fieldMetric": false}}, {"name": "JFZH", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "DMKJ_JFZH", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "DMKJ_JFZH", "fieldMetric": false}}, {"name": "XSZH", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "DMKJ_XSZH", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "DMKJ_XSZH", "fieldMetric": false}}, {"name": "ZHHXFZH", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "DMKJ_ZHHXFZH", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "DMKJ_ZHHXFZH", "fieldMetric": false}}, {"name": "CJ", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "ZHCPCJXX_CJ", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "ZHCPCJXX_CJ", "fieldMetric": false}}, {"name": "PCFS", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "ZHCPZBCJB_PCFS", "agg": null, "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "ZHCPZBCJB_PCFS", "fieldMetric": false}}, {"name": "ZHYE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSXFXXVIEW_ZHYE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSXFXXVIEW_ZHYE", "fieldMetric": false}}, {"name": "KNYE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSXFXXVIEW_KNYE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSXFXXVIEW_KNYE", "fieldMetric": false}}, {"name": "JYJE", "owners": ["admin"], "type": "ATOMIC", "metricTypeParams": {"measures": [{"name": "XSXFXXVIEW_JYJE", "agg": "sum", "expr": null, "constraint": null, "alias": null, "createMetric": null}], "metrics": null, "fields": null, "expr": "XSXFXXVIEW_JYJE", "fieldMetric": false}}], "dataModelMap": {"SQ_MJXX": {"id": 32, "name": "SQ_MJXX", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.SQ_MJXX", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "JCFX", "owners": null, "type": null, "expr": "JCFX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JCFX", "defaultValues": null, "ext": null}, {"name": "DQBZ", "owners": null, "type": null, "expr": "DQBZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DQBZ", "defaultValues": null, "ext": null}, {"name": "JCSJ", "owners": null, "type": null, "expr": "JCSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "JCSJ", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "XSCCZLB": {"id": 27, "name": "XSCCZLB", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.XSCCZLB", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "WJJK", "owners": null, "type": null, "expr": "WJJK", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WJJK", "defaultValues": null, "ext": null}, {"name": "CFZTM", "owners": null, "type": null, "expr": "CFZTM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFZTM", "defaultValues": null, "ext": null}, {"name": "CLBM", "owners": null, "type": null, "expr": "CLBM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CLBM", "defaultValues": null, "ext": null}, {"name": "CFMCM", "owners": null, "type": null, "expr": "CFMCM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFMCM", "defaultValues": null, "ext": null}, {"name": "CFYY", "owners": null, "type": null, "expr": "CFYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFYY", "defaultValues": null, "ext": null}, {"name": "CFGYR", "owners": null, "type": null, "expr": "CFGYR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFGYR", "defaultValues": null, "ext": null}, {"name": "WJRQ", "owners": null, "type": null, "expr": "WJRQ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "WJRQ", "defaultValues": null, "ext": null}, {"name": "CFCXRQ", "owners": null, "type": null, "expr": "CFCXRQ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "CFCXRQ", "defaultValues": null, "ext": null}, {"name": "CFRQ", "owners": null, "type": null, "expr": "CFRQ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "CFRQ", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "ZHCPZBCJB": {"id": 42, "name": "ZHCPZBCJB", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.ZHCPZBCJB", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "CPLXMC", "owners": null, "type": null, "expr": "CPLXMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CPLXMC", "defaultValues": null, "ext": null}, {"name": "XNXQ", "owners": null, "type": null, "expr": "XNXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XNXQ", "defaultValues": null, "ext": null}], "measures": [{"name": "ZHCPZBCJB_PCFS", "agg": null, "expr": "PCFS", "constraint": null, "alias": null, "createMetric": null}], "aggTime": "none", "timePartType": null}, "ZHCPCJXX": {"id": 40, "name": "ZHCPCJXX", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.ZHCPCJXX", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "XN", "owners": null, "type": null, "expr": "XN", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XN", "defaultValues": null, "ext": null}, {"name": "XQ", "owners": null, "type": null, "expr": "XQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XQ", "defaultValues": null, "ext": null}, {"name": "BJPM", "owners": null, "type": null, "expr": "BJPM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BJPM", "defaultValues": null, "ext": null}, {"name": "ZYPM", "owners": null, "type": null, "expr": "ZYPM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYPM", "defaultValues": null, "ext": null}], "measures": [{"name": "ZHCPCJXX_CJ", "agg": "sum", "expr": "CJ", "constraint": null, "alias": null, "createMetric": null}], "aggTime": "none", "timePartType": null}, "XSTSJYXXB": {"id": 54, "name": "XSTSJYXXB", "modelId": 2, "type": "oracle", "sqlQuery": "select \r\n  t1.XGH as XH,t2.TSMC,t2.HSD,t2.JSD,t2.JYSJ,t2.SJGHSJ\r\nfrom kust.DZJBSJZLB t1 \r\nleft join kust.TSLSJYSJZLB t2 on t1.JSZH = t2.JSZH\r\n", "tableQuery": "", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "TSMC", "owners": null, "type": null, "expr": "TSMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TSMC", "defaultValues": null, "ext": null}, {"name": "HSD", "owners": null, "type": null, "expr": "HSD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HSD", "defaultValues": null, "ext": null}, {"name": "JSD", "owners": null, "type": null, "expr": "JSD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JSD", "defaultValues": null, "ext": null}, {"name": "JYSJ", "owners": null, "type": null, "expr": "JYSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "JYSJ", "defaultValues": null, "ext": null}, {"name": "SJGHSJ", "owners": null, "type": null, "expr": "SJGHSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "SJGHSJ", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "DMKJ": {"id": 39, "name": "DMKJ", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.DMKJ", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [], "measures": [{"name": "DMKJ_JFZH", "agg": "sum", "expr": "JFZH", "constraint": null, "alias": null, "createMetric": null}, {"name": "DMKJ_XSZH", "agg": "sum", "expr": "XSZH", "constraint": null, "alias": null, "createMetric": null}, {"name": "DMKJ_ZHHXFZH", "agg": "sum", "expr": "ZHHXFZH", "constraint": null, "alias": null, "createMetric": null}], "aggTime": "none", "timePartType": null}, "XJYDSJL": {"id": 37, "name": "XJYDSJL", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.XJYDSJL", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "YDLXMC", "owners": null, "type": null, "expr": "YDLXMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YDLXMC", "defaultValues": null, "ext": null}, {"name": "YDYY", "owners": null, "type": null, "expr": "YDYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YDYY", "defaultValues": null, "ext": null}, {"name": "YDSJ", "owners": null, "type": null, "expr": "YDSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "YDSJ", "defaultValues": null, "ext": null}, {"name": "FIELDS_TEXT", "owners": null, "type": null, "expr": "FIELDS_TEXT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FIELDS_TEXT", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "XSSFXX": {"id": 26, "name": "XSSFXX", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.XSSFXX", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "SFXMMC", "owners": null, "type": null, "expr": "SFXMMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFXMMC", "defaultValues": null, "ext": null}, {"name": "SFQJMC", "owners": null, "type": null, "expr": "SFQJMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFQJMC", "defaultValues": null, "ext": null}], "measures": [{"name": "XSSFXX_QFJE", "agg": "sum", "expr": "QFJE", "constraint": null, "alias": null, "createMetric": null}, {"name": "XSSFXX_TFJE", "agg": "sum", "expr": "TFJE", "constraint": null, "alias": null, "createMetric": null}, {"name": "XSSFXX_YJJE", "agg": "sum", "expr": "YJJE", "constraint": null, "alias": null, "createMetric": null}, {"name": "XSSFXX_SJJE", "agg": "sum", "expr": "SJJE", "constraint": null, "alias": null, "createMetric": null}, {"name": "XSSFXX_JMJE", "agg": "sum", "expr": "JMJE", "constraint": null, "alias": null, "createMetric": null}, {"name": "XSSFXX_DKJE", "agg": "sum", "expr": "DKJE", "constraint": null, "alias": null, "createMetric": null}], "aggTime": "none", "timePartType": null}, "SSWGWGXX": {"id": 31, "name": "SSWGWGXX", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.SSWGWGXX", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "WJFS", "owners": null, "type": null, "expr": "WJFS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WJFS", "defaultValues": null, "ext": null}, {"name": "JCSJ", "owners": null, "type": null, "expr": "JCSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "JCSJ", "defaultValues": null, "ext": null}, {"name": "YGSSJ", "owners": null, "type": null, "expr": "YGSSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "YGSSJ", "defaultValues": null, "ext": null}, {"name": "JCFX", "owners": null, "type": null, "expr": "JCFX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JCFX", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "XSJBXX": {"id": 20, "name": "XSJBXX", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.XSJBXX", "identifiers": [{"name": "SFZH", "type": "foreign"}, {"name": "XH", "type": "primary"}, {"name": "XLDM", "type": "foreign"}, {"name": "ZYDM", "type": "foreign"}], "dimensions": [{"name": "SZBJ", "owners": null, "type": null, "expr": "SZBJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SZBJ", "defaultValues": null, "ext": null}, {"name": "BZR", "owners": null, "type": null, "expr": "BZR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BZR", "defaultValues": null, "ext": null}, {"name": "JTDH", "owners": null, "type": null, "expr": "JTDH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTDH", "defaultValues": null, "ext": null}, {"name": "SZYX", "owners": null, "type": null, "expr": "SZYX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SZYX", "defaultValues": null, "ext": null}, {"name": "JTHKSZD", "owners": null, "type": null, "expr": "JTHKSZD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTHKSZD", "defaultValues": null, "ext": null}, {"name": "RXSJ", "owners": null, "type": null, "expr": "RXSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "RXSJ", "defaultValues": null, "ext": null}, {"name": "XM", "owners": null, "type": null, "expr": "XM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XM", "defaultValues": null, "ext": null}, {"name": "BYSJ", "owners": null, "type": null, "expr": "BYSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "BYSJ", "defaultValues": null, "ext": null}, {"name": "SFSC", "owners": null, "type": null, "expr": "SFSC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFSC", "defaultValues": null, "ext": null}, {"name": "JTSZD", "owners": null, "type": null, "expr": "JTSZD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTSZD", "defaultValues": null, "ext": null}, {"name": "JTDZ", "owners": null, "type": null, "expr": "JTDZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTDZ", "defaultValues": null, "ext": null}, {"name": "ZXF", "owners": null, "type": null, "expr": "ZXF", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZXF", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "XSJLZLB": {"id": 38, "name": "XSJLZLB", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.XSJLZLB", "identifiers": [{"name": "XH", "type": "primary"}, {"name": "JLJBM", "type": "foreign"}, {"name": "JLDJM", "type": "foreign"}], "dimensions": [{"name": "JLMC", "owners": null, "type": null, "expr": "JLMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLMC", "defaultValues": null, "ext": null}, {"name": "HJSJ", "owners": null, "type": null, "expr": "HJSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "HJSJ", "defaultValues": null, "ext": null}, {"name": "HJXQ", "owners": null, "type": null, "expr": "HJXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HJXQ", "defaultValues": null, "ext": null}, {"name": "BJDW", "owners": null, "type": null, "expr": "BJDW", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BJDW", "defaultValues": null, "ext": null}, {"name": "JLYY", "owners": null, "type": null, "expr": "JLYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLYY", "defaultValues": null, "ext": null}, {"name": "HJXND", "owners": null, "type": null, "expr": "HJXND", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HJXND", "defaultValues": null, "ext": null}], "measures": [{"name": "XSJLZLB_JLJE", "agg": null, "expr": "JLJE", "constraint": null, "alias": null, "createMetric": null}], "aggTime": "none", "timePartType": null}, "XSJBSJZLB": {"id": 21, "name": "XSJBSJZLB", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.XSJBSJZLB", "identifiers": [{"name": "XH", "type": "primary"}, {"name": "HYZKM", "type": "foreign"}, {"name": "XBM", "type": "foreign"}, {"name": "JGM", "type": "foreign"}, {"name": "ZZMMM", "type": "foreign"}, {"name": "XXM", "type": "foreign"}, {"name": "MZM", "type": "foreign"}], "dimensions": [{"name": "YWXM", "owners": null, "type": null, "expr": "YWXM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YWXM", "defaultValues": null, "ext": null}, {"name": "XMPY", "owners": null, "type": null, "expr": "XMPY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XMPY", "defaultValues": null, "ext": null}, {"name": "TC", "owners": null, "type": null, "expr": "TC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TC", "defaultValues": null, "ext": null}, {"name": "AH", "owners": null, "type": null, "expr": "AH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "AH", "defaultValues": null, "ext": null}, {"name": "YHKH", "owners": null, "type": null, "expr": "YHKH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YHKH", "defaultValues": null, "ext": null}, {"name": "CSRQ", "owners": null, "type": null, "expr": "CSRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CSRQ", "defaultValues": null, "ext": null}, {"name": "SFZJH", "owners": null, "type": null, "expr": "SFZJH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZJH", "defaultValues": null, "ext": null}, {"name": "NJ", "owners": null, "type": null, "expr": "NJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "NJ", "defaultValues": null, "ext": null}, {"name": "RXNJ", "owners": null, "type": null, "expr": "RXNJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "RXNJ", "defaultValues": null, "ext": null}, {"name": "SFZX", "owners": null, "type": null, "expr": "SFZX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZX", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "ZYXXSJLB": {"id": 22, "name": "ZYXXSJLB", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.ZYXXSJLB", "identifiers": [{"name": "BZKZYM", "type": "primary"}, {"name": "XWDM", "type": "foreign"}], "dimensions": [{"name": "ZYH", "owners": null, "type": null, "expr": "ZYH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYH", "defaultValues": null, "ext": null}, {"name": "ZYMC", "owners": null, "type": null, "expr": "ZYMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYMC", "defaultValues": null, "ext": null}, {"name": "ZYJC", "owners": null, "type": null, "expr": "ZYJC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYJC", "defaultValues": null, "ext": null}, {"name": "ZYYWMC", "owners": null, "type": null, "expr": "ZYYWMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYYWMC", "defaultValues": null, "ext": null}, {"name": "XZ", "owners": null, "type": null, "expr": "XZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XZ", "defaultValues": null, "ext": null}, {"name": "JLNY", "owners": null, "type": null, "expr": "JLNY", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "JLNY", "defaultValues": null, "ext": null}, {"name": "SCZSRQ", "owners": null, "type": null, "expr": "SCZSRQ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "SCZSRQ", "defaultValues": null, "ext": null}, {"name": "SFZDZY", "owners": null, "type": null, "expr": "SFZDZY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZDZY", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "TSGMJTGXX": {"id": 33, "name": "TSGMJTGXX", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.TSGMJTGXX", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "ZJMC", "owners": null, "type": null, "expr": "ZJMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZJMC", "defaultValues": null, "ext": null}, {"name": "TSG", "owners": null, "type": null, "expr": "TSG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TSG", "defaultValues": null, "ext": null}, {"name": "FX", "owners": null, "type": null, "expr": "FX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FX", "defaultValues": null, "ext": null}, {"name": "TGSJ", "owners": null, "type": null, "expr": "TGSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "TGSJ", "defaultValues": null, "ext": null}, {"name": "SKFS", "owners": null, "type": null, "expr": "SKFS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SKFS", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "XSCJXXB": {"id": 56, "name": "XSCJXXB", "modelId": 2, "type": "oracle", "sqlQuery": "select t1.XH,t1.<PERSON>NX<PERSON>,t1.<PERSON><PERSON><PERSON><PERSON>,t2.<PERSON><PERSON>,t2.X<PERSON>XF,t2.ZXS KCZXS,t2.MZXS KCMZXS,t2.KCFZR,t3.JD,t3.<PERSON><PERSON>J,t3.<PERSON><PERSON>J,t3.<PERSON><PERSON><PERSON>,t3.PJXFJD,t3.<PERSON><PERSON><PERSON><PERSON><PERSON>,t3.FSLKSCJ,t3.KSRQ,t3.SFJG\r\nfrom kust.XKSJLB t1\r\njoin kust.KCSJLB t2 on t1.KCH = t2.KCH\r\njoin kust.CJZLB t3 on t1.XH = t3.XH and t1.KCH = t3.KCH", "tableQuery": "", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "XNXQ", "owners": null, "type": null, "expr": "XNXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XNXQ", "defaultValues": null, "ext": null}, {"name": "SFCXS", "owners": null, "type": null, "expr": "SFCXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFCXS", "defaultValues": null, "ext": null}, {"name": "KCMC", "owners": null, "type": null, "expr": "KCMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCMC", "defaultValues": null, "ext": null}, {"name": "KCXF", "owners": null, "type": null, "expr": "KCXF", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCXF", "defaultValues": null, "ext": null}, {"name": "KCZXS", "owners": null, "type": null, "expr": "KCZXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCZXS", "defaultValues": null, "ext": null}, {"name": "KCMZXS", "owners": null, "type": null, "expr": "KCMZXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCMZXS", "defaultValues": null, "ext": null}, {"name": "KCFZR", "owners": null, "type": null, "expr": "KCFZR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCFZR", "defaultValues": null, "ext": null}, {"name": "JD", "owners": null, "type": null, "expr": "JD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JD", "defaultValues": null, "ext": null}, {"name": "PSCJ", "owners": null, "type": null, "expr": "PSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "PSCJ", "defaultValues": null, "ext": null}, {"name": "KCCJ", "owners": null, "type": null, "expr": "KCCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCCJ", "defaultValues": null, "ext": null}, {"name": "ZCJ", "owners": null, "type": null, "expr": "ZCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZCJ", "defaultValues": null, "ext": null}, {"name": "PJXFJD", "owners": null, "type": null, "expr": "PJXFJD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "PJXFJD", "defaultValues": null, "ext": null}, {"name": "DJLKSCJ", "owners": null, "type": null, "expr": "DJLKSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DJLKSCJ", "defaultValues": null, "ext": null}, {"name": "FSLKSCJ", "owners": null, "type": null, "expr": "FSLKSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FSLKSCJ", "defaultValues": null, "ext": null}, {"name": "KSRQ", "owners": null, "type": null, "expr": "KSRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KSRQ", "defaultValues": null, "ext": null}, {"name": "SFJG", "owners": null, "type": null, "expr": "SFJG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFJG", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "XSHDQDQKB": {"id": 55, "name": "XSHDQDQKB", "modelId": 2, "type": "oracle", "sqlQuery": "select t1.XH,t1.QDSJ,t1.QDJG,t2.BT,t2.NR,t1.FZMC\r\nfrom kust.XSQDXX t1\r\njoin kust.QDNRFBXX t2 on t1.QDID = t2.WYBS", "tableQuery": "", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "QDSJ", "owners": null, "type": null, "expr": "QDSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "QDSJ", "defaultValues": null, "ext": null}, {"name": "QDJG", "owners": null, "type": null, "expr": "QDJG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QDJG", "defaultValues": null, "ext": null}, {"name": "BT", "owners": null, "type": null, "expr": "BT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BT", "defaultValues": null, "ext": null}, {"name": "NR", "owners": null, "type": null, "expr": "NR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "NR", "defaultValues": null, "ext": null}, {"name": "FZMC", "owners": null, "type": null, "expr": "FZMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FZMC", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}, "XSXFXXVIEW": {"id": 57, "name": "XSXFXXVIEW", "modelId": 2, "type": "oracle", "sqlQuery": "select\r\n    t1.XGH XH,t1.<PERSON><PERSON><PERSON>,t1.<PERSON><PERSON><PERSON><PERSON>,t1.KH,t1.<PERSON>N<PERSON><PERSON>,t1.ZHKHRQ,t1.ZHYXRQ,t1.ZHZT,\r\n\t\tt2.ZDZH,t2.JYJE,t2.RZRQSJ,t2.Y<PERSON><PERSON>,\r\n\t\tt3.DZ,t3.SHMC\r\nfrom YKTZHXX t1\r\njoin YKTXFXX t2 on t1.ZH = t2.ZH\r\njoin YKTSHXX t3 on t2.ZDZH = t3.ZH", "tableQuery": "", "identifiers": [{"name": "XH", "type": "primary"}, {"name": "ZJH", "type": "primary"}], "dimensions": [{"name": "KH", "owners": null, "type": null, "expr": "KH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KH", "defaultValues": null, "ext": null}, {"name": "ZHKHRQ", "owners": null, "type": null, "expr": "ZHKHRQ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "ZHKHRQ", "defaultValues": null, "ext": null}, {"name": "ZHYXRQ", "owners": null, "type": null, "expr": "ZHYXRQ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "ZHYXRQ", "defaultValues": null, "ext": null}, {"name": "ZHZT", "owners": null, "type": null, "expr": "ZHZT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZHZT", "defaultValues": null, "ext": null}, {"name": "RZRQSJ", "owners": null, "type": null, "expr": "RZRQSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "RZRQSJ", "defaultValues": null, "ext": null}, {"name": "YKCS", "owners": null, "type": null, "expr": "YKCS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YKCS", "defaultValues": null, "ext": null}, {"name": "DZ", "owners": null, "type": null, "expr": "DZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DZ", "defaultValues": null, "ext": null}, {"name": "SHMC", "owners": null, "type": null, "expr": "SHMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SHMC", "defaultValues": null, "ext": null}], "measures": [{"name": "XSXFXXVIEW_ZHYE", "agg": "sum", "expr": "ZHYE", "constraint": null, "alias": null, "createMetric": null}, {"name": "XSXFXXVIEW_KNYE", "agg": "sum", "expr": "KNYE", "constraint": null, "alias": null, "createMetric": null}, {"name": "XSXFXXVIEW_JYJE", "agg": "sum", "expr": "JYJE", "constraint": null, "alias": null, "createMetric": null}], "aggTime": "none", "timePartType": null}, "QJXX": {"id": 36, "name": "QJXX", "modelId": 2, "type": "oracle", "sqlQuery": "", "tableQuery": "KUST.QJXX", "identifiers": [{"name": "XH", "type": "primary"}], "dimensions": [{"name": "KSSJ", "owners": null, "type": null, "expr": "KSSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "KSSJ", "defaultValues": null, "ext": null}, {"name": "JJLXRDH", "owners": null, "type": null, "expr": "JJLXRDH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JJLXRDH", "defaultValues": null, "ext": null}, {"name": "XJSJ", "owners": null, "type": null, "expr": "XJSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XJSJ", "defaultValues": null, "ext": null}, {"name": "DXJRXM", "owners": null, "type": null, "expr": "DXJRXM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DXJRXM", "defaultValues": null, "ext": null}, {"name": "SFLX", "owners": null, "type": null, "expr": "SFLX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFLX", "defaultValues": null, "ext": null}, {"name": "JSSJ", "owners": null, "type": null, "expr": "JSSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "JSSJ", "defaultValues": null, "ext": null}, {"name": "QJSJ", "owners": null, "type": null, "expr": "QJSJ", "dimensionTimeTypeParams": {"isPrimary": "true", "timeGranularity": "day"}, "dataType": "UNKNOWN", "bizName": "QJSJ", "defaultValues": null, "ext": null}, {"name": "QJZT", "owners": null, "type": null, "expr": "QJZT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJZT", "defaultValues": null, "ext": null}, {"name": "QJNR", "owners": null, "type": null, "expr": "QJNR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJNR", "defaultValues": null, "ext": null}, {"name": "QJLX", "owners": null, "type": null, "expr": "QJLX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJLX", "defaultValues": null, "ext": null}], "measures": [], "aggTime": "none", "timePartType": null}}, "dimensionMap": {"SQ_MJXX": [{"name": "MJJCFX", "owners": "admin", "type": null, "expr": "JCFX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "MJJCFX", "defaultValues": null, "ext": {}}, {"name": "NJDQBZ", "owners": "admin", "type": null, "expr": "DQBZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "NJDQBZ", "defaultValues": null, "ext": {}}, {"name": "MJWJFS", "owners": "admin", "type": null, "expr": "WJFS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "MJWJFS", "defaultValues": null, "ext": {}}], "XSCCZLB": [{"name": "WJJK", "owners": "admin", "type": null, "expr": "WJJK", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WJJK", "defaultValues": null, "ext": {}}, {"name": "CFZTM", "owners": "admin", "type": null, "expr": "CFZTM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFZTM", "defaultValues": null, "ext": {}}, {"name": "CLBM", "owners": "admin", "type": null, "expr": "CLBM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CLBM", "defaultValues": null, "ext": {}}, {"name": "CFMCM", "owners": "admin", "type": null, "expr": "CFMCM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFMCM", "defaultValues": null, "ext": {}}, {"name": "CFYY", "owners": "admin", "type": null, "expr": "CFYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFYY", "defaultValues": null, "ext": {}}, {"name": "CFGYR", "owners": "admin", "type": null, "expr": "CFGYR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFGYR", "defaultValues": null, "ext": {}}, {"name": "WJRQ", "owners": "admin", "type": null, "expr": "WJRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WJRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "CFCXRQ", "owners": "admin", "type": null, "expr": "CFCXRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFCXRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "CFRQ", "owners": "admin", "type": null, "expr": "CFRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}], "ZHCPCJXX": [{"name": "XQ", "owners": "admin", "type": null, "expr": "XQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XQ", "defaultValues": null, "ext": {}}, {"name": "BJPM", "owners": "admin", "type": null, "expr": "BJPM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BJPM", "defaultValues": null, "ext": {}}, {"name": "ZYPM", "owners": "admin", "type": null, "expr": "ZYPM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYPM", "defaultValues": null, "ext": {}}], "ZHCPZBCJB": [{"name": "CPLXMC", "owners": "admin", "type": null, "expr": "CPLXMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CPLXMC", "defaultValues": null, "ext": {}}, {"name": "XNXQ", "owners": "admin", "type": null, "expr": "XNXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XNXQ", "defaultValues": null, "ext": {}}], "DMKJ": [], "XSTSJYXXB": [{"name": "TSMC", "owners": "admin", "type": null, "expr": "TSMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TSMC", "defaultValues": null, "ext": {}}, {"name": "HSD", "owners": "admin", "type": null, "expr": "HSD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HSD", "defaultValues": null, "ext": {}}, {"name": "JSD", "owners": "admin", "type": null, "expr": "JSD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JSD", "defaultValues": null, "ext": {}}, {"name": "JYSJ", "owners": "admin", "type": null, "expr": "JYSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JYSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SJGHSJ", "owners": "admin", "type": null, "expr": "SJGHSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SJGHSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}], "XJYDSJL": [{"name": "YDLXMC", "owners": "admin", "type": null, "expr": "YDLXMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YDLXMC", "defaultValues": null, "ext": {}}, {"name": "YDYY", "owners": "admin", "type": null, "expr": "YDYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YDYY", "defaultValues": null, "ext": {}}, {"name": "YDSJ", "owners": "admin", "type": null, "expr": "YDSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YDSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "FIELDS_TEXT", "owners": "admin", "type": null, "expr": "FIELDS_TEXT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FIELDS_TEXT", "defaultValues": null, "ext": {}}], "XSSFXX": [{"name": "SFXMMC", "owners": "admin", "type": null, "expr": "SFXMMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFXMMC", "defaultValues": null, "ext": {}}, {"name": "SFQJMC", "owners": "admin", "type": null, "expr": "SFQJMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFQJMC", "defaultValues": null, "ext": {}}], "XSJBXX": [{"name": "SZBJ", "owners": "admin", "type": null, "expr": "SZBJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SZBJ", "defaultValues": null, "ext": {}}, {"name": "BZR", "owners": "admin", "type": null, "expr": "BZR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BZR", "defaultValues": null, "ext": {}}, {"name": "JTDH", "owners": "admin", "type": null, "expr": "JTDH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTDH", "defaultValues": null, "ext": {}}, {"name": "SZYX", "owners": "admin", "type": null, "expr": "SZYX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SZYX", "defaultValues": null, "ext": {}}, {"name": "JTHKSZD", "owners": "admin", "type": null, "expr": "JTHKSZD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTHKSZD", "defaultValues": null, "ext": {}}, {"name": "RXSJ", "owners": "admin", "type": null, "expr": "RXSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "RXSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SFZH", "owners": "admin", "type": null, "expr": "SFZH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZH", "defaultValues": null, "ext": {}}, {"name": "XM", "owners": "admin", "type": null, "expr": "XM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XM", "defaultValues": null, "ext": {}}, {"name": "BYSJ", "owners": "admin", "type": null, "expr": "BYSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BYSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SFSC", "owners": "admin", "type": null, "expr": "SFSC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFSC", "defaultValues": null, "ext": {}}, {"name": "JTSZD", "owners": "admin", "type": null, "expr": "JTSZD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTSZD", "defaultValues": null, "ext": {}}, {"name": "JTDZ", "owners": "admin", "type": null, "expr": "JTDZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTDZ", "defaultValues": null, "ext": {}}, {"name": "ZXF", "owners": "admin", "type": null, "expr": "ZXF", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZXF", "defaultValues": null, "ext": {}}, {"name": "XH", "owners": "admin", "type": null, "expr": "XH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XH", "defaultValues": null, "ext": {}}, {"name": "XLDM", "owners": "admin", "type": null, "expr": "XLDM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XLDM", "defaultValues": null, "ext": {}}, {"name": "ZYDM", "owners": "admin", "type": null, "expr": "ZYDM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYDM", "defaultValues": null, "ext": {}}], "SSWGWGXX": [{"name": "WGWJWJFS", "owners": "admin", "type": null, "expr": "WJFS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WGWJWJFS", "defaultValues": null, "ext": {}}, {"name": "WGWJJCFX", "owners": "admin", "type": null, "expr": "JCFX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WGWJJCFX", "defaultValues": null, "ext": {}}, {"name": "WGWGYGSSJ", "owners": "admin", "type": null, "expr": "YGSSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WGWGYGSSJ", "defaultValues": null, "ext": {}}, {"name": "WGWGJCSJ", "owners": "admin", "type": null, "expr": "JCSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WGWGJCSJ", "defaultValues": null, "ext": {}}], "XSJLZLB": [{"name": "JLMC", "owners": "admin", "type": null, "expr": "JLMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLMC", "defaultValues": null, "ext": {}}, {"name": "HJSJ", "owners": "admin", "type": null, "expr": "HJSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HJSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "HJXQ", "owners": "admin", "type": null, "expr": "HJXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HJXQ", "defaultValues": null, "ext": {}}, {"name": "BJDW", "owners": "admin", "type": null, "expr": "BJDW", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BJDW", "defaultValues": null, "ext": {}}, {"name": "JLYY", "owners": "admin", "type": null, "expr": "JLYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLYY", "defaultValues": null, "ext": {}}, {"name": "HJXND", "owners": "admin", "type": null, "expr": "HJXND", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HJXND", "defaultValues": null, "ext": {}}, {"name": "JLJBM", "owners": "admin", "type": null, "expr": "JLJBM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLJBM", "defaultValues": null, "ext": {}}, {"name": "JLDJM", "owners": "admin", "type": null, "expr": "JLDJM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLDJM", "defaultValues": null, "ext": {}}], "XSJBSJZLB": [{"name": "YWXM", "owners": "admin", "type": null, "expr": "YWXM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YWXM", "defaultValues": null, "ext": {}}, {"name": "XMPY", "owners": "admin", "type": null, "expr": "XMPY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XMPY", "defaultValues": null, "ext": {}}, {"name": "TC", "owners": "admin", "type": null, "expr": "TC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TC", "defaultValues": null, "ext": {}}, {"name": "AH", "owners": "admin", "type": null, "expr": "AH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "AH", "defaultValues": null, "ext": {}}, {"name": "YHKH", "owners": "admin", "type": null, "expr": "YHKH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YHKH", "defaultValues": null, "ext": {}}, {"name": "CSRQ", "owners": "admin", "type": null, "expr": "CSRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CSRQ", "defaultValues": null, "ext": {}}, {"name": "NJ", "owners": "admin", "type": null, "expr": "NJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "NJ", "defaultValues": null, "ext": {}}, {"name": "RXNJ", "owners": "admin", "type": null, "expr": "RXNJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "RXNJ", "defaultValues": null, "ext": {}}, {"name": "SFZX", "owners": "admin", "type": null, "expr": "SFZX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZX", "defaultValues": null, "ext": {}}, {"name": "HYZKM", "owners": "admin", "type": null, "expr": "HYZKM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HYZKM", "defaultValues": null, "ext": {}}, {"name": "XBM", "owners": "admin", "type": null, "expr": "XBM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XBM", "defaultValues": null, "ext": {}}, {"name": "JGM", "owners": "admin", "type": null, "expr": "JGM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JGM", "defaultValues": null, "ext": {}}, {"name": "ZZMMM", "owners": "admin", "type": null, "expr": "ZZMMM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZZMMM", "defaultValues": null, "ext": {}}, {"name": "XXM", "owners": "admin", "type": null, "expr": "XXM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XXM", "defaultValues": null, "ext": {}}, {"name": "MZM", "owners": "admin", "type": null, "expr": "MZM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "MZM", "defaultValues": null, "ext": {}}], "ZYXXSJLB": [{"name": "ZYH", "owners": "admin", "type": null, "expr": "ZYH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYH", "defaultValues": null, "ext": {}}, {"name": "ZYMC", "owners": "admin", "type": null, "expr": "ZYMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYMC", "defaultValues": null, "ext": {}}, {"name": "ZYJC", "owners": "admin", "type": null, "expr": "ZYJC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYJC", "defaultValues": null, "ext": {}}, {"name": "ZYYWMC", "owners": "admin", "type": null, "expr": "ZYYWMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYYWMC", "defaultValues": null, "ext": {}}, {"name": "JLNY", "owners": "admin", "type": null, "expr": "JLNY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLNY", "defaultValues": null, "ext": {"time_format": "yyyy-MM"}}, {"name": "SCZSRQ", "owners": "admin", "type": null, "expr": "SCZSRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SCZSRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SFZDZY", "owners": "admin", "type": null, "expr": "SFZDZY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZDZY", "defaultValues": null, "ext": {}}, {"name": "BZKZYM", "owners": "admin", "type": null, "expr": "BZKZYM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BZKZYM", "defaultValues": null, "ext": {}}, {"name": "XWDM", "owners": "admin", "type": null, "expr": "XWDM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XWDM", "defaultValues": null, "ext": {}}], "TSGMJTGXX": [{"name": "ZJMC", "owners": "admin", "type": null, "expr": "ZJMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZJMC", "defaultValues": null, "ext": {}}, {"name": "TSG", "owners": "admin", "type": null, "expr": "TSG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TSG", "defaultValues": null, "ext": {}}, {"name": "FX", "owners": "admin", "type": null, "expr": "FX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FX", "defaultValues": null, "ext": {}}, {"name": "TGSJ", "owners": "admin", "type": null, "expr": "TGSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TGSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SKFS", "owners": "admin", "type": null, "expr": "SKFS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SKFS", "defaultValues": null, "ext": {}}], "XSHDQDQKB": [{"name": "QDSJ", "owners": "admin", "type": null, "expr": "QDSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QDSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "QDJG", "owners": "admin", "type": null, "expr": "QDJG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QDJG", "defaultValues": null, "ext": {}}, {"name": "BT", "owners": "admin", "type": null, "expr": "BT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BT", "defaultValues": null, "ext": {}}, {"name": "NR", "owners": "admin", "type": null, "expr": "NR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "NR", "defaultValues": null, "ext": {}}, {"name": "FZMC", "owners": "admin", "type": null, "expr": "FZMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FZMC", "defaultValues": null, "ext": {}}], "XSCJXXB": [{"name": "XNXQ", "owners": "admin", "type": null, "expr": "XNXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XNXQ", "defaultValues": null, "ext": {}}, {"name": "SFCXS", "owners": "admin", "type": null, "expr": "SFCXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFCXS", "defaultValues": null, "ext": {}}, {"name": "KCMC", "owners": "admin", "type": null, "expr": "KCMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCMC", "defaultValues": null, "ext": {}}, {"name": "KCXF", "owners": "admin", "type": null, "expr": "KCXF", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCXF", "defaultValues": null, "ext": {}}, {"name": "KCZXS", "owners": "admin", "type": null, "expr": "KCZXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCZXS", "defaultValues": null, "ext": {}}, {"name": "KCMZXS", "owners": "admin", "type": null, "expr": "KCMZXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCMZXS", "defaultValues": null, "ext": {}}, {"name": "KCFZR", "owners": "admin", "type": null, "expr": "KCFZR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCFZR", "defaultValues": null, "ext": {}}, {"name": "JD", "owners": "admin", "type": null, "expr": "JD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JD", "defaultValues": null, "ext": {}}, {"name": "PSCJ", "owners": "admin", "type": null, "expr": "PSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "PSCJ", "defaultValues": null, "ext": {}}, {"name": "KCCJ", "owners": "admin", "type": null, "expr": "KCCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCCJ", "defaultValues": null, "ext": {}}, {"name": "ZCJ", "owners": "admin", "type": null, "expr": "ZCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZCJ", "defaultValues": null, "ext": {}}, {"name": "PJXFJD", "owners": "admin", "type": null, "expr": "PJXFJD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "PJXFJD", "defaultValues": null, "ext": {}}, {"name": "DJLKSCJ", "owners": "admin", "type": null, "expr": "DJLKSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DJLKSCJ", "defaultValues": null, "ext": {}}, {"name": "FSLKSCJ", "owners": "admin", "type": null, "expr": "FSLKSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FSLKSCJ", "defaultValues": null, "ext": {}}, {"name": "KSRQ", "owners": "admin", "type": null, "expr": "KSRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KSRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SFJG", "owners": "admin", "type": null, "expr": "SFJG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFJG", "defaultValues": null, "ext": {}}], "XSXFXXVIEW": [{"name": "KH", "owners": "admin", "type": null, "expr": "KH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KH", "defaultValues": null, "ext": {}}, {"name": "ZHKHRQ", "owners": "admin", "type": null, "expr": "ZHKHRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZHKHRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "ZHYXRQ", "owners": "admin", "type": null, "expr": "ZHYXRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZHYXRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "ZHZT", "owners": "admin", "type": null, "expr": "ZHZT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZHZT", "defaultValues": null, "ext": {}}, {"name": "RZRQSJ", "owners": "admin", "type": null, "expr": "RZRQSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "RZRQSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd HH:mm"}}, {"name": "YKCS", "owners": "admin", "type": null, "expr": "YKCS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YKCS", "defaultValues": null, "ext": {}}, {"name": "DZ", "owners": "admin", "type": null, "expr": "DZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DZ", "defaultValues": null, "ext": {}}, {"name": "SHMC", "owners": "admin", "type": null, "expr": "SHMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SHMC", "defaultValues": null, "ext": {}}], "QJXX": [{"name": "KSSJ", "owners": "admin", "type": null, "expr": "KSSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KSSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "JJLXRDH", "owners": "admin", "type": null, "expr": "JJLXRDH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JJLXRDH", "defaultValues": null, "ext": {}}, {"name": "XJSJ", "owners": "admin", "type": null, "expr": "XJSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XJSJ", "defaultValues": null, "ext": {}}, {"name": "DXJRXM", "owners": "admin", "type": null, "expr": "DXJRXM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DXJRXM", "defaultValues": null, "ext": {}}, {"name": "SFLX", "owners": "admin", "type": null, "expr": "SFLX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFLX", "defaultValues": null, "ext": {}}, {"name": "JSSJ", "owners": "admin", "type": null, "expr": "JSSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JSSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "QJSJ", "owners": "admin", "type": null, "expr": "QJSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "QJZT", "owners": "admin", "type": null, "expr": "QJZT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJZT", "defaultValues": null, "ext": {}}, {"name": "QJNR", "owners": "admin", "type": null, "expr": "QJNR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJNR", "defaultValues": null, "ext": {}}, {"name": "QJLX", "owners": "admin", "type": null, "expr": "QJLX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJLX", "defaultValues": null, "ext": {}}]}, "materializationList": [], "joinRelations": [{"id": 14, "left": "XSJBXX", "right": "XSJBSJZLB", "joinType": "inner join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 15, "left": "XSJBXX", "right": "ZYXXSJLB", "joinType": "left join", "joinCondition": [{"left": "ZYDM", "middle": "=", "right": "BZKZYM"}]}, {"id": 19, "left": "XSJBXX", "right": "XSSFXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 20, "left": "XSJBXX", "right": "XSCCZLB", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 24, "left": "XSJBXX", "right": "SSWGWGXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 25, "left": "XSJBXX", "right": "SQ_MJXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 26, "left": "XSJBXX", "right": "TSGMJTGXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 29, "left": "XSJBXX", "right": "QJXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 30, "left": "XSJBXX", "right": "XJYDSJL", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 31, "left": "XSJBXX", "right": "XSJLZLB", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 32, "left": "XSJBXX", "right": "DMKJ", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 33, "left": "XSJBXX", "right": "ZHCPCJXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 34, "left": "XSJBXX", "right": "ZHCPZBCJB", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 38, "left": "QJXX", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 39, "left": "XSJBSJZLB", "right": "XSJBXX", "joinType": "inner join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 40, "left": "ZYXXSJLB", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "BZKZYM", "middle": "=", "right": "ZYDM"}]}, {"id": 47, "left": "XSSFXX", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 48, "left": "XSCCZLB", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 52, "left": "SSWGWGXX", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 53, "left": "SQ_MJXX", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 54, "left": "TSGMJTGXX", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 58, "left": "XJYDSJL", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 59, "left": "XSJLZLB", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 60, "left": "DMKJ", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 61, "left": "ZHCPCJXX", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 62, "left": "ZHCPZBCJB", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 65, "left": "XSTSJYXXB", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 66, "left": "XSHDQDQKB", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 67, "left": "XSCJXXB", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}]}, {"id": 68, "left": "XSXFXXVIEW", "right": "XSJBXX", "joinType": "left join", "joinCondition": [{"left": "XH", "middle": "=", "right": "XH"}, {"left": "ZJH", "middle": "=", "right": "SFZH"}]}], "database": {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-13T07:07:17.000+00:00", "updatedAt": "2024-12-18T03:40:37.000+00:00", "id": 2, "domainId": null, "name": "外网连接昆工oracle数据库", "description": "昆工测试数据库外网连接", "version": null, "url": "*********************************************", "username": "kust", "password": "iR9k8Mv5KzntGU4BdROb0w==", "database": null, "schema": null, "type": "ORACLE", "admins": [], "viewers": []}, "dimensions": [{"name": "MJJCFX", "owners": "admin", "type": null, "expr": "JCFX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "MJJCFX", "defaultValues": null, "ext": {}}, {"name": "NJDQBZ", "owners": "admin", "type": null, "expr": "DQBZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "NJDQBZ", "defaultValues": null, "ext": {}}, {"name": "MJWJFS", "owners": "admin", "type": null, "expr": "WJFS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "MJWJFS", "defaultValues": null, "ext": {}}, {"name": "WJJK", "owners": "admin", "type": null, "expr": "WJJK", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WJJK", "defaultValues": null, "ext": {}}, {"name": "CFZTM", "owners": "admin", "type": null, "expr": "CFZTM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFZTM", "defaultValues": null, "ext": {}}, {"name": "CLBM", "owners": "admin", "type": null, "expr": "CLBM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CLBM", "defaultValues": null, "ext": {}}, {"name": "CFMCM", "owners": "admin", "type": null, "expr": "CFMCM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFMCM", "defaultValues": null, "ext": {}}, {"name": "CFYY", "owners": "admin", "type": null, "expr": "CFYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFYY", "defaultValues": null, "ext": {}}, {"name": "CFGYR", "owners": "admin", "type": null, "expr": "CFGYR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFGYR", "defaultValues": null, "ext": {}}, {"name": "WJRQ", "owners": "admin", "type": null, "expr": "WJRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WJRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "CFCXRQ", "owners": "admin", "type": null, "expr": "CFCXRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFCXRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "CFRQ", "owners": "admin", "type": null, "expr": "CFRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CFRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "XQ", "owners": "admin", "type": null, "expr": "XQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XQ", "defaultValues": null, "ext": {}}, {"name": "BJPM", "owners": "admin", "type": null, "expr": "BJPM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BJPM", "defaultValues": null, "ext": {}}, {"name": "ZYPM", "owners": "admin", "type": null, "expr": "ZYPM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYPM", "defaultValues": null, "ext": {}}, {"name": "CPLXMC", "owners": "admin", "type": null, "expr": "CPLXMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CPLXMC", "defaultValues": null, "ext": {}}, {"name": "XNXQ", "owners": "admin", "type": null, "expr": "XNXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XNXQ", "defaultValues": null, "ext": {}}, {"name": "TSMC", "owners": "admin", "type": null, "expr": "TSMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TSMC", "defaultValues": null, "ext": {}}, {"name": "HSD", "owners": "admin", "type": null, "expr": "HSD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HSD", "defaultValues": null, "ext": {}}, {"name": "JSD", "owners": "admin", "type": null, "expr": "JSD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JSD", "defaultValues": null, "ext": {}}, {"name": "JYSJ", "owners": "admin", "type": null, "expr": "JYSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JYSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SJGHSJ", "owners": "admin", "type": null, "expr": "SJGHSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SJGHSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "YDLXMC", "owners": "admin", "type": null, "expr": "YDLXMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YDLXMC", "defaultValues": null, "ext": {}}, {"name": "YDYY", "owners": "admin", "type": null, "expr": "YDYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YDYY", "defaultValues": null, "ext": {}}, {"name": "YDSJ", "owners": "admin", "type": null, "expr": "YDSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YDSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "FIELDS_TEXT", "owners": "admin", "type": null, "expr": "FIELDS_TEXT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FIELDS_TEXT", "defaultValues": null, "ext": {}}, {"name": "SFXMMC", "owners": "admin", "type": null, "expr": "SFXMMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFXMMC", "defaultValues": null, "ext": {}}, {"name": "SFQJMC", "owners": "admin", "type": null, "expr": "SFQJMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFQJMC", "defaultValues": null, "ext": {}}, {"name": "SZBJ", "owners": "admin", "type": null, "expr": "SZBJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SZBJ", "defaultValues": null, "ext": {}}, {"name": "BZR", "owners": "admin", "type": null, "expr": "BZR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BZR", "defaultValues": null, "ext": {}}, {"name": "JTDH", "owners": "admin", "type": null, "expr": "JTDH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTDH", "defaultValues": null, "ext": {}}, {"name": "SZYX", "owners": "admin", "type": null, "expr": "SZYX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SZYX", "defaultValues": null, "ext": {}}, {"name": "JTHKSZD", "owners": "admin", "type": null, "expr": "JTHKSZD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTHKSZD", "defaultValues": null, "ext": {}}, {"name": "RXSJ", "owners": "admin", "type": null, "expr": "RXSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "RXSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SFZH", "owners": "admin", "type": null, "expr": "SFZH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZH", "defaultValues": null, "ext": {}}, {"name": "XM", "owners": "admin", "type": null, "expr": "XM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XM", "defaultValues": null, "ext": {}}, {"name": "BYSJ", "owners": "admin", "type": null, "expr": "BYSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BYSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SFSC", "owners": "admin", "type": null, "expr": "SFSC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFSC", "defaultValues": null, "ext": {}}, {"name": "JTSZD", "owners": "admin", "type": null, "expr": "JTSZD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTSZD", "defaultValues": null, "ext": {}}, {"name": "JTDZ", "owners": "admin", "type": null, "expr": "JTDZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JTDZ", "defaultValues": null, "ext": {}}, {"name": "ZXF", "owners": "admin", "type": null, "expr": "ZXF", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZXF", "defaultValues": null, "ext": {}}, {"name": "XH", "owners": "admin", "type": null, "expr": "XH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XH", "defaultValues": null, "ext": {}}, {"name": "XLDM", "owners": "admin", "type": null, "expr": "XLDM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XLDM", "defaultValues": null, "ext": {}}, {"name": "ZYDM", "owners": "admin", "type": null, "expr": "ZYDM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYDM", "defaultValues": null, "ext": {}}, {"name": "WGWJWJFS", "owners": "admin", "type": null, "expr": "WJFS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WGWJWJFS", "defaultValues": null, "ext": {}}, {"name": "WGWJJCFX", "owners": "admin", "type": null, "expr": "JCFX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WGWJJCFX", "defaultValues": null, "ext": {}}, {"name": "WGWGYGSSJ", "owners": "admin", "type": null, "expr": "YGSSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WGWGYGSSJ", "defaultValues": null, "ext": {}}, {"name": "WGWGJCSJ", "owners": "admin", "type": null, "expr": "JCSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "WGWGJCSJ", "defaultValues": null, "ext": {}}, {"name": "JLMC", "owners": "admin", "type": null, "expr": "JLMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLMC", "defaultValues": null, "ext": {}}, {"name": "HJSJ", "owners": "admin", "type": null, "expr": "HJSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HJSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "HJXQ", "owners": "admin", "type": null, "expr": "HJXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HJXQ", "defaultValues": null, "ext": {}}, {"name": "BJDW", "owners": "admin", "type": null, "expr": "BJDW", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BJDW", "defaultValues": null, "ext": {}}, {"name": "JLYY", "owners": "admin", "type": null, "expr": "JLYY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLYY", "defaultValues": null, "ext": {}}, {"name": "HJXND", "owners": "admin", "type": null, "expr": "HJXND", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HJXND", "defaultValues": null, "ext": {}}, {"name": "JLJBM", "owners": "admin", "type": null, "expr": "JLJBM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLJBM", "defaultValues": null, "ext": {}}, {"name": "JLDJM", "owners": "admin", "type": null, "expr": "JLDJM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLDJM", "defaultValues": null, "ext": {}}, {"name": "YWXM", "owners": "admin", "type": null, "expr": "YWXM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YWXM", "defaultValues": null, "ext": {}}, {"name": "XMPY", "owners": "admin", "type": null, "expr": "XMPY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XMPY", "defaultValues": null, "ext": {}}, {"name": "TC", "owners": "admin", "type": null, "expr": "TC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TC", "defaultValues": null, "ext": {}}, {"name": "AH", "owners": "admin", "type": null, "expr": "AH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "AH", "defaultValues": null, "ext": {}}, {"name": "YHKH", "owners": "admin", "type": null, "expr": "YHKH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YHKH", "defaultValues": null, "ext": {}}, {"name": "CSRQ", "owners": "admin", "type": null, "expr": "CSRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "CSRQ", "defaultValues": null, "ext": {}}, {"name": "NJ", "owners": "admin", "type": null, "expr": "NJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "NJ", "defaultValues": null, "ext": {}}, {"name": "RXNJ", "owners": "admin", "type": null, "expr": "RXNJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "RXNJ", "defaultValues": null, "ext": {}}, {"name": "SFZX", "owners": "admin", "type": null, "expr": "SFZX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZX", "defaultValues": null, "ext": {}}, {"name": "HYZKM", "owners": "admin", "type": null, "expr": "HYZKM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "HYZKM", "defaultValues": null, "ext": {}}, {"name": "XBM", "owners": "admin", "type": null, "expr": "XBM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XBM", "defaultValues": null, "ext": {}}, {"name": "JGM", "owners": "admin", "type": null, "expr": "JGM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JGM", "defaultValues": null, "ext": {}}, {"name": "ZZMMM", "owners": "admin", "type": null, "expr": "ZZMMM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZZMMM", "defaultValues": null, "ext": {}}, {"name": "XXM", "owners": "admin", "type": null, "expr": "XXM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XXM", "defaultValues": null, "ext": {}}, {"name": "MZM", "owners": "admin", "type": null, "expr": "MZM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "MZM", "defaultValues": null, "ext": {}}, {"name": "ZYH", "owners": "admin", "type": null, "expr": "ZYH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYH", "defaultValues": null, "ext": {}}, {"name": "ZYMC", "owners": "admin", "type": null, "expr": "ZYMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYMC", "defaultValues": null, "ext": {}}, {"name": "ZYJC", "owners": "admin", "type": null, "expr": "ZYJC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYJC", "defaultValues": null, "ext": {}}, {"name": "ZYYWMC", "owners": "admin", "type": null, "expr": "ZYYWMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZYYWMC", "defaultValues": null, "ext": {}}, {"name": "JLNY", "owners": "admin", "type": null, "expr": "JLNY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JLNY", "defaultValues": null, "ext": {"time_format": "yyyy-MM"}}, {"name": "SCZSRQ", "owners": "admin", "type": null, "expr": "SCZSRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SCZSRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SFZDZY", "owners": "admin", "type": null, "expr": "SFZDZY", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFZDZY", "defaultValues": null, "ext": {}}, {"name": "BZKZYM", "owners": "admin", "type": null, "expr": "BZKZYM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BZKZYM", "defaultValues": null, "ext": {}}, {"name": "XWDM", "owners": "admin", "type": null, "expr": "XWDM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XWDM", "defaultValues": null, "ext": {}}, {"name": "ZJMC", "owners": "admin", "type": null, "expr": "ZJMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZJMC", "defaultValues": null, "ext": {}}, {"name": "TSG", "owners": "admin", "type": null, "expr": "TSG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TSG", "defaultValues": null, "ext": {}}, {"name": "FX", "owners": "admin", "type": null, "expr": "FX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FX", "defaultValues": null, "ext": {}}, {"name": "TGSJ", "owners": "admin", "type": null, "expr": "TGSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "TGSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SKFS", "owners": "admin", "type": null, "expr": "SKFS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SKFS", "defaultValues": null, "ext": {}}, {"name": "QDSJ", "owners": "admin", "type": null, "expr": "QDSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QDSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "QDJG", "owners": "admin", "type": null, "expr": "QDJG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QDJG", "defaultValues": null, "ext": {}}, {"name": "BT", "owners": "admin", "type": null, "expr": "BT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "BT", "defaultValues": null, "ext": {}}, {"name": "NR", "owners": "admin", "type": null, "expr": "NR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "NR", "defaultValues": null, "ext": {}}, {"name": "FZMC", "owners": "admin", "type": null, "expr": "FZMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FZMC", "defaultValues": null, "ext": {}}, {"name": "XNXQ", "owners": "admin", "type": null, "expr": "XNXQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XNXQ", "defaultValues": null, "ext": {}}, {"name": "SFCXS", "owners": "admin", "type": null, "expr": "SFCXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFCXS", "defaultValues": null, "ext": {}}, {"name": "KCMC", "owners": "admin", "type": null, "expr": "KCMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCMC", "defaultValues": null, "ext": {}}, {"name": "KCXF", "owners": "admin", "type": null, "expr": "KCXF", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCXF", "defaultValues": null, "ext": {}}, {"name": "KCZXS", "owners": "admin", "type": null, "expr": "KCZXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCZXS", "defaultValues": null, "ext": {}}, {"name": "KCMZXS", "owners": "admin", "type": null, "expr": "KCMZXS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCMZXS", "defaultValues": null, "ext": {}}, {"name": "KCFZR", "owners": "admin", "type": null, "expr": "KCFZR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCFZR", "defaultValues": null, "ext": {}}, {"name": "JD", "owners": "admin", "type": null, "expr": "JD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JD", "defaultValues": null, "ext": {}}, {"name": "PSCJ", "owners": "admin", "type": null, "expr": "PSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "PSCJ", "defaultValues": null, "ext": {}}, {"name": "KCCJ", "owners": "admin", "type": null, "expr": "KCCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KCCJ", "defaultValues": null, "ext": {}}, {"name": "ZCJ", "owners": "admin", "type": null, "expr": "ZCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZCJ", "defaultValues": null, "ext": {}}, {"name": "PJXFJD", "owners": "admin", "type": null, "expr": "PJXFJD", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "PJXFJD", "defaultValues": null, "ext": {}}, {"name": "DJLKSCJ", "owners": "admin", "type": null, "expr": "DJLKSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DJLKSCJ", "defaultValues": null, "ext": {}}, {"name": "FSLKSCJ", "owners": "admin", "type": null, "expr": "FSLKSCJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "FSLKSCJ", "defaultValues": null, "ext": {}}, {"name": "KSRQ", "owners": "admin", "type": null, "expr": "KSRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KSRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "SFJG", "owners": "admin", "type": null, "expr": "SFJG", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFJG", "defaultValues": null, "ext": {}}, {"name": "KH", "owners": "admin", "type": null, "expr": "KH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KH", "defaultValues": null, "ext": {}}, {"name": "ZHKHRQ", "owners": "admin", "type": null, "expr": "ZHKHRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZHKHRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "ZHYXRQ", "owners": "admin", "type": null, "expr": "ZHYXRQ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZHYXRQ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "ZHZT", "owners": "admin", "type": null, "expr": "ZHZT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "ZHZT", "defaultValues": null, "ext": {}}, {"name": "RZRQSJ", "owners": "admin", "type": null, "expr": "RZRQSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "RZRQSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd HH:mm"}}, {"name": "YKCS", "owners": "admin", "type": null, "expr": "YKCS", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "YKCS", "defaultValues": null, "ext": {}}, {"name": "DZ", "owners": "admin", "type": null, "expr": "DZ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DZ", "defaultValues": null, "ext": {}}, {"name": "SHMC", "owners": "admin", "type": null, "expr": "SHMC", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SHMC", "defaultValues": null, "ext": {}}, {"name": "KSSJ", "owners": "admin", "type": null, "expr": "KSSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "KSSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "JJLXRDH", "owners": "admin", "type": null, "expr": "JJLXRDH", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JJLXRDH", "defaultValues": null, "ext": {}}, {"name": "XJSJ", "owners": "admin", "type": null, "expr": "XJSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "XJSJ", "defaultValues": null, "ext": {}}, {"name": "DXJRXM", "owners": "admin", "type": null, "expr": "DXJRXM", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "DXJRXM", "defaultValues": null, "ext": {}}, {"name": "SFLX", "owners": "admin", "type": null, "expr": "SFLX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "SFLX", "defaultValues": null, "ext": {}}, {"name": "JSSJ", "owners": "admin", "type": null, "expr": "JSSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "JSSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "QJSJ", "owners": "admin", "type": null, "expr": "QJSJ", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJSJ", "defaultValues": null, "ext": {"time_format": "yyyy-MM-dd"}}, {"name": "QJZT", "owners": "admin", "type": null, "expr": "QJZT", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJZT", "defaultValues": null, "ext": {}}, {"name": "QJNR", "owners": "admin", "type": null, "expr": "QJNR", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJNR", "defaultValues": null, "ext": {}}, {"name": "QJLX", "owners": "admin", "type": null, "expr": "QJLX", "dimensionTimeTypeParams": {"isPrimary": null, "timeGranularity": null}, "dataType": "UNKNOWN", "bizName": "QJLX", "defaultValues": null, "ext": {}}]}, "semanticSchemaResp": {"dataSetId": 9, "modelIds": [20, 21, 22, 26, 27, 31, 32, 33, 36, 37, 38, 39, 40, 42, 54, 55, 56, 57], "schemaType": "DATASET", "metrics": [{"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 51, "name": "欠费金额", "bizName": "QFJE", "description": "欠费金额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 26, "domainId": 6, "modelBizName": "XSSFXX", "modelName": "学生收费信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSSFXX_QFJE", "filterSql": null, "measures": [{"bizName": "XSSFXX_QFJE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSSFXX_QFJE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 52, "name": "退费金额", "bizName": "TFJE", "description": "退费金额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 26, "domainId": 6, "modelBizName": "XSSFXX", "modelName": "学生收费信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSSFXX_TFJE", "filterSql": null, "measures": [{"bizName": "XSSFXX_TFJE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSSFXX_TFJE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 53, "name": "应缴金额", "bizName": "YJJE", "description": "应缴金额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 26, "domainId": 6, "modelBizName": "XSSFXX", "modelName": "学生收费信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSSFXX_YJJE", "filterSql": null, "measures": [{"bizName": "XSSFXX_YJJE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSSFXX_YJJE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 54, "name": "实缴金额", "bizName": "SJJE", "description": "实缴金额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 26, "domainId": 6, "modelBizName": "XSSFXX", "modelName": "学生收费信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSSFXX_SJJE", "filterSql": null, "measures": [{"bizName": "XSSFXX_SJJE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSSFXX_SJJE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 55, "name": "减免金额", "bizName": "JMJE", "description": "减免金额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 26, "domainId": 6, "modelBizName": "XSSFXX", "modelName": "学生收费信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSSFXX_JMJE", "filterSql": null, "measures": [{"bizName": "XSSFXX_JMJE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSSFXX_JMJE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 56, "name": "贷款金额", "bizName": "DKJE", "description": "贷款金额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 26, "domainId": 6, "modelBizName": "XSSFXX", "modelName": "学生收费信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSSFXX_DKJE", "filterSql": null, "measures": [{"bizName": "XSSFXX_DKJE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSSFXX_DKJE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 62, "name": "奖励金额", "bizName": "JLJE", "description": "奖励金额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "modelBizName": "XSJLZLB", "modelName": "学生奖励子类表", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSJLZLB_JLJE", "filterSql": null, "measures": [{"bizName": "XSJLZLB_JLJE", "constraint": null, "agg": null}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": null, "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSJLZLB_JLJE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:38:49.000+00:00", "updatedAt": "2024-12-21T09:38:49.000+00:00", "id": 63, "name": "积分总和", "bizName": "JFZH", "description": "积分总和", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 39, "domainId": 6, "modelBizName": "DMKJ", "modelName": "学生到梦空间", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "DMKJ_JFZH", "filterSql": null, "measures": [{"bizName": "DMKJ_JFZH", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "DMKJ_JFZH", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:38:49.000+00:00", "updatedAt": "2024-12-21T09:38:49.000+00:00", "id": 64, "name": "小时总和", "bizName": "XSZH", "description": "小时总和", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 39, "domainId": 6, "modelBizName": "DMKJ", "modelName": "学生到梦空间", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "DMKJ_XSZH", "filterSql": null, "measures": [{"bizName": "DMKJ_XSZH", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "DMKJ_XSZH", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:38:49.000+00:00", "updatedAt": "2024-12-21T09:38:49.000+00:00", "id": 65, "name": "转换后学分总和", "bizName": "ZHHXFZH", "description": "转换后学分总和", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 39, "domainId": 6, "modelBizName": "DMKJ", "modelName": "学生到梦空间", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "DMKJ_ZHHXFZH", "filterSql": null, "measures": [{"bizName": "DMKJ_ZHHXFZH", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "DMKJ_ZHHXFZH", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:40:58.000+00:00", "updatedAt": "2024-12-21T09:40:58.000+00:00", "id": 66, "name": "成绩", "bizName": "CJ", "description": "成绩", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 40, "domainId": 6, "modelBizName": "ZHCPCJXX", "modelName": "学生综合测评成绩信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "ZHCPCJXX_CJ", "filterSql": null, "measures": [{"bizName": "ZHCPCJXX_CJ", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "ZHCPCJXX_CJ", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:44:11.000+00:00", "updatedAt": "2024-12-21T09:44:11.000+00:00", "id": 68, "name": "测评分数", "bizName": "PCFS", "description": "测评分数", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 42, "domainId": 6, "modelBizName": "ZHCPZBCJB", "modelName": "学生综合测评指标成绩表", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "ZHCPZBCJB_PCFS", "filterSql": null, "measures": [{"bizName": "ZHCPZBCJB_PCFS", "constraint": null, "agg": null}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": null, "containsPartitionDimensions": false, "useCnt": 0, "expr": "ZHCPZBCJB_PCFS", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 76, "name": "账号余额", "bizName": "ZHYE", "description": "账号余额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "modelBizName": "XSXFXXVIEW", "modelName": "学生消费信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSXFXXVIEW_ZHYE", "filterSql": null, "measures": [{"bizName": "XSXFXXVIEW_ZHYE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSXFXXVIEW_ZHYE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 77, "name": "卡内余额", "bizName": "KNYE", "description": "卡内余额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "modelBizName": "XSXFXXVIEW", "modelName": "学生消费信息", "type": "ATOMIC", "dataFormatType": null, "dataFormat": null, "alias": null, "classifications": null, "relateDimension": null, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSXFXXVIEW_KNYE", "filterSql": null, "measures": [{"bizName": "XSXFXXVIEW_KNYE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSXFXXVIEW_KNYE", "drillDownDimensions": [], "relaDimensionIdKey": ""}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:33:24.000+00:00", "id": 78, "name": "交易金额", "bizName": "JYJE", "description": "交易金额", "status": 1, "typeEnum": "METRIC", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "modelBizName": "XSXFXXVIEW", "modelName": "学生消费信息", "type": "ATOMIC", "dataFormatType": "", "dataFormat": null, "alias": "消费金额", "classifications": null, "relateDimension": {"drillDownDimensions": []}, "hasAdminRes": false, "isCollect": false, "ext": {}, "metricDefineType": "MEASURE", "metricDefineByMeasureParams": {"expr": "XSXFXXVIEW_JYJE", "filterSql": null, "measures": [{"bizName": "XSXFXXVIEW_JYJE", "constraint": null, "agg": "sum"}]}, "metricDefineByFieldParams": null, "metricDefineByMetricParams": null, "isTag": 0, "isPublish": 0, "similarity": 0.0, "defaultAgg": "sum", "containsPartitionDimensions": false, "useCnt": 0, "expr": "XSXFXXVIEW_JYJE", "drillDownDimensions": [], "relaDimensionIdKey": ""}], "dimensions": [{"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 171, "name": "所在班级", "bizName": "SZBJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "SZBJ", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 172, "name": "辅导员/班主任", "bizName": "BZR", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "BZR", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 173, "name": "家庭电话", "bizName": "JTDH", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "JTDH", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 174, "name": "所在院系", "bizName": "SZYX", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "SZYX", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 175, "name": "家庭户口所在地", "bizName": "JTHKSZD", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "JTHKSZD", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 176, "name": "入学时间", "bizName": "RXSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "time", "expr": "RXSJ", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 177, "name": "身份证号", "bizName": "SFZH", "description": "身份证号", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "foreign_key", "expr": "SFZH", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-23T08:30:13.000+00:00", "id": 178, "name": "姓名", "bizName": "XM", "description": "学生姓名；学生名字。", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "XM", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "用户,学生名称,名字", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 179, "name": "毕业时间", "bizName": "BYSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "time", "expr": "BYSJ", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 180, "name": "是否删除", "bizName": "SFSC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "SFSC", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 181, "name": "家庭所在地", "bizName": "JTSZD", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "JTSZD", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 182, "name": "家庭地址", "bizName": "JTDZ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "JTDZ", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 183, "name": "总学分", "bizName": "ZXF", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "categorical", "expr": "ZXF", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-23T07:06:51.000+00:00", "id": 184, "name": "学号", "bizName": "XH", "description": "学号", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 2, "modelId": 20, "domainId": 6, "type": "primary_key", "expr": "XH", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 185, "name": "学历代码", "bizName": "XLDM", "description": "学历代码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "foreign_key", "expr": "XLDM", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 186, "name": "专业代码", "bizName": "ZYDM", "description": "专业代码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 20, "domainId": 6, "type": "foreign_key", "expr": "ZYDM", "modelName": "学生基本信息", "modelBizName": "XSJBXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 187, "name": "英文姓名", "bizName": "YWXM", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "YWXM", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 188, "name": "姓名拼音", "bizName": "XMPY", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "XMPY", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 189, "name": "特长", "bizName": "TC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "TC", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 190, "name": "爱好", "bizName": "AH", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "AH", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 191, "name": "银行卡号", "bizName": "YHKH", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "YHKH", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 192, "name": "出生日期", "bizName": "CSRQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "CSRQ", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 194, "name": "年级", "bizName": "NJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "NJ", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 195, "name": "入学年级", "bizName": "RXNJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "RXNJ", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 196, "name": "是否在校", "bizName": "SFZX", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "categorical", "expr": "SFZX", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 197, "name": "婚姻状况码", "bizName": "HYZKM", "description": "婚姻状况码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "foreign_key", "expr": "HYZKM", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 198, "name": "性别码", "bizName": "XBM", "description": "性别码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "foreign_key", "expr": "XBM", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 199, "name": "籍贯码", "bizName": "JGM", "description": "籍贯码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "foreign_key", "expr": "JGM", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 200, "name": "政治面貌码", "bizName": "ZZMMM", "description": "政治面貌码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "foreign_key", "expr": "ZZMMM", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 201, "name": "血型码", "bizName": "XXM", "description": "血型码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "foreign_key", "expr": "XXM", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 202, "name": "民族码", "bizName": "MZM", "description": "民族码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 21, "domainId": 6, "type": "foreign_key", "expr": "MZM", "modelName": "学生基本数据子类表", "modelBizName": "XSJBSJZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 203, "name": "专业号", "bizName": "ZYH", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "categorical", "expr": "ZYH", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 204, "name": "专业名称", "bizName": "ZYMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "categorical", "expr": "ZYMC", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 205, "name": "专业简称", "bizName": "ZYJC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "categorical", "expr": "ZYJC", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 206, "name": "专业英文名称", "bizName": "ZYYWMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "categorical", "expr": "ZYYWMC", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 207, "name": "建立年月", "bizName": "JLNY", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "time", "expr": "JLNY", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 208, "name": "首次招生日期", "bizName": "SCZSRQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "time", "expr": "SCZSRQ", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 209, "name": "是否重点专业", "bizName": "SFZDZY", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "categorical", "expr": "SFZDZY", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 210, "name": "本专科专业码", "bizName": "BZKZYM", "description": "本专科专业码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "primary_key", "expr": "BZKZYM", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 211, "name": "学位代码", "bizName": "XWDM", "description": "学位代码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 22, "domainId": 6, "type": "foreign_key", "expr": "XWDM", "modelName": "专业信息数据类表", "modelBizName": "ZYXXSJLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 237, "name": "收费项目名称", "bizName": "SFXMMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 26, "domainId": 6, "type": "categorical", "expr": "SFXMMC", "modelName": "学生收费信息", "modelBizName": "XSSFXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 238, "name": "收费期间名称", "bizName": "SFQJMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 26, "domainId": 6, "type": "categorical", "expr": "SFQJMC", "modelName": "学生收费信息", "modelBizName": "XSSFXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 240, "name": "违纪简况", "bizName": "WJJK", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "categorical", "expr": "WJJK", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 241, "name": "处分状态码", "bizName": "CFZTM", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "categorical", "expr": "CFZTM", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 242, "name": "处理部门", "bizName": "CLBM", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "categorical", "expr": "CLBM", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 243, "name": "处分名称", "bizName": "CFMCM", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "categorical", "expr": "CFMCM", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 244, "name": "处分原因", "bizName": "CFYY", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "categorical", "expr": "CFYY", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 245, "name": "处分给与人", "bizName": "CFGYR", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "categorical", "expr": "CFGYR", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 246, "name": "违纪日期", "bizName": "WJRQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "time", "expr": "WJRQ", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 247, "name": "处分撤消日期", "bizName": "CFCXRQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "time", "expr": "CFCXRQ", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 248, "name": "处分日期", "bizName": "CFRQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 27, "domainId": 6, "type": "time", "expr": "CFRQ", "modelName": "学生惩处子类表", "modelBizName": "XSCCZLB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:55:19.000+00:00", "updatedAt": "2024-12-21T08:55:19.000+00:00", "id": 270, "name": "闸机名称", "bizName": "ZJMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 33, "domainId": 6, "type": "categorical", "expr": "ZJMC", "modelName": "图书馆门禁通过信息", "modelBizName": "TSGMJTGXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:55:19.000+00:00", "updatedAt": "2024-12-21T08:55:19.000+00:00", "id": 271, "name": "图书馆", "bizName": "TSG", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 33, "domainId": 6, "type": "categorical", "expr": "TSG", "modelName": "图书馆门禁通过信息", "modelBizName": "TSGMJTGXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:55:19.000+00:00", "updatedAt": "2024-12-21T08:55:19.000+00:00", "id": 272, "name": "方向", "bizName": "FX", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 33, "domainId": 6, "type": "categorical", "expr": "FX", "modelName": "图书馆门禁通过信息", "modelBizName": "TSGMJTGXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:55:19.000+00:00", "updatedAt": "2024-12-21T08:55:19.000+00:00", "id": 273, "name": "通过时间", "bizName": "TGSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 33, "domainId": 6, "type": "time", "expr": "TGSJ", "modelName": "图书馆门禁通过信息", "modelBizName": "TSGMJTGXX", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:55:19.000+00:00", "updatedAt": "2024-12-21T08:55:19.000+00:00", "id": 274, "name": "刷卡方式", "bizName": "SKFS", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 33, "domainId": 6, "type": "categorical", "expr": "SKFS", "modelName": "图书馆门禁通过信息", "modelBizName": "TSGMJTGXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 292, "name": "开始时间", "bizName": "KSSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "time", "expr": "KSSJ", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 293, "name": "紧急联系人电话", "bizName": "JJLXRDH", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "categorical", "expr": "JJLXRDH", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 294, "name": "销假或回校时间", "bizName": "XJSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "categorical", "expr": "XJSJ", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 295, "name": "代销假人员姓名", "bizName": "DXJRXM", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "categorical", "expr": "DXJRXM", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 296, "name": "是否离校", "bizName": "SFLX", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "categorical", "expr": "SFLX", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 297, "name": "结束时间", "bizName": "JSSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "time", "expr": "JSSJ", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 298, "name": "请假时间", "bizName": "QJSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "time", "expr": "QJSJ", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 299, "name": "请假状态", "bizName": "QJZT", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "categorical", "expr": "QJZT", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 300, "name": "请假内容", "bizName": "QJNR", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "categorical", "expr": "QJNR", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 301, "name": "请假类型", "bizName": "QJLX", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 36, "domainId": 6, "type": "categorical", "expr": "QJLX", "modelName": "学生请假信息", "modelBizName": "QJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:32:06.000+00:00", "updatedAt": "2024-12-21T09:32:06.000+00:00", "id": 303, "name": "异动类型名称", "bizName": "YDLXMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 37, "domainId": 6, "type": "categorical", "expr": "YDLXMC", "modelName": "学籍异动数据类", "modelBizName": "XJYDSJL", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:32:06.000+00:00", "updatedAt": "2024-12-21T09:32:06.000+00:00", "id": 304, "name": "异动原因", "bizName": "YDYY", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 37, "domainId": 6, "type": "categorical", "expr": "YDYY", "modelName": "学籍异动数据类", "modelBizName": "XJYDSJL", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:32:06.000+00:00", "updatedAt": "2024-12-21T09:32:06.000+00:00", "id": 305, "name": "异动时间", "bizName": "YDSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 37, "domainId": 6, "type": "time", "expr": "YDSJ", "modelName": "学籍异动数据类", "modelBizName": "XJYDSJL", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:32:06.000+00:00", "updatedAt": "2024-12-21T09:32:06.000+00:00", "id": 306, "name": "fields的可读文本", "bizName": "FIELDS_TEXT", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 37, "domainId": 6, "type": "categorical", "expr": "FIELDS_TEXT", "modelName": "学籍异动数据类", "modelBizName": "XJYDSJL", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 308, "name": "奖励名称", "bizName": "JLMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "type": "categorical", "expr": "JLMC", "modelName": "学生奖励子类表", "modelBizName": "XSJLZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 309, "name": "获奖时间", "bizName": "HJSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "type": "time", "expr": "HJSJ", "modelName": "学生奖励子类表", "modelBizName": "XSJLZLB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 310, "name": "获奖学期", "bizName": "HJXQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "type": "categorical", "expr": "HJXQ", "modelName": "学生奖励子类表", "modelBizName": "XSJLZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 311, "name": "颁奖单位", "bizName": "BJDW", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "type": "categorical", "expr": "BJDW", "modelName": "学生奖励子类表", "modelBizName": "XSJLZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 312, "name": "奖励原因", "bizName": "JLYY", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "type": "categorical", "expr": "JLYY", "modelName": "学生奖励子类表", "modelBizName": "XSJLZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 313, "name": "获奖学年度", "bizName": "HJXND", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "type": "categorical", "expr": "HJXND", "modelName": "学生奖励子类表", "modelBizName": "XSJLZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 315, "name": "奖励级别码", "bizName": "JLJBM", "description": "奖励级别码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "type": "foreign_key", "expr": "JLJBM", "modelName": "学生奖励子类表", "modelBizName": "XSJLZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 316, "name": "奖励等级码", "bizName": "JLDJM", "description": "奖励等级码", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 38, "domainId": 6, "type": "foreign_key", "expr": "JLDJM", "modelName": "学生奖励子类表", "modelBizName": "XSJLZLB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:40:58.000+00:00", "updatedAt": "2024-12-21T09:42:58.000+00:00", "id": 318, "name": "学期", "bizName": "XQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 40, "domainId": 6, "type": "categorical", "expr": "XQ", "modelName": "学生综合测评成绩信息", "modelBizName": "ZHCPCJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:40:58.000+00:00", "updatedAt": "2024-12-21T09:42:58.000+00:00", "id": 319, "name": "班级排名", "bizName": "BJPM", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 40, "domainId": 6, "type": "categorical", "expr": "BJPM", "modelName": "学生综合测评成绩信息", "modelBizName": "ZHCPCJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:40:58.000+00:00", "updatedAt": "2024-12-21T09:42:58.000+00:00", "id": 320, "name": "专业排名", "bizName": "ZYPM", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 40, "domainId": 6, "type": "categorical", "expr": "ZYPM", "modelName": "学生综合测评成绩信息", "modelBizName": "ZHCPCJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:44:11.000+00:00", "updatedAt": "2024-12-21T09:44:21.000+00:00", "id": 327, "name": "测评类型名称", "bizName": "CPLXMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 42, "domainId": 6, "type": "categorical", "expr": "CPLXMC", "modelName": "学生综合测评指标成绩表", "modelBizName": "ZHCPZBCJB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:44:11.000+00:00", "updatedAt": "2024-12-21T09:44:21.000+00:00", "id": 328, "name": "学年学期", "bizName": "XNXQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 42, "domainId": 6, "type": "categorical", "expr": "XNXQ", "modelName": "学生综合测评指标成绩表", "modelBizName": "ZHCPZBCJB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T03:16:08.000+00:00", "updatedAt": "2024-12-23T03:16:08.000+00:00", "id": 366, "name": "门禁进出方向", "bizName": "MJJCFX", "description": "门禁进出方向", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 32, "domainId": 6, "type": "categorical", "expr": "JCFX", "modelName": "社区-门禁信息", "modelBizName": "SQ_MJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "门禁进出方向", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T03:17:28.000+00:00", "updatedAt": "2024-12-23T03:17:28.000+00:00", "id": 368, "name": "门禁当前标志", "bizName": "NJDQBZ", "description": "门禁当前标志", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 32, "domainId": 6, "type": "categorical", "expr": "DQBZ", "modelName": "社区-门禁信息", "modelBizName": "SQ_MJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "门禁当前标志", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T03:18:35.000+00:00", "updatedAt": "2024-12-23T03:18:35.000+00:00", "id": 369, "name": "门禁违纪方式", "bizName": "MJWJFS", "description": "门禁违纪方式", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 32, "domainId": 6, "type": "categorical", "expr": "WJFS", "modelName": "社区-门禁信息", "modelBizName": "SQ_MJXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "门禁违纪方式", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T03:19:34.000+00:00", "updatedAt": "2024-12-23T03:19:34.000+00:00", "id": 370, "name": "晚归未归违纪方式", "bizName": "WGWJWJFS", "description": "晚归未归违纪方式", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 31, "domainId": 6, "type": "categorical", "expr": "WJFS", "modelName": "宿舍晚归未归信息", "modelBizName": "SSWGWGXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "晚归未归违纪方式", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T03:20:23.000+00:00", "updatedAt": "2024-12-23T03:20:23.000+00:00", "id": 371, "name": "晚归未归进出方向", "bizName": "WGWJJCFX", "description": "晚归未归进出方向", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 31, "domainId": 6, "type": "categorical", "expr": "JCFX", "modelName": "宿舍晚归未归信息", "modelBizName": "SSWGWGXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "晚归未归进出方向", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T03:22:08.000+00:00", "updatedAt": "2024-12-23T03:22:08.000+00:00", "id": 372, "name": "晚归未归应归宿时间", "bizName": "WGWGYGSSJ", "description": "晚归未归应归宿时间", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 31, "domainId": 6, "type": "categorical", "expr": "YGSSJ", "modelName": "宿舍晚归未归信息", "modelBizName": "SSWGWGXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "晚归未归应归宿时间", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T03:23:23.000+00:00", "updatedAt": "2024-12-23T03:23:23.000+00:00", "id": 373, "name": "晚归未归进出时间", "bizName": "WGWGJCSJ", "description": "晚归未归进出时间", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 31, "domainId": 6, "type": "categorical", "expr": "JCSJ", "modelName": "宿舍晚归未归信息", "modelBizName": "SSWGWGXX", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "晚归未归进出时间", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T06:47:20.000+00:00", "updatedAt": "2024-12-23T06:47:20.000+00:00", "id": 374, "name": "图书名称", "bizName": "TSMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 54, "domainId": 6, "type": "categorical", "expr": "TSMC", "modelName": "图书借阅信息表", "modelBizName": "XSTSJYXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T06:47:20.000+00:00", "updatedAt": "2024-12-23T06:47:20.000+00:00", "id": 375, "name": "还书地点", "bizName": "HSD", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 54, "domainId": 6, "type": "categorical", "expr": "HSD", "modelName": "图书借阅信息表", "modelBizName": "XSTSJYXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T06:47:20.000+00:00", "updatedAt": "2024-12-23T06:47:20.000+00:00", "id": 376, "name": "借书地点", "bizName": "JSD", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 54, "domainId": 6, "type": "categorical", "expr": "JSD", "modelName": "图书借阅信息表", "modelBizName": "XSTSJYXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T06:47:20.000+00:00", "updatedAt": "2024-12-23T06:47:20.000+00:00", "id": 377, "name": "借阅时间", "bizName": "JYSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 54, "domainId": 6, "type": "time", "expr": "JYSJ", "modelName": "图书借阅信息表", "modelBizName": "XSTSJYXXB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T06:47:20.000+00:00", "updatedAt": "2024-12-23T06:47:20.000+00:00", "id": 378, "name": "实际归还时间", "bizName": "SJGHSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 54, "domainId": 6, "type": "time", "expr": "SJGHSJ", "modelName": "图书借阅信息表", "modelBizName": "XSTSJYXXB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T08:24:38.000+00:00", "updatedAt": "2024-12-23T12:25:18.000+00:00", "id": 379, "name": "签到时间", "bizName": "QDSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 55, "domainId": 6, "type": "time", "expr": "QDSJ", "modelName": "学生活动签到情况表", "modelBizName": "XSHDQDQKB", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T08:24:38.000+00:00", "updatedAt": "2024-12-23T12:25:22.000+00:00", "id": 380, "name": "签到结果", "bizName": "QDJG", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 55, "domainId": 6, "type": "categorical", "expr": "QDJG", "modelName": "学生活动签到情况表", "modelBizName": "XSHDQDQKB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T08:24:38.000+00:00", "updatedAt": "2024-12-23T12:25:25.000+00:00", "id": 381, "name": "签到标题", "bizName": "BT", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 55, "domainId": 6, "type": "categorical", "expr": "BT", "modelName": "学生活动签到情况表", "modelBizName": "XSHDQDQKB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T08:24:38.000+00:00", "updatedAt": "2024-12-23T12:25:28.000+00:00", "id": 382, "name": "签到内容", "bizName": "NR", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 55, "domainId": 6, "type": "categorical", "expr": "NR", "modelName": "学生活动签到情况表", "modelBizName": "XSHDQDQKB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T08:24:38.000+00:00", "updatedAt": "2024-12-23T12:25:30.000+00:00", "id": 383, "name": "班级、团队、分组名称", "bizName": "FZMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 55, "domainId": 6, "type": "categorical", "expr": "FZMC", "modelName": "学生活动签到情况表", "modelBizName": "XSHDQDQKB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 384, "name": "学年学期", "bizName": "XNXQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "XNXQ", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 385, "name": "是否是重修生", "bizName": "SFCXS", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "SFCXS", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 386, "name": "课程名称", "bizName": "KCMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "KCMC", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 387, "name": "课程学分", "bizName": "KCXF", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "KCXF", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 388, "name": "课程总学时", "bizName": "KCZXS", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "KCZXS", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 389, "name": "课程周学时", "bizName": "KCMZXS", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "KCMZXS", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 390, "name": "课程负责人", "bizName": "KCFZR", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "KCFZR", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 391, "name": "课程成绩绩点", "bizName": "JD", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "JD", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 392, "name": "平时成绩", "bizName": "PSCJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "PSCJ", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 393, "name": "课程成绩", "bizName": "KCCJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "KCCJ", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 394, "name": "总成绩", "bizName": "ZCJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "ZCJ", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 395, "name": "平均学分绩点", "bizName": "PJXFJD", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "PJXFJD", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 396, "name": "等级类考试成绩", "bizName": "DJLKSCJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "DJLKSCJ", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 397, "name": "分数类考试成绩", "bizName": "FSLKSCJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "FSLKSCJ", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:08:07.000+00:00", "id": 398, "name": "考试日期", "bizName": "KSRQ", "description": "考试日期", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "time", "expr": "KSRQ", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": "考试日期", "defaultValues": null, "dimValueMaps": [], "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:07:36.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 399, "name": "是否及格", "bizName": "SFJG", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 56, "domainId": 6, "type": "categorical", "expr": "SFJG", "modelName": "学生考试成绩信息表", "modelBizName": "XSCJXXB", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 400, "name": "卡号", "bizName": "KH", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "type": "categorical", "expr": "KH", "modelName": "学生消费信息", "modelBizName": "XSXFXXVIEW", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 401, "name": "账户开户日期", "bizName": "ZHKHRQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "type": "time", "expr": "ZHKHRQ", "modelName": "学生消费信息", "modelBizName": "XSXFXXVIEW", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 402, "name": "账户有效日期", "bizName": "ZHYXRQ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "type": "time", "expr": "ZHYXRQ", "modelName": "学生消费信息", "modelBizName": "XSXFXXVIEW", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 403, "name": "账户状态标志", "bizName": "ZHZT", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "type": "categorical", "expr": "ZHZT", "modelName": "学生消费信息", "modelBizName": "XSXFXXVIEW", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 404, "name": "入账日期时间", "bizName": "RZRQSJ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "type": "time", "expr": "RZRQSJ", "modelName": "学生消费信息", "modelBizName": "XSXFXXVIEW", "modelFilterSql": "", "semanticType": "DATE", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {"time_format": "yyyy-MM-dd HH:mm"}, "useCnt": 0, "timeDimension": true, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 405, "name": "用卡次数", "bizName": "YKCS", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "type": "categorical", "expr": "YKCS", "modelName": "学生消费信息", "modelBizName": "XSXFXXVIEW", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 406, "name": "消费地址", "bizName": "DZ", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "type": "categorical", "expr": "DZ", "modelName": "学生消费信息", "modelBizName": "XSXFXXVIEW", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:17:10.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 407, "name": "消费商户名称", "bizName": "SHMC", "description": "", "status": 1, "typeEnum": "DIMENSION", "sensitiveLevel": 0, "modelId": 57, "domainId": 6, "type": "categorical", "expr": "SHMC", "modelName": "学生消费信息", "modelBizName": "XSXFXXVIEW", "modelFilterSql": "", "semanticType": "CATEGORY", "alias": null, "defaultValues": null, "dimValueMaps": null, "dataType": null, "isTag": 0, "typeParams": null, "ext": {}, "useCnt": 0, "timeDimension": false, "partitionTime": false}], "tags": [], "modelRelas": [{"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 14, "domainId": 6, "fromModelId": 20, "toModelId": 21, "joinType": "inner join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 15, "domainId": 6, "fromModelId": 20, "toModelId": 22, "joinType": "left join", "joinConditions": [{"leftField": "ZYDM", "rightField": "BZKZYM", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 16, "domainId": 6, "fromModelId": 20, "toModelId": 23, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 18, "domainId": 6, "fromModelId": 20, "toModelId": 25, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 19, "domainId": 6, "fromModelId": 20, "toModelId": 26, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 20, "domainId": 6, "fromModelId": 20, "toModelId": 27, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 21, "domainId": 6, "fromModelId": 20, "toModelId": 28, "joinType": "inner join", "joinConditions": [{"leftField": "XH", "rightField": "XGH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 24, "domainId": 6, "fromModelId": 20, "toModelId": 31, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 25, "domainId": 6, "fromModelId": 20, "toModelId": 32, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 26, "domainId": 6, "fromModelId": 20, "toModelId": 33, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 27, "domainId": 6, "fromModelId": 20, "toModelId": 34, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 29, "domainId": 6, "fromModelId": 20, "toModelId": 36, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 30, "domainId": 6, "fromModelId": 20, "toModelId": 37, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 31, "domainId": 6, "fromModelId": 20, "toModelId": 38, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 32, "domainId": 6, "fromModelId": 20, "toModelId": 39, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 33, "domainId": 6, "fromModelId": 20, "toModelId": 40, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 34, "domainId": 6, "fromModelId": 20, "toModelId": 42, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 35, "domainId": 6, "fromModelId": 20, "toModelId": 43, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XGH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 38, "domainId": 6, "fromModelId": 36, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 39, "domainId": 6, "fromModelId": 21, "toModelId": 20, "joinType": "inner join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 40, "domainId": 6, "fromModelId": 22, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "BZKZYM", "rightField": "ZYDM", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 47, "domainId": 6, "fromModelId": 26, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 48, "domainId": 6, "fromModelId": 27, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 52, "domainId": 6, "fromModelId": 31, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 53, "domainId": 6, "fromModelId": 32, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 54, "domainId": 6, "fromModelId": 33, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 56, "domainId": 6, "fromModelId": 34, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 58, "domainId": 6, "fromModelId": 37, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 59, "domainId": 6, "fromModelId": 38, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 60, "domainId": 6, "fromModelId": 39, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 61, "domainId": 6, "fromModelId": 40, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 62, "domainId": 6, "fromModelId": 42, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 65, "domainId": 6, "fromModelId": 54, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 66, "domainId": 6, "fromModelId": 55, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 67, "domainId": 6, "fromModelId": 56, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}]}, {"createdBy": null, "updatedBy": null, "createdAt": null, "updatedAt": null, "id": 68, "domainId": 6, "fromModelId": 57, "toModelId": 20, "joinType": "left join", "joinConditions": [{"leftField": "XH", "rightField": "XH", "operator": "="}, {"leftField": "ZJH", "rightField": "SFZH", "operator": "="}]}], "modelResps": [{"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:03:12.000+00:00", "updatedAt": "2024-12-21T08:38:03.000+00:00", "id": 20, "name": "学生基本信息", "bizName": "XSJBXX", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.XSJBXX", "identifiers": [{"name": "身份证号", "type": "foreign", "bizName": "SFZH", "isCreateDimension": 1, "fieldName": "SFZH"}, {"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}, {"name": "学历代码", "type": "foreign", "bizName": "XLDM", "isCreateDimension": 1, "fieldName": "XLDM"}, {"name": "专业代码", "type": "foreign", "bizName": "ZYDM", "isCreateDimension": 1, "fieldName": "ZYDM"}], "dimensions": [{"name": "所在班级", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SZBJ", "description": null, "fieldName": "SZBJ"}, {"name": "辅导员/班主任", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "BZR", "description": null, "fieldName": "BZR"}, {"name": "家庭电话", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JTDH", "description": null, "fieldName": "JTDH"}, {"name": "所在院系", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SZYX", "description": null, "fieldName": "SZYX"}, {"name": "家庭户口所在地", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JTHKSZD", "description": null, "fieldName": "JTHKSZD"}, {"name": "入学时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "RXSJ", "description": null, "fieldName": "RXSJ"}, {"name": "姓名", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "XM", "description": null, "fieldName": "XM"}, {"name": "毕业时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "BYSJ", "description": null, "fieldName": "BYSJ"}, {"name": "是否删除", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFSC", "description": null, "fieldName": "SFSC"}, {"name": "家庭所在地", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JTSZD", "description": null, "fieldName": "JTSZD"}, {"name": "家庭地址", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JTDZ", "description": null, "fieldName": "JTDZ"}, {"name": "总学分", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZXF", "description": null, "fieldName": "ZXF"}], "measures": [], "fields": [{"fieldName": "DAID", "dataType": "VARCHAR2"}, {"fieldName": "EMAIL", "dataType": "VARCHAR2"}, {"fieldName": "JTHKSZDDM", "dataType": "VARCHAR2"}, {"fieldName": "KSH", "dataType": "VARCHAR2"}, {"fieldName": "ZYMC", "dataType": "VARCHAR2"}, {"fieldName": "SZBJ", "dataType": "VARCHAR2"}, {"fieldName": "RXQDASZDW", "dataType": "VARCHAR2"}, {"fieldName": "CJR", "dataType": "VARCHAR2"}, {"fieldName": "XGSJ", "dataType": "VARCHAR2"}, {"fieldName": "BYYXXF", "dataType": "VARCHAR2"}, {"fieldName": "PJXFJD", "dataType": "VARCHAR2"}, {"fieldName": "FDY", "dataType": "VARCHAR2"}, {"fieldName": "SFJDLKH", "dataType": "VARCHAR2"}, {"fieldName": "XXID", "dataType": "VARCHAR2"}, {"fieldName": "BZR", "dataType": "VARCHAR2"}, {"fieldName": "KNSLBDM", "dataType": "VARCHAR2"}, {"fieldName": "PYFS", "dataType": "VARCHAR2"}, {"fieldName": "HKSFZRXX", "dataType": "VARCHAR2"}, {"fieldName": "DXHWPDW", "dataType": "VARCHAR2"}, {"fieldName": "JYSHSM", "dataType": "VARCHAR2"}, {"fieldName": "XGR", "dataType": "VARCHAR2"}, {"fieldName": "JTLXRXM", "dataType": "VARCHAR2"}, {"fieldName": "IS_NEW", "dataType": "VARCHAR2"}, {"fieldName": "JTDH", "dataType": "VARCHAR2"}, {"fieldName": "SZYX", "dataType": "VARCHAR2"}, {"fieldName": "SFSLBDM", "dataType": "VARCHAR2"}, {"fieldName": "SFSLB", "dataType": "VARCHAR2"}, {"fieldName": "PYFSDM", "dataType": "VARCHAR2"}, {"fieldName": "SFTJSH", "dataType": "VARCHAR2"}, {"fieldName": "TJSJ", "dataType": "VARCHAR2"}, {"fieldName": "JYSH", "dataType": "VARCHAR2"}, {"fieldName": "CJSJ", "dataType": "VARCHAR2"}, {"fieldName": "BJSHSJ", "dataType": "VARCHAR2"}, {"fieldName": "JTHKSZD", "dataType": "VARCHAR2"}, {"fieldName": "XZ", "dataType": "VARCHAR2"}, {"fieldName": "ZYFX", "dataType": "VARCHAR2"}, {"fieldName": "KNSLB", "dataType": "VARCHAR2"}, {"fieldName": "JYSHSJ", "dataType": "VARCHAR2"}, {"fieldName": "SFLJYJT", "dataType": "VARCHAR2"}, {"fieldName": "XSID", "dataType": "VARCHAR2"}, {"fieldName": "XL", "dataType": "VARCHAR2"}, {"fieldName": "RXSJ", "dataType": "VARCHAR2"}, {"fieldName": "BYNF", "dataType": "VARCHAR2"}, {"fieldName": "SFJDLK", "dataType": "VARCHAR2"}, {"fieldName": "XB", "dataType": "VARCHAR2"}, {"fieldName": "SFZH", "dataType": "VARCHAR2"}, {"fieldName": "KSLBDM", "dataType": "VARCHAR2"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "DASFZRXX", "dataType": "VARCHAR2"}, {"fieldName": "RXQHKSZDPCS", "dataType": "VARCHAR2"}, {"fieldName": "XYSHSM", "dataType": "VARCHAR2"}, {"fieldName": "BJSHR", "dataType": "VARCHAR2"}, {"fieldName": "BJSHSM", "dataType": "VARCHAR2"}, {"fieldName": "SYHDSM", "dataType": "VARCHAR2"}, {"fieldName": "ZCSHSJ", "dataType": "VARCHAR2"}, {"fieldName": "XM", "dataType": "VARCHAR2"}, {"fieldName": "XLDM", "dataType": "VARCHAR2"}, {"fieldName": "ZYDM", "dataType": "VARCHAR2"}, {"fieldName": "BYSJ", "dataType": "VARCHAR2"}, {"fieldName": "XYSH", "dataType": "VARCHAR2"}, {"fieldName": "XYSHSJ", "dataType": "VARCHAR2"}, {"fieldName": "JYSHR", "dataType": "VARCHAR2"}, {"fieldName": "BJSH", "dataType": "VARCHAR2"}, {"fieldName": "XYSHR", "dataType": "VARCHAR2"}, {"fieldName": "SYHDSJ", "dataType": "VARCHAR2"}, {"fieldName": "SFZCDXS", "dataType": "VARCHAR2"}, {"fieldName": "SFSC", "dataType": "VARCHAR2"}, {"fieldName": "JTSZD", "dataType": "VARCHAR2"}, {"fieldName": "JTDZ", "dataType": "VARCHAR2"}, {"fieldName": "KSLB", "dataType": "VARCHAR2"}, {"fieldName": "SYHD", "dataType": "VARCHAR2"}, {"fieldName": "ZXF", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": [], "alias": null, "fullPath": null, "ext": null, "fieldList": ["XZ", "BJSH", "KNSLB", "JTSZD", "BYYXXF", "EMAIL", "KNSLBDM", "JTLXRXM", "DXHWPDW", "BJSHR", "SFSLBDM", "SFJDLK", "SYHDSJ", "PYFSDM", "SYHDSM", "PJXFJD", "HKSFZRXX", "ZYDM", "DASFZRXX", "IS_NEW", "JTHKSZD", "XGSJ", "SFZCDXS", "RXQDASZDW", "XXID", "PYFS", "RXSJ", "SFTJSH", "ZYMC", "XYSH", "XGR", "JTDZ", "SFSC", "SYHD", "JYSH", "DAID", "XYSHSM", "SZBJ", "ZXF", "CJR", "BYNF", "JYSHR", "RXQHKSZDPCS", "XLDM", "KSLBDM", "SFLJYJT", "TIMESTAMPS", "FDY", "XYSHSJ", "KSH", "SZYX", "CJSJ", "SFSLB", "SFZH", "JYSHSJ", "TJSJ", "XB", "JTDH", "JYSHSM", "BJSHSJ", "BJSHSM", "XH", "BYSJ", "XL", "XM", "SFJDLKH", "JTHKSZDDM", "BZR", "ZYFX", "XYSHR", "KSLB", "ZCSHSJ", "XSID"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:07:57.000+00:00", "updatedAt": "2024-12-21T08:07:57.000+00:00", "id": 21, "name": "学生基本数据子类表", "bizName": "XSJBSJZLB", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.XSJBSJZLB", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 0, "fieldName": "XH"}, {"name": "婚姻状况码", "type": "foreign", "bizName": "HYZKM", "isCreateDimension": 1, "fieldName": "HYZKM"}, {"name": "性别码", "type": "foreign", "bizName": "XBM", "isCreateDimension": 1, "fieldName": "XBM"}, {"name": "籍贯码", "type": "foreign", "bizName": "JGM", "isCreateDimension": 1, "fieldName": "JGM"}, {"name": "政治面貌码", "type": "foreign", "bizName": "ZZMMM", "isCreateDimension": 1, "fieldName": "ZZMMM"}, {"name": "血型码", "type": "foreign", "bizName": "XXM", "isCreateDimension": 1, "fieldName": "XXM"}, {"name": "民族码", "type": "foreign", "bizName": "MZM", "isCreateDimension": 1, "fieldName": "MZM"}], "dimensions": [{"name": "英文姓名", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "YWXM", "description": null, "fieldName": "YWXM"}, {"name": "姓名拼音", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "XMPY", "description": null, "fieldName": "XMPY"}, {"name": "特长", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "TC", "description": null, "fieldName": "TC"}, {"name": "爱好", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "AH", "description": null, "fieldName": "AH"}, {"name": "银行卡号", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "YHKH", "description": null, "fieldName": "YHKH"}, {"name": "出生日期", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "CSRQ", "description": null, "fieldName": "CSRQ"}, {"name": "身份证件号", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFZJH", "description": null, "fieldName": "SFZJH"}, {"name": "年级", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "NJ", "description": null, "fieldName": "NJ"}, {"name": "入学年级", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "RXNJ", "description": null, "fieldName": "RXNJ"}, {"name": "是否在校", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFZX", "description": null, "fieldName": "SFZX"}], "measures": [], "fields": [{"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "XM", "dataType": "VARCHAR2"}, {"fieldName": "YWXM", "dataType": "VARCHAR2"}, {"fieldName": "XMPY", "dataType": "VARCHAR2"}, {"fieldName": "HYZKM", "dataType": "VARCHAR2"}, {"fieldName": "CYM", "dataType": "VARCHAR2"}, {"fieldName": "XBM", "dataType": "VARCHAR2"}, {"fieldName": "SFDSZN", "dataType": "VARCHAR2"}, {"fieldName": "GJDQM", "dataType": "VARCHAR2"}, {"fieldName": "JGM", "dataType": "VARCHAR2"}, {"fieldName": "XYDM", "dataType": "VARCHAR2"}, {"fieldName": "BJMC", "dataType": "VARCHAR2"}, {"fieldName": "CCQJ", "dataType": "VARCHAR2"}, {"fieldName": "SFZJYXQ", "dataType": "VARCHAR2"}, {"fieldName": "TC", "dataType": "VARCHAR2"}, {"fieldName": "AH", "dataType": "VARCHAR2"}, {"fieldName": "YHKH", "dataType": "VARCHAR2"}, {"fieldName": "CSRQ", "dataType": "VARCHAR2"}, {"fieldName": "ZP", "dataType": "BLOB"}, {"fieldName": "CSDM", "dataType": "VARCHAR2"}, {"fieldName": "SFZJH", "dataType": "VARCHAR2"}, {"fieldName": "SFZJLXM", "dataType": "VARCHAR2"}, {"fieldName": "GATQWM", "dataType": "VARCHAR2"}, {"fieldName": "JKZKM", "dataType": "VARCHAR2"}, {"fieldName": "ZZMMM", "dataType": "VARCHAR2"}, {"fieldName": "XXM", "dataType": "VARCHAR2"}, {"fieldName": "MZM", "dataType": "VARCHAR2"}, {"fieldName": "XYZJM", "dataType": "VARCHAR2"}, {"fieldName": "HJXZM", "dataType": "VARCHAR2"}, {"fieldName": "NJ", "dataType": "VARCHAR2"}, {"fieldName": "SFZJ", "dataType": "VARCHAR2"}, {"fieldName": "XSLBM", "dataType": "VARCHAR2"}, {"fieldName": "BJDM", "dataType": "VARCHAR2"}, {"fieldName": "SFYXJ", "dataType": "VARCHAR2"}, {"fieldName": "XJZT", "dataType": "VARCHAR2"}, {"fieldName": "ZYMC", "dataType": "VARCHAR2"}, {"fieldName": "ZYDM", "dataType": "VARCHAR2"}, {"fieldName": "YXMC", "dataType": "VARCHAR2"}, {"fieldName": "RXNJ", "dataType": "VARCHAR2"}, {"fieldName": "SFZX", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["SFZJYXQ", "BJMC", "MZM", "XSLBM", "YHKH", "XJZT", "SFDSZN", "XXM", "GATQWM", "BJDM", "HJXZM", "CYM", "GJDQM", "XMPY", "SFZJH", "TIMESTAMPS", "YXMC", "XBM", "ZYDM", "XYDM", "RXNJ", "SFZJ", "CCQJ", "CSDM", "AH", "SFYXJ", "HYZKM", "JKZKM", "TC", "XH", "CSRQ", "JGM", "SFZJLXM", "ZZMMM", "XM", "ZP", "SFZX", "YWXM", "NJ", "XYZJM", "ZYMC"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:13:29.000+00:00", "updatedAt": "2024-12-21T08:13:29.000+00:00", "id": 22, "name": "专业信息数据类表", "bizName": "ZYXXSJLB", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.ZYXXSJLB", "identifiers": [{"name": "本专科专业码", "type": "primary", "bizName": "BZKZYM", "isCreateDimension": 1, "fieldName": "BZKZYM"}, {"name": "学位代码", "type": "foreign", "bizName": "XWDM", "isCreateDimension": 1, "fieldName": "XWDM"}], "dimensions": [{"name": "专业号", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZYH", "description": null, "fieldName": "ZYH"}, {"name": "专业名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZYMC", "description": null, "fieldName": "ZYMC"}, {"name": "专业简称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZYJC", "description": null, "fieldName": "ZYJC"}, {"name": "专业英文名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZYYWMC", "description": null, "fieldName": "ZYYWMC"}, {"name": "学制", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 0, "bizName": "XZ", "description": null, "fieldName": "XZ"}, {"name": "建立年月", "type": "time", "expr": null, "dateFormat": "yyyy-MM", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "JLNY", "description": null, "fieldName": "JLNY"}, {"name": "首次招生日期", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "SCZSRQ", "description": null, "fieldName": "SCZSRQ"}, {"name": "是否重点专业", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFZDZY", "description": null, "fieldName": "SFZDZY"}], "measures": [], "fields": [{"fieldName": "ZYH", "dataType": "VARCHAR2"}, {"fieldName": "ZYMC", "dataType": "VARCHAR2"}, {"fieldName": "ZYJC", "dataType": "VARCHAR2"}, {"fieldName": "ZYFXH", "dataType": "VARCHAR2"}, {"fieldName": "BZKZYM", "dataType": "VARCHAR2"}, {"fieldName": "DWH", "dataType": "VARCHAR2"}, {"fieldName": "XKMLM", "dataType": "VARCHAR2"}, {"fieldName": "PYCCM", "dataType": "VARCHAR2"}, {"fieldName": "XWDM", "dataType": "VARCHAR2"}, {"fieldName": "XWLBM", "dataType": "VARCHAR2"}, {"fieldName": "QSXQM", "dataType": "VARCHAR2"}, {"fieldName": "ZSZTM", "dataType": "VARCHAR2"}, {"fieldName": "ZYYWMC", "dataType": "VARCHAR2"}, {"fieldName": "XZ", "dataType": "NUMBER"}, {"fieldName": "JLNY", "dataType": "VARCHAR2"}, {"fieldName": "SCZSRQ", "dataType": "VARCHAR2"}, {"fieldName": "ZDZYJBM", "dataType": "VARCHAR2"}, {"fieldName": "ZDZYLXM", "dataType": "VARCHAR2"}, {"fieldName": "SFZDZY", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["XKMLM", "ZDZYLXM", "XWDM", "ZSZTM", "XZ", "ZYH", "ZYFXH", "BZKZYM", "QSXQM", "JLNY", "ZYJC", "DWH", "PYCCM", "SCZSRQ", "ZDZYJBM", "ZYYWMC", "XWLBM", "TIMESTAMPS", "SFZDZY", "ZYMC"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:33:55.000+00:00", "updatedAt": "2024-12-21T08:33:55.000+00:00", "id": 26, "name": "学生收费信息", "bizName": "XSSFXX", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.XSSFXX", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "收费项目名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFXMMC", "description": null, "fieldName": "SFXMMC"}, {"name": "收费期间名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFQJMC", "description": null, "fieldName": "SFQJMC"}], "measures": [{"name": "欠费金额", "agg": "sum", "expr": "QFJE", "bizName": "XSSFXX_QFJE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "QFJE"}, {"name": "退费金额", "agg": "sum", "expr": "TFJE", "bizName": "XSSFXX_TFJE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "TFJE"}, {"name": "应缴金额", "agg": "sum", "expr": "YJJE", "bizName": "XSSFXX_YJJE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "YJJE"}, {"name": "实缴金额", "agg": "sum", "expr": "SJJE", "bizName": "XSSFXX_SJJE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "SJJE"}, {"name": "减免金额", "agg": "sum", "expr": "JMJE", "bizName": "XSSFXX_JMJE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "JMJE"}, {"name": "贷款金额", "agg": "sum", "expr": "DKJE", "bizName": "XSSFXX_DKJE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "DKJE"}], "fields": [{"fieldName": "SFQJDM", "dataType": "VARCHAR2"}, {"fieldName": "QFJE", "dataType": "VARCHAR2"}, {"fieldName": "SFXMMC", "dataType": "VARCHAR2"}, {"fieldName": "TFJE", "dataType": "VARCHAR2"}, {"fieldName": "BMDM", "dataType": "VARCHAR2"}, {"fieldName": "ZYDM", "dataType": "VARCHAR2"}, {"fieldName": "SFXMDM", "dataType": "VARCHAR2"}, {"fieldName": "YJJE", "dataType": "VARCHAR2"}, {"fieldName": "SFZH", "dataType": "VARCHAR2"}, {"fieldName": "LXND", "dataType": "VARCHAR2"}, {"fieldName": "BMMC", "dataType": "VARCHAR2"}, {"fieldName": "SJJE", "dataType": "VARCHAR2"}, {"fieldName": "JMJE", "dataType": "VARCHAR2"}, {"fieldName": "DKJE", "dataType": "VARCHAR2"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "RXND", "dataType": "VARCHAR2"}, {"fieldName": "KSH", "dataType": "VARCHAR2"}, {"fieldName": "XM", "dataType": "VARCHAR2"}, {"fieldName": "ZYMC", "dataType": "VARCHAR2"}, {"fieldName": "SFQJMC", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["SFQJMC", "LXND", "KSH", "ZYDM", "DKJE", "YJJE", "SFZH", "TFJE", "BMDM", "XH", "SFXMDM", "SJJE", "QFJE", "XM", "SFQJDM", "TIMESTAMPS", "RXND", "SFXMMC", "JMJE", "BMMC", "ZYMC"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:36:37.000+00:00", "updatedAt": "2024-12-21T08:36:37.000+00:00", "id": 27, "name": "学生惩处子类表", "bizName": "XSCCZLB", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.XSCCZLB", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "违纪简况", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "WJJK", "description": null, "fieldName": "WJJK"}, {"name": "处分状态码", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "CFZTM", "description": null, "fieldName": "CFZTM"}, {"name": "处理部门", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "CLBM", "description": null, "fieldName": "CLBM"}, {"name": "处分名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "CFMCM", "description": null, "fieldName": "CFMCM"}, {"name": "处分原因", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "CFYY", "description": null, "fieldName": "CFYY"}, {"name": "处分给与人", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "CFGYR", "description": null, "fieldName": "CFGYR"}, {"name": "违纪日期", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "WJRQ", "description": null, "fieldName": "WJRQ"}, {"name": "处分撤消日期", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "CFCXRQ", "description": null, "fieldName": "CFCXRQ"}, {"name": "处分日期", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "CFRQ", "description": null, "fieldName": "CFRQ"}], "measures": [], "fields": [{"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "WJJK", "dataType": "VARCHAR2"}, {"fieldName": "WJLBM", "dataType": "VARCHAR2"}, {"fieldName": "XNXQ", "dataType": "VARCHAR2"}, {"fieldName": "CFZTM", "dataType": "VARCHAR2"}, {"fieldName": "CLBM", "dataType": "VARCHAR2"}, {"fieldName": "CFMCM", "dataType": "VARCHAR2"}, {"fieldName": "CFYY", "dataType": "VARCHAR2"}, {"fieldName": "CFGYR", "dataType": "VARCHAR2"}, {"fieldName": "CFCXWH", "dataType": "VARCHAR2"}, {"fieldName": "SWHSYJL", "dataType": "VARCHAR2"}, {"fieldName": "CFWH", "dataType": "VARCHAR2"}, {"fieldName": "SWHSYRQ", "dataType": "VARCHAR2"}, {"fieldName": "WJRQ", "dataType": "VARCHAR2"}, {"fieldName": "SSRQ", "dataType": "VARCHAR2"}, {"fieldName": "CFCXRQ", "dataType": "VARCHAR2"}, {"fieldName": "CFRQ", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["CFZTM", "CFMCM", "XNXQ", "CFCXWH", "WJLBM", "CLBM", "CFWH", "CFGYR", "XH", "SWHSYRQ", "SWHSYJL", "TIMESTAMPS", "SSRQ", "CFCXRQ", "WJRQ", "WJJK", "CFYY", "CFRQ"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:51:42.000+00:00", "updatedAt": "2024-12-21T08:51:42.000+00:00", "id": 31, "name": "宿舍晚归未归信息", "bizName": "SSWGWGXX", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.SSWGWGXX", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "违纪方式", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "WJFS", "description": null, "fieldName": "WJFS"}, {"name": "进出时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "JCSJ", "description": null, "fieldName": "JCSJ"}, {"name": "应归宿时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "YGSSJ", "description": null, "fieldName": "YGSSJ"}, {"name": "进出方向", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JCFX", "description": null, "fieldName": "JCFX"}], "measures": [], "fields": [{"fieldName": "DQBZ", "dataType": "VARCHAR2"}, {"fieldName": "WJFS", "dataType": "VARCHAR2"}, {"fieldName": "JCSJ", "dataType": "DATE"}, {"fieldName": "YGSSJ", "dataType": "DATE"}, {"fieldName": "WYBS", "dataType": "VARCHAR2"}, {"fieldName": "JCFX", "dataType": "VARCHAR2"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["XH", "JCSJ", "DQBZ", "WJFS", "TIMESTAMPS", "JCFX", "YGSSJ", "WYBS"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:52:57.000+00:00", "updatedAt": "2024-12-21T08:52:57.000+00:00", "id": 32, "name": "社区-门禁信息", "bizName": "SQ_MJXX", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.SQ_MJXX", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "进出方向", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JCFX", "description": null, "fieldName": "JCFX"}, {"name": "当前标志", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "DQBZ", "description": null, "fieldName": "DQBZ"}, {"name": "进出时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "JCSJ", "description": null, "fieldName": "JCSJ"}], "measures": [], "fields": [{"fieldName": "JCFX", "dataType": "VARCHAR2"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "DQBZ", "dataType": "VARCHAR2"}, {"fieldName": "JCSJ", "dataType": "DATE"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["XH", "JCSJ", "DQBZ", "TIMESTAMPS", "JCFX"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T08:55:19.000+00:00", "updatedAt": "2024-12-21T08:55:19.000+00:00", "id": 33, "name": "图书馆门禁通过信息", "bizName": "TSGMJTGXX", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.TSGMJTGXX", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "闸机名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZJMC", "description": null, "fieldName": "ZJMC"}, {"name": "图书馆", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "TSG", "description": null, "fieldName": "TSG"}, {"name": "方向", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "FX", "description": null, "fieldName": "FX"}, {"name": "通过时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "TGSJ", "description": null, "fieldName": "TGSJ"}, {"name": "刷卡方式", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SKFS", "description": null, "fieldName": "SKFS"}], "measures": [], "fields": [{"fieldName": "ZJBH", "dataType": "VARCHAR2"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "XM", "dataType": "VARCHAR2"}, {"fieldName": "ZJMC", "dataType": "VARCHAR2"}, {"fieldName": "LSH", "dataType": "VARCHAR2"}, {"fieldName": "TSG", "dataType": "VARCHAR2"}, {"fieldName": "XSLB", "dataType": "VARCHAR2"}, {"fieldName": "FX", "dataType": "VARCHAR2"}, {"fieldName": "TGSJ", "dataType": "DATE"}, {"fieldName": "SKFS", "dataType": "VARCHAR2"}, {"fieldName": "SSBMMC", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["XH", "LSH", "FX", "SSBMMC", "SKFS", "XM", "XSLB", "TIMESTAMPS", "TSG", "ZJBH", "ZJMC", "TGSJ"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:28:19.000+00:00", "updatedAt": "2024-12-21T09:30:03.000+00:00", "id": 36, "name": "学生请假信息", "bizName": "QJXX", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.QJXX", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "开始时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "KSSJ", "description": null, "fieldName": "KSSJ"}, {"name": "紧急联系人电话", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JJLXRDH", "description": null, "fieldName": "JJLXRDH"}, {"name": "销假或回校时间", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "XJSJ", "description": null, "fieldName": "XJSJ"}, {"name": "代销假人员姓名", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "DXJRXM", "description": null, "fieldName": "DXJRXM"}, {"name": "是否离校", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFLX", "description": null, "fieldName": "SFLX"}, {"name": "结束时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "JSSJ", "description": null, "fieldName": "JSSJ"}, {"name": "请假时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "QJSJ", "description": null, "fieldName": "QJSJ"}, {"name": "请假状态", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "QJZT", "description": null, "fieldName": "QJZT"}, {"name": "请假内容", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "QJNR", "description": null, "fieldName": "QJNR"}, {"name": "请假类型", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "QJLX", "description": null, "fieldName": "QJLX"}], "measures": [], "fields": [{"fieldName": "KSSJ", "dataType": "VARCHAR2"}, {"fieldName": "DXJRID", "dataType": "VARCHAR2"}, {"fieldName": "JJLXRDH", "dataType": "VARCHAR2"}, {"fieldName": "BJMC", "dataType": "VARCHAR2"}, {"fieldName": "XJSJ", "dataType": "VARCHAR2"}, {"fieldName": "FDYXM", "dataType": "VARCHAR2"}, {"fieldName": "YXMC", "dataType": "VARCHAR2"}, {"fieldName": "QJRYID", "dataType": "VARCHAR2"}, {"fieldName": "NJ", "dataType": "VARCHAR2"}, {"fieldName": "FDYID", "dataType": "VARCHAR2"}, {"fieldName": "DXJRXM", "dataType": "VARCHAR2"}, {"fieldName": "SFLX", "dataType": "VARCHAR2"}, {"fieldName": "JSSJ", "dataType": "VARCHAR2"}, {"fieldName": "WYBS", "dataType": "VARCHAR2"}, {"fieldName": "QJSJ", "dataType": "VARCHAR2"}, {"fieldName": "XM", "dataType": "VARCHAR2"}, {"fieldName": "QJZT", "dataType": "VARCHAR2"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "XB", "dataType": "VARCHAR2"}, {"fieldName": "QJNR", "dataType": "VARCHAR2"}, {"fieldName": "QJLX", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": [], "alias": null, "fullPath": null, "ext": null, "fieldList": ["XJSJ", "QJLX", "JJLXRDH", "BJMC", "JSSJ", "FDYXM", "QJRYID", "XB", "FDYID", "XH", "DXJRID", "DXJRXM", "QJSJ", "XM", "QJZT", "QJNR", "TIMESTAMPS", "SFLX", "WYBS", "KSSJ", "YXMC", "NJ"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:32:06.000+00:00", "updatedAt": "2024-12-21T09:32:06.000+00:00", "id": 37, "name": "学籍异动数据类", "bizName": "XJYDSJL", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.XJYDSJL", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "异动类型名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "YDLXMC", "description": null, "fieldName": "YDLXMC"}, {"name": "异动原因", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "YDYY", "description": null, "fieldName": "YDYY"}, {"name": "异动时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "YDSJ", "description": null, "fieldName": "YDSJ"}, {"name": "fields的可读文本", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "FIELDS_TEXT", "description": null, "fieldName": "FIELDS_TEXT"}], "measures": [], "fields": [{"fieldName": "XM", "dataType": "VARCHAR2"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "YDLXMC", "dataType": "VARCHAR2"}, {"fieldName": "YDYYM", "dataType": "VARCHAR2"}, {"fieldName": "YDYY", "dataType": "VARCHAR2"}, {"fieldName": "FIELDS", "dataType": "CLOB"}, {"fieldName": "XN", "dataType": "VARCHAR2"}, {"fieldName": "YDLXDM", "dataType": "VARCHAR2"}, {"fieldName": "YDSJ", "dataType": "VARCHAR2"}, {"fieldName": "FIELDS_TEXT", "dataType": "CLOB"}, {"fieldName": "CLWH", "dataType": "VARCHAR2"}, {"fieldName": "LSH", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["LSH", "FIELDS", "YDLXDM", "YDYY", "FIELDS_TEXT", "YDYYM", "CLWH", "YDSJ", "YDLXMC", "XH", "XM", "XN", "TIMESTAMPS"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:35:37.000+00:00", "updatedAt": "2024-12-21T09:35:37.000+00:00", "id": 38, "name": "学生奖励子类表", "bizName": "XSJLZLB", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.XSJLZLB", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}, {"name": "奖励级别码", "type": "foreign", "bizName": "JLJBM", "isCreateDimension": 1, "fieldName": "JLJBM"}, {"name": "奖励等级码", "type": "foreign", "bizName": "JLDJM", "isCreateDimension": 1, "fieldName": "JLDJM"}], "dimensions": [{"name": "奖励名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JLMC", "description": null, "fieldName": "JLMC"}, {"name": "获奖时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "HJSJ", "description": null, "fieldName": "HJSJ"}, {"name": "获奖学期", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "HJXQ", "description": null, "fieldName": "HJXQ"}, {"name": "颁奖单位", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "BJDW", "description": null, "fieldName": "BJDW"}, {"name": "奖励原因", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JLYY", "description": null, "fieldName": "JLYY"}, {"name": "获奖学年度", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "HJXND", "description": null, "fieldName": "HJXND"}], "measures": [{"name": "奖励金额", "agg": null, "expr": "JLJE", "bizName": "XSJLZLB_JLJE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "JLJE"}], "fields": [{"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "JLMC", "dataType": "VARCHAR2"}, {"fieldName": "HJSJ", "dataType": "VARCHAR2"}, {"fieldName": "HJXQ", "dataType": "VARCHAR2"}, {"fieldName": "JLFSM", "dataType": "VARCHAR2"}, {"fieldName": "BJDW", "dataType": "VARCHAR2"}, {"fieldName": "JLJE", "dataType": "NUMBER"}, {"fieldName": "JLJBM", "dataType": "VARCHAR2"}, {"fieldName": "JLLXM", "dataType": "VARCHAR2"}, {"fieldName": "HJLBM", "dataType": "VARCHAR2"}, {"fieldName": "JLYY", "dataType": "VARCHAR2"}, {"fieldName": "HJXND", "dataType": "VARCHAR2"}, {"fieldName": "JLWH", "dataType": "VARCHAR2"}, {"fieldName": "JLDJM", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["JLYY", "BJDW", "JLJBM", "JLFSM", "JLLXM", "HJLBM", "XH", "JLWH", "JLDJM", "HJXND", "HJSJ", "TIMESTAMPS", "JLMC", "HJXQ", "JLJE"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:38:49.000+00:00", "updatedAt": "2024-12-21T09:38:49.000+00:00", "id": 39, "name": "学生到梦空间", "bizName": "DMKJ", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.DMKJ", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [], "measures": [{"name": "积分总和", "agg": "sum", "expr": "JFZH", "bizName": "DMKJ_JFZH", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "JFZH"}, {"name": "小时总和", "agg": "sum", "expr": "XSZH", "bizName": "DMKJ_XSZH", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "XSZH"}, {"name": "转换后学分总和", "agg": "sum", "expr": "ZHHXFZH", "bizName": "DMKJ_ZHHXFZH", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "ZHHXFZH"}], "fields": [{"fieldName": "KJCXXF_XS", "dataType": "VARCHAR2"}, {"fieldName": "WTHD_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "ZYGY_XS", "dataType": "VARCHAR2"}, {"fieldName": "XM", "dataType": "VARCHAR2"}, {"fieldName": "XFSX", "dataType": "VARCHAR2"}, {"fieldName": "CXCYXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "NJ", "dataType": "VARCHAR2"}, {"fieldName": "WTHDXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "JZJF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "CXCYXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "ID", "dataType": "VARCHAR2"}, {"fieldName": "SXCZXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "SJXXXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "GZLLXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "STHDXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "JFZH", "dataType": "VARCHAR2"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "CYKSSJ", "dataType": "VARCHAR2"}, {"fieldName": "RZJLXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "SJXXXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "JZJF_JF", "dataType": "VARCHAR2"}, {"fieldName": "CYJSSJ", "dataType": "VARCHAR2"}, {"fieldName": "WTHD_JF", "dataType": "VARCHAR2"}, {"fieldName": "JNTCXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "ZYGYXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "GZLLXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "JNTCXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "BJ", "dataType": "VARCHAR2"}, {"fieldName": "CYSC_F", "dataType": "VARCHAR2"}, {"fieldName": "ZYGYXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "KJCX_JF", "dataType": "VARCHAR2"}, {"fieldName": "KJCX_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "RZJLXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "XY", "dataType": "VARCHAR2"}, {"fieldName": "WTHDXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "RZJL_XS", "dataType": "VARCHAR2"}, {"fieldName": "STHDXF_JFZHHXF", "dataType": "VARCHAR2"}, {"fieldName": "XSZH", "dataType": "VARCHAR2"}, {"fieldName": "ZHHXFZH", "dataType": "VARCHAR2"}, {"fieldName": "SXCZXF_JF", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": null, "alias": null, "fullPath": null, "ext": null, "fieldList": ["XSZH", "XY", "WTHD_JFZHHXF", "SJXXXF_JFZHHXF", "ZYGYXF_JFZHHXF", "CXCYXF_JF", "JFZH", "BJ", "CYJSSJ", "JZJF_JFZHHXF", "WTHD_JF", "KJCXXF_XS", "WTHDXF_JF", "KJCX_JF", "ZYGYXF_JF", "CXCYXF_JFZHHXF", "TIMESTAMPS", "RZJL_XS", "JZJF_JF", "ID", "ZHHXFZH", "SXCZXF_JFZHHXF", "STHDXF_JF", "JNTCXF_JFZHHXF", "JNTCXF_JF", "KJCX_JFZHHXF", "ZYGY_XS", "CYKSSJ", "CYSC_F", "RZJLXF_JF", "STHDXF_JFZHHXF", "XH", "RZJLXF_JFZHHXF", "XM", "SXCZXF_JF", "SJXXXF_JF", "XFSX", "WTHDXF_JFZHHXF", "NJ", "GZLLXF_JF", "GZLLXF_JFZHHXF"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:40:58.000+00:00", "updatedAt": "2024-12-21T09:42:58.000+00:00", "id": 40, "name": "学生综合测评成绩信息", "bizName": "ZHCPCJXX", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.ZHCPCJXX", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "学年", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 0, "bizName": "XN", "description": null, "fieldName": "XN"}, {"name": "学期", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "XQ", "description": null, "fieldName": "XQ"}, {"name": "班级排名", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "BJPM", "description": null, "fieldName": "BJPM"}, {"name": "专业排名", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZYPM", "description": null, "fieldName": "ZYPM"}], "measures": [{"name": "成绩", "agg": "sum", "expr": "CJ", "bizName": "ZHCPCJXX_CJ", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "CJ"}], "fields": [{"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "XN", "dataType": "VARCHAR2"}, {"fieldName": "XQ", "dataType": "VARCHAR2"}, {"fieldName": "CJ", "dataType": "NUMBER"}, {"fieldName": "BJPM", "dataType": "VARCHAR2"}, {"fieldName": "ZYPM", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": [], "alias": null, "fullPath": null, "ext": null, "fieldList": ["XH", "XN", "ZYPM", "CJ", "TIMESTAMPS", "XQ", "BJPM"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-21T09:44:11.000+00:00", "updatedAt": "2024-12-21T09:44:21.000+00:00", "id": 42, "name": "学生综合测评指标成绩表", "bizName": "ZHCPZBCJB", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "table_query", "sqlQuery": "", "tableQuery": "KUST.ZHCPZBCJB", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 1, "fieldName": "XH"}], "dimensions": [{"name": "测评类型名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "CPLXMC", "description": null, "fieldName": "CPLXMC"}, {"name": "学年学期", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "XNXQ", "description": null, "fieldName": "XNXQ"}], "measures": [{"name": "测评分数", "agg": null, "expr": "PCFS", "bizName": "ZHCPZBCJB_PCFS", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "PCFS"}], "fields": [{"fieldName": "CPLXDM", "dataType": "VARCHAR2"}, {"fieldName": "CPLXMC", "dataType": "VARCHAR2"}, {"fieldName": "PCFS", "dataType": "NUMBER"}, {"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "XNXQ", "dataType": "VARCHAR2"}, {"fieldName": "TIMESTAMPS", "dataType": "DATE"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": [], "alias": null, "fullPath": null, "ext": null, "fieldList": ["CPLXMC", "XH", "PCFS", "XNXQ", "TIMESTAMPS", "CPLXDM"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T06:45:07.000+00:00", "updatedAt": "2024-12-23T06:47:20.000+00:00", "id": 54, "name": "图书借阅信息表", "bizName": "XSTSJYXXB", "description": "学生图书借阅信息", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "sql_query", "sqlQuery": "select \r\n  t1.XGH as XH,t2.TSMC,t2.HSD,t2.JSD,t2.JYSJ,t2.SJGHSJ\r\nfrom kust.DZJBSJZLB t1 \r\nleft join kust.TSLSJYSJZLB t2 on t1.JSZH = t2.JSZH\r\n", "tableQuery": "", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 0, "fieldName": "XH"}], "dimensions": [{"name": "图书名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "TSMC", "description": null, "fieldName": "TSMC"}, {"name": "还书地点", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "HSD", "description": null, "fieldName": "HSD"}, {"name": "借书地点", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JSD", "description": null, "fieldName": "JSD"}, {"name": "借阅时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "JYSJ", "description": null, "fieldName": "JYSJ"}, {"name": "实际归还时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "SJGHSJ", "description": null, "fieldName": "SJGHSJ"}], "measures": [], "fields": [{"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "TSMC", "dataType": "VARCHAR2"}, {"fieldName": "HSD", "dataType": "VARCHAR2"}, {"fieldName": "JSD", "dataType": "VARCHAR2"}, {"fieldName": "JYSJ", "dataType": "VARCHAR2"}, {"fieldName": "SJGHSJ", "dataType": "VARCHAR2"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": [], "alias": null, "fullPath": null, "ext": null, "fieldList": ["XH", "TSMC", "JYSJ", "HSD", "SJGHSJ", "JSD"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T08:23:34.000+00:00", "updatedAt": "2024-12-23T08:24:38.000+00:00", "id": 55, "name": "学生活动签到情况表", "bizName": "XSHDQDQKB", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "sql_query", "sqlQuery": "select t1.XH,t1.QDSJ,t1.QDJG,t2.BT,t2.NR,t1.FZMC\r\nfrom kust.XSQDXX t1\r\njoin kust.QDNRFBXX t2 on t1.QDID = t2.WYBS", "tableQuery": "", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 0, "fieldName": "XH"}], "dimensions": [{"name": "签到时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "QDSJ", "description": null, "fieldName": "QDSJ"}, {"name": "签到结果", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "QDJG", "description": null, "fieldName": "QDJG"}, {"name": "签到标题", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "BT", "description": null, "fieldName": "BT"}, {"name": "签到内容", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "NR", "description": null, "fieldName": "NR"}, {"name": "班级、团队、分组名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "FZMC", "description": null, "fieldName": "FZMC"}], "measures": [], "fields": [{"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "QDSJ", "dataType": "VARCHAR2"}, {"fieldName": "QDJG", "dataType": "VARCHAR2"}, {"fieldName": "BT", "dataType": "VARCHAR2"}, {"fieldName": "NR", "dataType": "VARCHAR2"}, {"fieldName": "FZMC", "dataType": "VARCHAR2"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": [], "alias": null, "fullPath": null, "ext": null, "fieldList": ["XH", "BT", "NR", "QDJG", "FZMC", "QDSJ"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-23T09:04:46.000+00:00", "updatedAt": "2024-12-23T09:07:36.000+00:00", "id": 56, "name": "学生考试成绩信息表", "bizName": "XSCJXXB", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "sql_query", "sqlQuery": "select t1.XH,t1.<PERSON>NX<PERSON>,t1.<PERSON><PERSON><PERSON><PERSON>,t2.<PERSON><PERSON>,t2.X<PERSON>XF,t2.ZXS KCZXS,t2.MZXS KCMZXS,t2.KCFZR,t3.JD,t3.<PERSON><PERSON>J,t3.<PERSON><PERSON>J,t3.<PERSON><PERSON><PERSON>,t3.PJXFJD,t3.<PERSON><PERSON><PERSON><PERSON><PERSON>,t3.FSLKSCJ,t3.KSRQ,t3.SFJG\r\nfrom kust.XKSJLB t1\r\njoin kust.KCSJLB t2 on t1.KCH = t2.KCH\r\njoin kust.CJZLB t3 on t1.XH = t3.XH and t1.KCH = t3.KCH", "tableQuery": "", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 0, "fieldName": "XH"}], "dimensions": [{"name": "学年学期", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "XNXQ", "description": null, "fieldName": "XNXQ"}, {"name": "是否是重修生", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFCXS", "description": null, "fieldName": "SFCXS"}, {"name": "课程名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "KCMC", "description": null, "fieldName": "KCMC"}, {"name": "课程学分", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "KCXF", "description": null, "fieldName": "KCXF"}, {"name": "课程总学时", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "KCZXS", "description": null, "fieldName": "KCZXS"}, {"name": "课程周学时", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "KCMZXS", "description": null, "fieldName": "KCMZXS"}, {"name": "课程负责人", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "KCFZR", "description": null, "fieldName": "KCFZR"}, {"name": "课程成绩绩点", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "JD", "description": null, "fieldName": "JD"}, {"name": "平时成绩", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "PSCJ", "description": null, "fieldName": "PSCJ"}, {"name": "课程成绩", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "KCCJ", "description": null, "fieldName": "KCCJ"}, {"name": "总成绩", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZCJ", "description": null, "fieldName": "ZCJ"}, {"name": "平均学分绩点", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "PJXFJD", "description": null, "fieldName": "PJXFJD"}, {"name": "等级类考试成绩", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "DJLKSCJ", "description": null, "fieldName": "DJLKSCJ"}, {"name": "分数类考试成绩", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "FSLKSCJ", "description": null, "fieldName": "FSLKSCJ"}, {"name": "考试日期", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "KSRQ", "description": null, "fieldName": "KSRQ"}, {"name": "是否及格", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SFJG", "description": null, "fieldName": "SFJG"}], "measures": [], "fields": [{"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "XNXQ", "dataType": "VARCHAR2"}, {"fieldName": "SFCXS", "dataType": "VARCHAR2"}, {"fieldName": "KCMC", "dataType": "VARCHAR2"}, {"fieldName": "KCXF", "dataType": "NUMBER"}, {"fieldName": "KCZXS", "dataType": "VARCHAR2"}, {"fieldName": "KCMZXS", "dataType": "NUMBER"}, {"fieldName": "KCFZR", "dataType": "VARCHAR2"}, {"fieldName": "JD", "dataType": "NUMBER"}, {"fieldName": "PSCJ", "dataType": "VARCHAR2"}, {"fieldName": "KCCJ", "dataType": "VARCHAR2"}, {"fieldName": "ZCJ", "dataType": "VARCHAR2"}, {"fieldName": "PJXFJD", "dataType": "VARCHAR2"}, {"fieldName": "DJLKSCJ", "dataType": "VARCHAR2"}, {"fieldName": "FSLKSCJ", "dataType": "VARCHAR2"}, {"fieldName": "KSRQ", "dataType": "VARCHAR2"}, {"fieldName": "SFJG", "dataType": "VARCHAR2"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": [], "alias": null, "fullPath": null, "ext": null, "fieldList": ["ZCJ", "KSRQ", "KCXF", "XNXQ", "KCFZR", "FSLKSCJ", "KCMC", "KCMZXS", "PSCJ", "XH", "DJLKSCJ", "SFJG", "SFCXS", "JD", "KCCJ", "KCZXS", "PJXFJD"], "timeDimension": []}, {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-24T01:14:55.000+00:00", "updatedAt": "2024-12-24T01:17:10.000+00:00", "id": 57, "name": "学生消费信息", "bizName": "XSXFXXVIEW", "description": "", "status": 1, "typeEnum": null, "sensitiveLevel": 0, "domainId": 6, "databaseId": 2, "tagObjectId": 0, "modelDetail": {"queryType": "sql_query", "sqlQuery": "select\r\n    t1.XGH XH,t1.<PERSON><PERSON><PERSON>,t1.<PERSON><PERSON><PERSON><PERSON>,t1.KH,t1.<PERSON>N<PERSON><PERSON>,t1.ZHKHRQ,t1.ZHYXRQ,t1.ZHZT,\r\n\t\tt2.ZDZH,t2.JYJE,t2.RZRQSJ,t2.Y<PERSON><PERSON>,\r\n\t\tt3.DZ,t3.SHMC\r\nfrom YKTZHXX t1\r\njoin YKTXFXX t2 on t1.ZH = t2.ZH\r\njoin YKTSHXX t3 on t2.ZDZH = t3.ZH", "tableQuery": "", "identifiers": [{"name": "学号", "type": "primary", "bizName": "XH", "isCreateDimension": 0, "fieldName": "XH"}, {"name": "证件号", "type": "primary", "bizName": "ZJH", "isCreateDimension": 0, "fieldName": "ZJH"}], "dimensions": [{"name": "卡号", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "KH", "description": null, "fieldName": "KH"}, {"name": "账户开户日期", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "ZHKHRQ", "description": null, "fieldName": "ZHKHRQ"}, {"name": "账户有效日期", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "ZHYXRQ", "description": null, "fieldName": "ZHYXRQ"}, {"name": "账户状态标志", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "ZHZT", "description": null, "fieldName": "ZHZT"}, {"name": "入账日期时间", "type": "time", "expr": null, "dateFormat": "yyyy-MM-dd HH:mm", "typeParams": {"isPrimary": "true", "timeGranularity": "day"}, "isCreateDimension": 1, "bizName": "RZRQSJ", "description": null, "fieldName": "RZRQSJ"}, {"name": "用卡次数", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "YKCS", "description": null, "fieldName": "YKCS"}, {"name": "消费地址", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "DZ", "description": null, "fieldName": "DZ"}, {"name": "消费商户名称", "type": "categorical", "expr": null, "dateFormat": "yyyy-MM-dd", "typeParams": null, "isCreateDimension": 1, "bizName": "SHMC", "description": null, "fieldName": "SHMC"}], "measures": [{"name": "账号余额", "agg": "sum", "expr": "ZHYE", "bizName": "XSXFXXVIEW_ZHYE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "ZHYE"}, {"name": "卡内余额", "agg": "sum", "expr": "KNYE", "bizName": "XSXFXXVIEW_KNYE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "KNYE"}, {"name": "交易金额", "agg": "sum", "expr": "JYJE", "bizName": "XSXFXXVIEW_JYJE", "isCreateMetric": 1, "constraint": null, "alias": null, "fieldName": "JYJE"}], "fields": [{"fieldName": "XH", "dataType": "VARCHAR2"}, {"fieldName": "ZJH", "dataType": "VARCHAR2"}, {"fieldName": "ZHYE", "dataType": "VARCHAR2"}, {"fieldName": "KH", "dataType": "VARCHAR2"}, {"fieldName": "KNYE", "dataType": "VARCHAR2"}, {"fieldName": "ZHKHRQ", "dataType": "DATE"}, {"fieldName": "ZHYXRQ", "dataType": "DATE"}, {"fieldName": "ZHZT", "dataType": "VARCHAR2"}, {"fieldName": "ZDZH", "dataType": "VARCHAR2"}, {"fieldName": "JYJE", "dataType": "VARCHAR2"}, {"fieldName": "RZRQSJ", "dataType": "DATE"}, {"fieldName": "YKCS", "dataType": "VARCHAR2"}, {"fieldName": "DZ", "dataType": "VARCHAR2"}, {"fieldName": "SHMC", "dataType": "VARCHAR2"}], "sqlVariables": []}, "depends": null, "filterSql": "", "viewers": [], "viewOrgs": [], "admins": [], "adminOrgs": [], "isOpen": null, "drillDownDimensions": [], "alias": null, "fullPath": null, "ext": null, "fieldList": ["ZHZT", "ZHKHRQ", "JYJE", "YKCS", "KNYE", "SHMC", "XH", "ZHYE", "ZDZH", "RZRQSJ", "DZ", "ZHYXRQ", "ZJH", "KH"], "timeDimension": []}], "dataSetResp": null, "databaseResp": {"createdBy": "admin", "updatedBy": "admin", "createdAt": "2024-12-13T07:07:17.000+00:00", "updatedAt": "2024-12-18T03:40:37.000+00:00", "id": 2, "name": "外网连接昆工oracle数据库", "description": "昆工测试数据库外网连接", "admins": [], "viewers": [], "type": "oracle", "url": "*********************************************", "username": "kust", "password": "iR9k8Mv5KzntGU4BdROb0w==", "database": null, "version": null, "schema": null, "hasPermission": false, "hasUsePermission": false, "hasEditPermission": false, "host": "**************", "port": "11521"}, "queryType": null}, "limit": 1000, "isTranslated": false, "translated": false, "ok": false}