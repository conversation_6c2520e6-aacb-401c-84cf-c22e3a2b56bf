package com.ultravis.bi.controller;

import com.tencent.supersonic.common.pojo.MyPageBase;
import com.ultravis.bi.base.QueryBasePage;
import com.ultravis.bi.dto.DeleteVectorDbCollectionFieldReq;
import com.ultravis.bi.dto.VectorDbPageQuery;
import com.ultravis.bi.dto.VectorDbResp;
import com.ultravis.bi.service.VectorDbService;
import io.milvus.v2.service.collection.response.ListCollectionsResp;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 向量数据库管理
 *
 * @author: lyp
 * @date: 2025/4/27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/chat/bi-milvus/vector-db")
public class VectorDbController {

    private final VectorDbService service;

    /**
     * 获取向量数据库列表
     *
     * @return 结果
     */
    @GetMapping("/collections")
    public ListCollectionsResp getAllVectorDbCollections() {
        return service.getAllVectorDbCollections();
    }

    /**
     * 分页查询向量数据库内容
     *
     * @return 结果
     */
    @GetMapping("/querySemantics")
    public QueryBasePage<VectorDbResp> querySemantics(VectorDbPageQuery request, MyPageBase pageQuery) {
        return service.getVectorDbPage(request, pageQuery);
    }

    @PostMapping("/batchDeleteVectorDbCollectionField")
    public void batchDeleteVectorDbCollectionField(@RequestBody DeleteVectorDbCollectionFieldReq request) {
        service.batchDeleteVectorDbCollectionField(request);
    }
}
