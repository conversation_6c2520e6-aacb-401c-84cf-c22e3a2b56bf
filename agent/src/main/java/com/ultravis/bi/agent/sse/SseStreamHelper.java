package com.ultravis.bi.agent.sse;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;
import java.util.function.Consumer;

/**
 * SseStreamHelper
 *
 * <AUTHOR>
 * @date 2025/5/26
 **/


public class SseStreamHelper {
    private final SseConnectionManager connectionManager;

    public SseStreamHelper() {
        this.connectionManager = new SseConnectionManager();
    }

    /**
     * 创建简单的文本流
     */
    public SseEmitter createTextStream(Consumer<TextStreamSender> streamTask) {
        SseConnection connection = connectionManager.createConnection();

        connectionManager.executeAsync(connection, conn -> {
            TextStreamSender sender = new TextStreamSender(conn);
            streamTask.accept(sender);
            conn.complete();
        });

        return connection.getEmitter();
    }

    /**
     * 创建OpenAI格式的流
     */
    public SseEmitter createOpenAIStream(Consumer<OpenAIStreamSender> streamTask) {
        SseConnection connection = connectionManager.createConnection();
        OpenAISseAdapter adapter = new OpenAISseAdapter();

        connectionManager.executeAsync(connection, conn -> {
            OpenAIStreamSender sender = new OpenAIStreamSender(conn, adapter);
            streamTask.accept(sender);
            adapter.sendCompletion(conn, null, Map.of());
            conn.complete();
        });

        return connection.getEmitter();
    }

    public static class TextStreamSender {
        private final SseConnection connection;

        public TextStreamSender(SseConnection connection) {
            this.connection = connection;
        }

        public boolean send(String eventName, Object data) {
            return connection.sendJson(eventName, data);
        }

        public boolean sendText(String text) {
            return connection.sendRaw(null, text);
        }
    }


    public static class OpenAIStreamSender {
        private final SseConnection connection;
        private final OpenAISseAdapter adapter;

        public OpenAIStreamSender(SseConnection connection, OpenAISseAdapter adapter) {
            this.connection = connection;
            this.adapter = adapter;
        }

        public boolean sendToken(String content, Map<String, Object> metadata) {
            return adapter.sendToken(connection, null, content, metadata);
        }

        public boolean sendError(String message, String type) {
            return adapter.sendError(connection, message, type);
        }
    }
}
