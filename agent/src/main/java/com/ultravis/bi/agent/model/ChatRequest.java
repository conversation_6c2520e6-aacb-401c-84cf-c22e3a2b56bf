package com.ultravis.bi.agent.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * chat request with api
 *
 * <AUTHOR>
 * @date 2025/5/23
 **/


@Getter
@Setter
@Accessors(chain = true)
public class ChatRequest {

    private List<RequestMessage> messages;

    private String chatId;

    private String threadId;

    private String feedback;
}
