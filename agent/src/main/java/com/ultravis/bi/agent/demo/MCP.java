package com.ultravis.bi.agent.demo;

import com.ultravis.bi.agent.llm.LLM;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.transport.stdio.StdioMcpTransport;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.request.ChatRequest;
import dev.langchain4j.model.chat.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.CompiledGraph;
import org.bsc.langgraph4j.GraphStateException;
import org.bsc.langgraph4j.agentexecutor.AgentExecutor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/21
 **/


@Slf4j
public class MCP {
    public static void execute() {
        ChatModel chatModel = LLM.getLlmByType("");
        StdioMcpTransport transport = new StdioMcpTransport.Builder().command(
                        List.of("cmd", "/c", "uv", "tool", "run", "arxiv-mcp-server", "--storage-path",
                                "D:\\tmp\\mcp\\arxiv"))
                // only if you wnt to see the traffic in the log
                .logEvents(true).environment(Map.of()).build();

        DefaultMcpClient mcpClient = new DefaultMcpClient.Builder().transport(transport).build();

        McpToolProvider toolProvider =
                McpToolProvider.builder().mcpClients(List.of(mcpClient)).build();

        // get the MCP tools
        List<ToolSpecification> tools = mcpClient.listTools().stream().toList();

        UserMessage userMessage = UserMessage.from("");
        ChatRequest request =
                ChatRequest.builder().toolSpecifications(tools).messages(userMessage).build();

        ChatResponse response = chatModel.chat(request);
        log.info("response: {}", response);


        AgentExecutor.Builder agentBuilder = AgentExecutor.builder().chatModel(chatModel);

        for (var tool : mcpClient.listTools()) {
            agentBuilder.tool(tool, (toolReq, memoryId) -> mcpClient.executeTool(toolReq));
        }

        CompiledGraph<AgentExecutor.State> graph;
        try {
            graph = agentBuilder.build().compile();
        } catch (GraphStateException e) {
            throw new RuntimeException(e);
        }

        UserMessage message = UserMessage.from("I'm need Agent Review Paper");
        String result = graph.invoke(Map.of("messages", message))
                .flatMap(AgentExecutor.State::finalResponse).orElse("no response");
        log.info("result: {}", result);
    }

    public static void main(String[] args) {
        execute();
    }
}
