package com.ultravis.bi.agent.node;

import com.ultravis.bi.agent.graph.MessageState;
import dev.langchain4j.data.message.UserMessage;
import lombok.extern.slf4j.Slf4j;
import org.bsc.langgraph4j.action.NodeAction;

import java.util.List;
import java.util.Map;

/**
 * human review feedback
 *
 * <AUTHOR>
 * @date 2025/5/23
 **/


@Slf4j
public class HumanReviewFeedbackNode implements NodeAction<MessageState> {
    @Override
    public Map<String, Object> apply(MessageState state) {
        // log.info("human review feedback node");
        String feedback = state.humanFeedback().orElseThrow();
        log.info("review feedback: {}", feedback);
        if ("ACCEPTED".equalsIgnoreCase(feedback)) {
            return Map.of("final_query", state.rewrittenQuery().orElseThrow());
        }
        UserMessage queryText = (UserMessage) state.lastMessage().orElseThrow();

        return Map.of("final_query", queryText.singleText(), "options", List.of(), "event_type",
                "message");
    }
}
