package com.ultravis.bi.agent.utils;

/**
 * json utils
 *
 * <AUTHOR>
 * @date 2025/5/26
 **/


public class JsonUtils {

    private JsonUtils() {
    }

    /**
     * Repair and normalize JSON output**
     *
     * @param content content  String content that may contain <PERSON><PERSON><PERSON>
     * @return String Repaired JSON string, or original content if not JSON
     */
    public static String repairJsonOutput(String content) {
        if (content.startsWith("{") || content.startsWith("[") || content.contains(
                "```json") || content.contains("```JSON")) {
            if (content.startsWith("```json")) {
                content = content.substring(7);
            }
            if (content.startsWith("```JSON")) {
                content = content.substring(7);
            }
            if (content.endsWith("```")) {
                content = content.substring(0, content.length() - 3);
            }
        }
        return content;
    }
}
