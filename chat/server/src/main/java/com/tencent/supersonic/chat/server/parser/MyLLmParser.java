package com.tencent.supersonic.chat.server.parser;

import com.tencent.supersonic.chat.server.agent.Agent;
import com.tencent.supersonic.common.pojo.ChatApp;
import com.tencent.supersonic.common.pojo.ChatModelConfig;
import com.tencent.supersonic.headless.server.utils.ModelConfigHelper;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.provider.ModelProvider;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 调取大模型
 *
 * @author: lyp
 * @date: 2025/4/24
 */
@Service
public class MyLLmParser {
    public static final String APP_KEY = "S2SQL_PARSER";

    public String llmInvoke(Agent agent, String promptStr) {
        ChatApp chatApp = agent.getChatAppConfig().get(APP_KEY);
        if (Objects.isNull(chatApp)) {
            return "";
        }
        UserMessage prompt = UserMessage.from(promptStr);
        ChatModelConfig chatModelConfig = chatApp.getChatModelConfig();
        String apiKey = chatModelConfig.getApiKey();
        String modelName = chatModelConfig.getModelName();
        String baseUrl = chatModelConfig.getBaseUrl();
        ChatModel chatLanguageModel = OpenAiChatModel.builder()
                .apiKey(apiKey)
                .baseUrl(baseUrl)
                .modelName(modelName)
                .build();
        ModelProvider.getChatModel(ModelConfigHelper.getChatModelConfig(chatApp));
        ChatResponse response = chatLanguageModel.chat(prompt);
        return response.aiMessage().text();
    }
}
