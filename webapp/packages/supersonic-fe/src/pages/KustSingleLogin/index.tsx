import React, {useEffect, useState} from "react";
import {getSingleLogin} from "./services";
import {AUTH_TOKEN_KEY} from "@/common/constants";
import {queryCurrentUser} from "@/services/user";
import {ROUTE_AUTH_CODES} from "../../../config/routes";
import {FROM_ROUTER_HISTORY} from "@/services/request";
import {history, useLocation, useModel} from "@umijs/max";
import {Spin} from "antd";
import NoPermission from "@/components/NoPermission";

const SingleLoginPage: React.FC = () => {
  const {initialState = {}, setInitialState} = useModel('@@initialState');
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  const handleLogin = async () => {
    try {
      // 从 URL 中获取 ticket 参数
      const searchParams = new URLSearchParams(location.search);
      const ticket = searchParams.get('ticket') || '';
      const {code, data, msg} = await getSingleLogin(ticket);

      if (code === 200) {
        if(data.indexOf('https://cas.kmust.edu.cn/lyuapServer/') >=0 ) {
          window.location.href = data; // 重定向单点登录地址
          return;
        } else {
          localStorage.setItem(AUTH_TOKEN_KEY, data);

          const {code: queryUserCode, data: queryUserData} = await queryCurrentUser();
          if (queryUserCode === 200) {
            const currentUser = {
              ...queryUserData,
              staffName: queryUserData.staffName || queryUserData.name,
            };

            const authCodes = Array.isArray(initialState?.authCodes)
                ? initialState?.authCodes
                : [];

            if (queryUserData.superAdmin) {
              authCodes.push(ROUTE_AUTH_CODES.SYSTEM_ADMIN);
            }

            await setInitialState({...initialState, currentUser, authCodes});
            localStorage.removeItem(FROM_ROUTER_HISTORY);
            history.push('/chat');
          } else {
            console.log('获取用户信息失败');
          }
        }
      } else {
        console.log(msg || '单点登录失败');
      }
    } catch (error) {
      console.log('登录过程发生错误：' + error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleLogin();
  }, []);

  return (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh'
        }}>
        <Spin spinning={loading}>
          {loading && <div style={{padding: 20, border: '1px solid #ddd'}}>
            单点登录中...
          </div>}
          {!loading && <NoPermission text="您没有权限使用该功能！" showBtn={false}/>}
        </Spin>
      </div>
    </div>
  );
}

export default SingleLoginPage;
