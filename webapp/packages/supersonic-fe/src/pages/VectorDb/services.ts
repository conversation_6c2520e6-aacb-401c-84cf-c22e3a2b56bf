import request from 'umi-request';

export function getVectorDbCollections(): Promise<any> {
  return request(`${process.env.CHAT_API_BASE_URL}bi-milvus/vector-db/collections`, {
    method: 'GET',
  });
}

export function querySemantics(params: any): Promise<any> {
  return request(`${process.env.CHAT_API_BASE_URL}bi-milvus/vector-db/querySemantics`, {
    method: 'GET',
    params,
  });
}

export function batchDeleteVectorDbCollectionField(params: any): Promise<any> {
  return request(`${process.env.CHAT_API_BASE_URL}bi-milvus/vector-db/batchDeleteVectorDbCollectionField`, {
    method: 'POST',
    data: params,
  });
}
