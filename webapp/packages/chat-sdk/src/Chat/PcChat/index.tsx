import {getCurrentUserInfo, isMobile, jsonParse, setToken, updateMessageContainerScroll, uuid} from '../../utils/utils';
import {
    forwardRef,
    ForwardRefRenderFunction,
    useCallback,
    useEffect,
    useImperativeHandle,
    useRef,
    useState,
} from 'react';

import {Avatar, ConfigProvider, Dropdown, MenuProps, Modal, Space} from 'antd';
import {CaretDownOutlined, DownOutlined} from '@ant-design/icons';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import styles from './style.module.less';

import {useThrottleFn} from 'ahooks';
import classNames from 'classnames';
import {cloneDeep, isBoolean} from 'lodash';

import Conversation from '../Conversation';
import ChatFooter from '../ChatFooter';
import AgentList from '../AgentList';
import MobileAgents from '../MobileAgents';
import ShowCase from '../../ShowCase';
import MessageContainer from '../MessageContainer';
import NoPermission from "../../components/NoPermission";
import PlanModeMessageContainer from "../PlanModeMessageContainer";
import AgentTip from "../components/AgentTip";
import NextChatMessageContainer from "../NextChatMessageContainer";

import {AgentType, ConversationDetailType, MessageItem, MessageTypeEnum} from '../type';
import {HistoryMsgItemType, MsgDataType, SendMsgParamsType} from '../../common/type';
import {queryAgentList} from '../service';
import {getHistoryMsg} from '../../service';

import LogoImg from '/public/logo.png'
import HistoryChatImg from '/public/images/history_chat.png'
import MenuShowImg from '/public/images/menu_show.png'
import MenuHideImg from '/public/images/menu_hide.png'
import NewChatImg from '/public/images/new_chat.png'
import UserDefaultImg from "/public/images/user_default.png"


dayjs.locale('zh-cn');

type Props = {
    token?: string;
    agentIds?: number[];
    initialAgentId?: number;
    chatVisible?: boolean;
    noInput?: boolean;
    isDeveloper?: boolean;
    integrateSystem?: string;
    isCopilot?: boolean;
    openSimpleMode: boolean;
    onCurrentAgentChange?: (agent?: AgentType) => void;
    onReportMsgEvent?: (msg: string, valid: boolean) => void;
    onClickAccessToken: () => void;
    onChangePassword: () => void;
    onClickLoginOut: () => void;
};

const PcChat: ForwardRefRenderFunction<any, Props> = (
    {
        token,
        agentIds,
        initialAgentId,
        chatVisible,
        noInput,
        isDeveloper,
        integrateSystem,
        isCopilot,
        openSimpleMode,
        onCurrentAgentChange,
        onReportMsgEvent,
        onClickAccessToken,
        onChangePassword,
        onClickLoginOut
    },
    ref
) => {

    const [messageList, setMessageList] = useState<MessageItem[]>([]);
    const [inputMsg, setInputMsg] = useState('');
    const [pageNo, setPageNo] = useState(1);
    const [hasNextPage, setHasNextPage] = useState(false);
    const [historyInited, setHistoryInited] = useState(false);
    const [currentConversation, setCurrentConversation] = useState<ConversationDetailType | undefined>(isMobile ? {
        //chatId: 0,
        chatId: 5,
        chatName: '问答'
    } : undefined);
    const [historyVisible, setHistoryVisible] = useState(false);
    const [agentList, setAgentList] = useState<AgentType[]>([]);
    const [currentAgent, setCurrentAgent] = useState<AgentType>();
    const [mobileAgentsVisible, setMobileAgentsVisible] = useState(false);
    const [agentListVisible, setAgentListVisible] = useState(false);
    const [showCaseVisible, setShowCaseVisible] = useState(false);

    const [leftBlockVisible, setLeftBlockVisible] = useState<boolean>(true);
    const [menuVisible, setMenuVisible] = useState<boolean>(false);
    const [isSimpleMode, setIsSimpleMode] = useState<boolean>(false);
    const [isDebugMode, setIsDebugMode] = useState<boolean>(true);
    const [currentUserInfo, setCurrentUserInfo] = useState<any>();
    const [isSuperAdmin, setIsSuperAdmin] = useState<boolean>(false);

    const [chatPerformanceMode, setChatPerformanceMode] = useState<'speed-first' | 'quality-first'>('quality-first');
    const [agentChatMode, setAgentChatMode] = useState<'explore' | 'plan' | 'nextChat'>('explore');
    const [userInputMsg, setUserInputMsg] = useState<string>("");
    const [showAgentTip, setShowAgentTip] = useState<boolean>(false);
    const conversationRef = useRef<any>();
    const chatFooterRef = useRef<any>();

    useEffect(() => {
        const userInfo = getCurrentUserInfo();
        setCurrentUserInfo(userInfo);
        setIsSuperAdmin(userInfo.superAdmin);
    }, [])

    useImperativeHandle(ref, () => ({
        sendCopilotMsg,
    }));

    const sendCopilotMsg = (params: SendMsgParamsType) => {
        setAgentListVisible(false);
        const {agentId, msg, modelId} = params;
        if (currentAgent?.id !== agentId) {
            setMessageList([]);
            const agent = agentList.find(item => item.id === agentId) || ({} as AgentType);
            updateCurrentAgent({...agent, initialSendMsgParams: params});
        } else {
            onSendMsg(msg, messageList, modelId, params);
        }
    };

    const updateAgentConfigMode = (agent: AgentType) => {
        const toolConfig = jsonParse(agent?.toolConfig, {});
        const {simpleMode, debugMode} = toolConfig;
        const userInfo = getCurrentUserInfo();
        if (isBoolean(simpleMode)) {
            let flag = simpleMode || openSimpleMode
            if (userInfo.superAdmin) {
                flag = false
            }
            setIsSimpleMode(flag);
        } else {
            setIsSimpleMode(false);
        }
        if (isBoolean(debugMode)) {
            setIsDebugMode(debugMode);
        } else {
            setIsDebugMode(true);
        }
    };

    const updateCurrentAgent = (agent?: AgentType) => {
        setCurrentAgent(agent);
        onCurrentAgentChange?.(agent);
        localStorage.setItem('AGENT_ID', `${agent?.id}`);
        if (agent) {
            updateAgentConfigMode(agent);
        }
        if (!isCopilot) {
            window.history.replaceState({}, '', `${window.location.pathname}?agentId=${agent?.id}`);
        }
    };

    const initAgentList = async () => {
        const res = await queryAgentList();
        const agentListValue = (res.data || []).filter(
            item => item.status === 1 && (agentIds === undefined || agentIds.includes(item.id))
        );
        setAgentList(agentListValue);
        if (agentListValue.length > 0) {
            const agentId = initialAgentId || localStorage.getItem('AGENT_ID');
            if (agentId) {
                const agent = agentListValue.find(item => item.id === +agentId);
                updateCurrentAgent(agent || agentListValue[0]);
            } else {
                updateCurrentAgent(agentListValue[0]);
            }
        }
    };

    useEffect(() => {
        initAgentList();
        const mode = isMobile ? true : isSimpleMode
        setIsSimpleMode(mode)
    }, []);

    useEffect(() => {
        if (token) {
            setToken(token);
        }
    }, [token]);

    useEffect(() => {
        if (chatVisible) {
            inputFocus();
            updateMessageContainerScroll();
        }
    }, [chatVisible]);

    useEffect(() => {
        if (!currentConversation) {
            return;
        }
        const {initialMsgParams, isAdd} = currentConversation;
        if (isAdd) {
            inputFocus();
            if (initialMsgParams) {
                onSendMsg(initialMsgParams.msg, [], initialMsgParams.modelId, initialMsgParams);
                return;
            }
            sendHelloRsp();
            return;
        }
        updateHistoryMsg(1);
        setPageNo(1);
    }, [currentConversation]);

    useEffect(() => {
        if (historyInited) {
            const messageContainerEle = document.getElementById('messageContainer');
            messageContainerEle?.addEventListener('scroll', handleScroll);
        }
        return () => {
            const messageContainerEle = document.getElementById('messageContainer');
            messageContainerEle?.removeEventListener('scroll', handleScroll);
        };
    }, [historyInited]);

    const sendHelloRsp = (agent?: AgentType) => {
        if (noInput) {
            return;
        }
        setMessageList([
            {
                id: uuid(),
                type: MessageTypeEnum.AGENT_LIST,
                msg: agent?.name || currentAgent?.name || agentList?.[0]?.name,
            },
        ]);
    };

    const convertHistoryMsg = (list: HistoryMsgItemType[]) => {
        return list.map((item: HistoryMsgItemType) => ({
            id: item.questionId,
            questionId: item.questionId,
            type: MessageTypeEnum.QUESTION,
            msg: item.queryText,
            parseInfos: item.parseInfos,
            parseTimeCost: item.parseTimeCost,
            msgData: {...(item.queryResult || {}), similarQueries: item.similarQueries},
            score: item.score,
            agentId: currentAgent?.id,
        }));
    };

    const updateHistoryMsg = async (page: number) => {
        const res = await getHistoryMsg(page, currentConversation!.chatId, 3);
        const {hasNextPage, list} = res?.data || {hasNextPage: false, list: []};
        const msgList = [...convertHistoryMsg(list), ...(page === 1 ? [] : messageList)];
        setMessageList(msgList);
        setHasNextPage(hasNextPage);
        if (page === 1) {
            if (list.length === 0) {
                sendHelloRsp();
            }
            updateMessageContainerScroll();
            setHistoryInited(true);
            inputFocus();
        } else {
            const msgEle = document.getElementById(`${messageList[0]?.id}`);
            msgEle?.scrollIntoView();
        }
    };

    const {run: handleScroll} = useThrottleFn(
        e => {
            if (e.target.scrollTop === 0 && hasNextPage) {
                updateHistoryMsg(pageNo + 1);
                setPageNo(pageNo + 1);
            }
        },
        {
            leading: true,
            trailing: true,
            wait: 200,
        }
    );

    const inputFocus = () => {
        if (!isMobile) {
            chatFooterRef.current?.inputFocus();
        }
    };

    const inputBlur = () => {
        chatFooterRef.current?.inputBlur();
    };

    const onSendMsg = async (
        msg?: string,
        list?: MessageItem[],
        modelId?: number,
        sendMsgParams?: SendMsgParamsType
    ) => {
        const currentMsg = msg || inputMsg;
        if (currentMsg.trim() === '') {
            setInputMsg('');
            return;
        }

        const msgAgent = agentList.find(item => currentMsg.indexOf(item.name) === 1);
        const certainAgent = currentMsg[0] === '/' && msgAgent;
        const agentIdValue = certainAgent ? msgAgent.id : undefined;
        const agent = agentList.find(item => item.id === sendMsgParams?.agentId);

        if (agent || certainAgent) {
            updateCurrentAgent(agent || msgAgent);
        }
        const msgs = [
            ...(list || messageList),
            {
                id: uuid(),
                msg: currentMsg,
                msgValue: certainAgent
                    ? currentMsg.replace(`/${certainAgent.name}`, '').trim()
                    : currentMsg,
                modelId: modelId === -1 ? undefined : modelId,
                agentId: agent?.id || agentIdValue || currentAgent?.id,
                type: MessageTypeEnum.QUESTION,
                filters: sendMsgParams?.filters,
            },
        ];
        setMessageList(msgs);
        updateMessageContainerScroll();
        setInputMsg('');
    };

    const onInputMsgChange = (value: string) => {
        const inputMsgValue = value || '';
        setInputMsg(inputMsgValue);
    };

    const saveConversationToLocal = (conversation: ConversationDetailType) => {
        if (conversation) {
            if (conversation.chatId !== -1) {
                localStorage.setItem('CONVERSATION_ID', `${conversation.chatId}`);
            }
        } else {
            localStorage.removeItem('CONVERSATION_ID');
        }
    };

    const onSelectConversation = (
        conversation: ConversationDetailType,
        sendMsgParams?: SendMsgParamsType,
        isAdd?: boolean
    ) => {
        setCurrentConversation({
            ...conversation,
            initialMsgParams: sendMsgParams,
            isAdd,
        });
        saveConversationToLocal(conversation);
    };

    const onMsgDataLoaded = (
        data: MsgDataType,
        questionId: string | number,
        question: string,
        valid: boolean,
        isRefresh?: boolean
    ) => {
        onReportMsgEvent?.(question, valid);
        if (!isMobile) {
            conversationRef?.current?.updateData(currentAgent?.id);
        }
        if (!data) {
            return;
        }
        const msgs = cloneDeep(messageList);
        const msg = msgs.find(item => item.id === questionId);
        if (msg) {
            msg.msgData = data;
            setMessageList(msgs);
        }
        if (!isRefresh) {
            updateMessageContainerScroll(`${questionId}`);
        }
    };

    const onToggleHistoryVisible = () => {
        const flag = !historyVisible
        setHistoryVisible(flag);
        if (flag) {
            setMenuVisible(false)
        }
    };

    const onAddConversation = () => {
        conversationRef.current?.onAddConversation();
        inputFocus();
        setShowAgentTip(true)
    };

    const onSelectAgent = (agent: AgentType) => {
        if (agent.id === currentAgent?.id) {
            return;
        }
        if (messageList.length === 1 && messageList[0].type === MessageTypeEnum.AGENT_LIST) {
            setMessageList([]);
        }
        updateCurrentAgent(agent);
        updateMessageContainerScroll();
    };

    const sendMsg = (msg: string, modelId?: number) => {
        if (agentChatMode !== 'explore') {
            setUserInputMsg(msg);
            setInputMsg('');
            setShowAgentTip(false)
            setTimeout(() => setUserInputMsg(""), 100)
        } else {
            onSendMsg(msg, messageList, modelId);
        }
        if (isMobile) {
            inputBlur();
        }
    };

    const onCloseConversation = () => {
        setHistoryVisible(false);
    };

    const onChangeChatPerformanceMode = useCallback((mode) => {
        setChatPerformanceMode(mode);
    }, [])

    const onChangeAgentChatMode = useCallback((mode) => {
        setAgentChatMode(mode);
        setShowAgentTip(mode !== 'explore')
    }, [])


    const onOpenMenu = () => {
        const flag = !menuVisible
        setMenuVisible(flag)
        if (flag) {
            setHistoryVisible(false)
        }
    }

    const openFrame = (type: number) => {
        const hostname = window.location.origin;
        let url = hostname + "/webapp"
        switch (type) {
            case 1:
                url = url + "/dashboard"
                break
            case 2:
                url = url + "/agent"
                break
            case 3:
                url = url + "/model"
                break
            case 4:
                url = url + "/database"
                break
            case 5:
                url = url + "/llm"
                break
            case 6:
                url = url + "/vectorDb"
                break
            case 7:
                url = url + "/system"
                break
        }
        setMenuVisible(false)
        window.open(url)
    }


    const items: MenuProps['items'] = [
        {
            key: '1',
            label: "访问令牌",
            onClick: onClickAccessToken
        },
        {
            key: '2',
            label: "修改密码",
            onClick: onChangePassword
        },
        {
            key: '3',
            label: "退出登录",
            onClick: onClickLoginOut
        },
    ];

    const createMenuListDom = () => {
        return (
            <div className={styles.topMenuList}>
                <div className={styles.topMenu} onClick={() => openFrame(1)}>仪表盘</div>
                {isSuperAdmin &&
                    <>
                        <div className={styles.topMenu} onClick={() => openFrame(2)}>助手中心
                        </div>
                        <div className={styles.topMenu} onClick={() => openFrame(3)}>数据模型
                        </div>
                        <div className={styles.topMenu} onClick={() => openFrame(4)}>数据源
                        </div>
                        <div className={styles.topMenu} onClick={() => openFrame(5)}>AI模型
                        </div>
                        <div className={styles.topMenu} onClick={() => openFrame(6)}>向量数据库
                        </div>
                        <div className={styles.topMenu} onClick={() => openFrame(7)}>系统设置
                        </div>
                    </>
                }
            </div>
        );
    }

    const createLeftBottomMenuList = () => {
        return (
            <>
                {historyVisible &&
                    <div className={styles.chatBodyLeftMenuHistoryFgx}>
                        <div className={styles.chatBodyLeftMenuHistoryFgxLine}></div>
                        <div className={styles.chatBodyLeftMenuHistoryFgxIcon}>
                            <CaretDownOutlined/>
                        </div>
                    </div>
                }
                <div className={styles.chatBodyLeftMenuBox}>
                    <div className={classNames('', {
                        [styles.chatBodyLeftMenu]: true,
                        [styles.chatBodyLeftMenuList]: true
                    })} onClick={onOpenMenu}>
                        <div>
                            <Avatar size={60}
                                    src={<img src={menuVisible ? MenuShowImg : MenuHideImg}
                                              alt="avatar"/>}/>
                        </div>
                        <div className={styles.chatBodyLeftMenuName}
                             style={{
                                 color: menuVisible ? "#832B24" : "#832B24"
                             }}>
                            {menuVisible ? "收起菜单" : "打开菜单"}
                        </div>
                    </div>
                    <div className={classNames('', {
                        [styles.chatBodyLeftMenu]: true,
                        [styles.chatBodyLeftMenuHistory]: true
                    })}
                         onClick={onToggleHistoryVisible}>
                        <div>
                            <Avatar size={60} src={<img src={HistoryChatImg} alt="avatar"/>}/>
                        </div>
                        <div className={styles.chatBodyLeftMenuName}>
                            历史对话
                        </div>
                    </div>
                    <div
                        className={classNames('', {
                            [styles.chatBodyLeftMenu]: true,
                            [styles.chatBodyLeftMenuNewChat]: true
                        })}
                        onClick={() => {
                            onAddConversation()
                        }}>
                        <div>
                            <Avatar shape="square" size={60}
                                    src={<img src={NewChatImg} alt="avatar"/>}/>
                        </div>
                        <div className={styles.chatBodyLeftMenuName}>新建对话</div>
                    </div>
                </div>
            </>
        )
    }
    const createRightUserInfoDom = () => {
        return (
            <div className={styles.chatBodyRightUserInfo}>
                <Avatar src={<img src={UserDefaultImg}
                                  alt="avatar"/>} style={{marginRight: '10px'}}/>
                <Dropdown menu={{items}}>
                    <Space>
                        <span style={{color: '#832B24', cursor: 'pointer'}}>
                            {currentUserInfo.displayName || currentUserInfo.name}
                        </span>
                        <DownOutlined style={{color: '#832B24'}}/>
                    </Space>
                </Dropdown>
            </div>
        )
    }

    const createChatFooterDom = () => {
        return (
            <ChatFooter
                inputMsg={inputMsg}
                chatId={currentConversation?.chatId}
                agentList={agentList}
                currentAgent={currentAgent}
                onInputMsgChange={onInputMsgChange}
                onSendMsg={sendMsg}
                onSelectAgent={onSelectAgent}
                onOpenAgents={() => {
                    if (isMobile) {
                        setMobileAgentsVisible(true);
                    } else {
                        setAgentListVisible(!agentListVisible);
                    }
                }}
                onChangeAgentChatMode={onChangeAgentChatMode}
                onChangeChatPerformanceMode={onChangeChatPerformanceMode}
                ref={chatFooterRef}
            />
        )
    }

    const createCaseDom = () => {
        return currentAgent && (
            <Modal
                title="showcase"
                width="98%"
                open={showCaseVisible}
                centered
                footer={null}
                wrapClassName={styles.showCaseModal}
                destroyOnClose
                onCancel={() => {
                    setShowCaseVisible(false);
                }}
            >
                <ShowCase
                    height="calc(100vh - 140px)"
                    agentId={currentAgent.id}
                    onSendMsg={onSendMsg}
                />
            </Modal>
        )
    }

    const createMessageContainerDom = () => {
        return currentConversation && (
            <MessageContainer
                id="messageContainer"
                isSimpleMode={isMobile ? true : isSimpleMode}
                isDebugMode={isDebugMode}
                messageList={messageList}
                chatId={currentConversation?.chatId}
                currentAgent={currentAgent}
                chatVisible={chatVisible}
                isDeveloper={isDeveloper}
                integrateSystem={integrateSystem}
                onMsgDataLoaded={onMsgDataLoaded}
                onSendMsg={onSendMsg}
                chatPerformanceMode={chatPerformanceMode}
            />
        )
    }
    const createPlanModeMessageContainerDom = () => {
        return (<>
            <div className={styles.planModeMessageContainer} id="planModeMessageContainer">
                {showAgentTip &&
                    <AgentTip currentAgent={currentAgent}
                              onSendMsg={(_msg) => {
                                  sendMsg(_msg)
                                  setShowAgentTip(false)
                              }}/>}

                {currentConversation &&
                    <PlanModeMessageContainer
                        id="planModeMessageContainer"
                        isNewChat={showAgentTip}
                        agentId={currentAgent!.id}
                        userInputMsg={userInputMsg}
                        chatId={currentConversation?.chatId}
                        onReviewMsg={(_msg: string) => setInputMsg(_msg)}/>
                }
            </div>

        </>)
    }

    const createNextChatContainerDom = () => {
        return (<>
            {showAgentTip &&
                <AgentTip currentAgent={currentAgent}
                          onSendMsg={(_msg) => {
                              sendMsg(_msg)
                              setShowAgentTip(false)
                          }}/>}
            {currentConversation &&
                <NextChatMessageContainer
                    id="planModeMessageContainer"
                    isNewChat={showAgentTip}
                    userInputMsg={userInputMsg}
                    chatId={currentConversation?.chatId} onHideLeft={(_flag: boolean) => setLeftBlockVisible(_flag)}/>
            }
        </>)
    }

    return (
        <ConfigProvider locale={locale}>
            <div className={styles.chat}>
                <div className={styles.chatSection}>
                    {!isMobile && agentList.length > 1 && agentListVisible && (
                        <AgentList
                            agentList={agentList}
                            currentAgent={currentAgent}
                            onSelectAgent={onSelectAgent}
                            onCloseAgentDrawer={() => {
                                setAgentListVisible(false)
                            }}
                        />
                    )}
                    <div className={styles.chatApp}>
                        {!currentConversation && <NoPermission/>}
                        {currentConversation && (
                            <div className={styles.chatBody}>
                                {leftBlockVisible &&
                                    <div className={styles.chatBodyLeft}>
                                        <div className={styles.chatBodyLeftHeader}>
                                            <div className={styles.chatBodyLeftHeaderLogo}>
                                                <Avatar size={45} src={<img src={LogoImg} alt="avatar"/>}/>
                                            </div>
                                            <div className={styles.chatBodyLeftHeaderTitle}>酷斯特AI助手</div>
                                        </div>
                                        {/*菜单列表*/}
                                        {menuVisible && createMenuListDom()}
                                        {/*左侧底部按钮*/}
                                        {createLeftBottomMenuList()}
                                    </div>
                                }
                                <div className={styles.chatContent}>
                                    {/*消息内容*/}
                                    {agentChatMode === 'explore' && createMessageContainerDom()}
                                    {agentChatMode === 'plan' && createPlanModeMessageContainerDom()}
                                    {agentChatMode === 'nextChat' && createNextChatContainerDom()}
                                    {/*底部输入框*/}
                                    {!noInput && createChatFooterDom()}
                                </div>
                                <div className={styles.chatBodyRight}>
                                    {createRightUserInfoDom()}
                                </div>
                            </div>
                        )}
                    </div>
                    <Conversation
                        currentAgent={currentAgent}
                        currentConversation={currentConversation}
                        historyVisible={historyVisible}
                        onSelectConversation={onSelectConversation}
                        onCloseConversation={onCloseConversation}
                        ref={conversationRef}
                    />
                    {createCaseDom()}
                </div>
                <MobileAgents
                    open={mobileAgentsVisible}
                    agentList={agentList}
                    currentAgent={currentAgent}
                    onSelectAgent={onSelectAgent}
                    onClose={() => {
                        setMobileAgentsVisible(false);
                    }}
                />
            </div>
        </ConfigProvider>
    );
};

export default forwardRef(PcChat);
