.studentDetailDrawerBox {
  position: fixed;
  top: 1rem;
  right: 1rem;
  bottom: 1rem;
  width: 38.5%;
  background: #fff;
  z-index: 90;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, .1);

  .studentDetailJiaZi {
    margin-bottom: -0.8rem;
    text-align: center;
    .studentDetailJiaZiImg {
      width: 8rem;
      height: 3.2rem;
    }
  }

  .studentDetailDrawerBoxBody {
    height: 100%;
    width: 100%;

    background-image: url("../../../../public/images/shu_jia_di_bu.png");
    background-repeat: no-repeat;
    background-size: 100%;
    padding: 30px;

    .studentDetail {
      height: 93%;
      overflow-y: auto;
      overflow-x: hidden;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 10px 20px rgba(0, 0, 0, .1);
    }
  }

  .studentDetailDrawerBoxHeader {
    font-size: 1.2rem;

    .closeBtn {
      cursor: pointer;
    }
  }
}
