import React from 'react';
import styles from './style.module.less';
import {ChatMessage} from "../data";
import StudentPortrait from "../../../components/StudentPortrait";
import {CloseCircleOutlined} from "@ant-design/icons";
import SHU_JIA_IMG from "/public/images/shu_jia.png";
import QueueAnim from "rc-queue-anim";

type Props = {
    open: boolean;
    chatMessage: ChatMessage | undefined;
    onClose: () => void;
};

const StudentDetailDrawer: React.FC<Props> = ({
                                                  open,
                                                  onClose,
                                                  chatMessage
                                              }) => {
    const createDom = () => {
        return (
            <div className={styles.studentDetailDrawerBox}>
                <div className={styles.studentDetailDrawerBoxHeader}>
                    <div className={styles.closeBtn} onClick={onClose}><CloseCircleOutlined/></div>
                </div>
                <div className={styles.studentDetailJiaZi}>
                    <img className={styles.studentDetailJiaZiImg} src={SHU_JIA_IMG} alt="avatar"/>
                </div>
                <div className={styles.studentDetailDrawerBoxBody}>
                    <div className={styles.studentDetail}>
                        {chatMessage && <StudentPortrait chatMessage={chatMessage}/>}
                    </div>

                </div>
            </div>
        )
    }

    return (
        <QueueAnim>
            {open ? createDom() : null}
        </QueueAnim>
    );
};


export default StudentDetailDrawer;
