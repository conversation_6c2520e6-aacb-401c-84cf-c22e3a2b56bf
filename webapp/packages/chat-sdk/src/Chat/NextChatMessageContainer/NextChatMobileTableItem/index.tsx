import styles from './style.module.less';
import {DownOutlined, UpOutlined} from "@ant-design/icons";
import React, {useEffect, useState} from "react";
import {makeUUID} from "../data";

type Props = {
    record: any;
    tableColumns: any;
    tableDataSourceLength: number;
    onClickTableItem: (record: any) => void;
};

const NextChatMobileTableItem: React.FC<Props> = ({
                                                      record,
                                                      tableColumns,
                                                      tableDataSourceLength,
                                                      onClickTableItem
                                                  }) => {

    const [tableItemData, setTableItemData] = useState<any>();

    useEffect(() => {
        //张杰的各科目成绩
        if (record) {
            setTableItemData(record);
        }
    }, [record]);


    const handlerOpenMore = () => {
        setTableItemData((pre: any) => {
            return {...pre, openMore: !pre.openMore};
        })
    }

    if (!tableItemData) {
        return null;
    }

    return (
        <div className={styles.mobileTableCard} key={makeUUID()}>
            <div className={styles.mobileTableCardBody} onClick={onClickTableItem}>
                {tableColumns.map(({title, dataIndex}, index: number) => {
                    let value: string = "--";
                    if (tableDataSourceLength > 0) {
                        value = tableItemData[dataIndex] || "--"
                    }
                    if (index > 3 && !tableItemData.openMore) {
                        return null;
                    }
                    return (
                        <div key={makeUUID()}>
                            <div className={styles.mobileTableCardBodyRow}>
                                <div className={styles.mobileTableCardBodyRowLabel}
                                     style={{
                                         borderTopLeftRadius: index === 0 ? '0.5rem' : '',
                                         borderBottomLeftRadius: tableColumns.length === 1 ? '0.5rem' : ''
                                     }}
                                >
                                    {title}
                                </div>
                                <div className={styles.mobileTableCardBodyRowValue}>
                                    {value}
                                </div>
                            </div>
                            {tableColumns.length > 1 &&
                                <div className={styles.mobileTableCardBodyRowDivider}></div>
                            }
                        </div>
                    )
                })}
            </div>
            {tableColumns.length > 3 &&
                <div className={styles.mobileTableCardAction} onClick={() => handlerOpenMore()}>
                    {tableItemData.openMore &&
                        <>
                            <div className={styles.mobileTableCardActionText}>收起</div>
                            <div className={styles.mobileTableCardActionIcon}>
                                <UpOutlined/>
                            </div>
                        </>
                    }
                    {!tableItemData.openMore &&
                        <>
                            <div className={styles.mobileTableCardActionText}>展开</div>
                            <div className={styles.mobileTableCardActionIcon}>
                                <DownOutlined/>
                            </div>
                        </>
                    }
                </div>
            }
        </div>
    );
};

export default NextChatMobileTableItem;
