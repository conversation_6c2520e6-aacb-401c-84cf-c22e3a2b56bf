.nextChatMessageContainer {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 0;
  overflow-x: hidden;
  overflow-y: scroll;
  scrollbar-width: none; /* 设置滚动条宽度（可选值：auto | thin | none） */
  .error{
    min-height: 3rem;
    margin: 0.3rem;
    background-color: #fff0f0;
    padding: 1rem;
    border-radius: 1rem;
    //color: #c20c0c;
    color: #720808;
  }
  .loading {
    min-height: 3rem;
    display: flex;
    align-items: center;
    margin: 0.3rem;
    background-color: #fff0f0;
    padding: 1rem;
    border-radius: 1rem;
    //color: #c20c0c;
    color: #720808;
  }
  .messageTools{
    color: hsl(221, 27%, 23%);
    .messageTool{
      cursor: pointer;
    }
  }
  .messageSqlBox {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-bottom: 0.5rem;
    border-radius: 0.3rem;
    justify-content: space-between;
    background-color: #fff;
    margin-bottom: 0.5rem;
    cursor: pointer;
    .messageSqlBoxHeader{
      padding-top: 0.5rem;
      display: flex;
      justify-content: space-between;
    }
  }

  .messageList {
    display: flex;
    flex-direction: column;
    padding: 60px 20px 50px 14px;
    row-gap: 16px;

    .messageItem {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      background-color: #fff;
      border-radius: 0.5rem;
      margin-bottom: 0.5rem;

      .messageItemBottom {
        padding-left: 0.5rem;
        padding-bottom: 0.5rem;

        .messageItemLink {
          width: 15rem;
          height: 2rem;
          display: flex;
          align-items: center;
          padding-left: 0.5rem;
          padding-right: 0.5rem;
          border-radius: 0.3rem;
          justify-content: space-between;
          background-color: #f5f5f5;
          margin-bottom: 0.5rem;
          cursor: pointer;

          &:hover {
            background-color: #eeeeee;
          }
        }
      }
    }
  }
}
