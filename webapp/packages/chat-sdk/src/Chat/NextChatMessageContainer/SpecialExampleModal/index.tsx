import React, {useEffect, useState} from 'react';

import TextArea from "antd/es/input/TextArea";
import {Card, Col, Form, Input, message, Modal, Row} from 'antd';

import {sql} from "@codemirror/lang-sql";
import CodeMirror from "@uiw/react-codemirror/esm";

import {AgentMessageInfo} from "../data";
import {addSpecialExampleQuery} from "../service/api";

import styles from './style.module.less'
import SpecialExampleForm from "./SpecialExampleForm";


type Props = {
    open: boolean;
    question: string;
    chatMessage: AgentMessageInfo
    onCloseExampleModal: () => void;
};

const SpecialExampleModal: React.FC<Props> = ({
                                                  open = false,
                                                  question,
                                                  chatMessage,
                                                  onCloseExampleModal
                                              }) => {

        const [form] = Form.useForm();

        const [confirmLoading, setConfirmLoading] = useState(false);
        const [formDataList, setFormDataList] = useState<any>([]);
        const [parameters, setParameters] = useState<any>([]);

        Form.useWatch((values) => {
            console.log(`sql: ${values.sql || ''}`);
            const sql = values.sql || ""
            const matches = sql?.match(/:\w+/g);

            //console.log("输出 ", matches); // 输出 [':start_date', ':end_date']

            const varNames: any = matches ? matches.map(m => m.slice(1)) : [];

            //console.log("输出参数 :", varNames); // 输出 ['start_date', 'end_date']
            setFormDataList([])
            if (varNames.length > 0) {
                setFormDataList((pre) => {
                    const oldList = pre.map((item: any) => item.name);
                    const newList = varNames.filter((item: any) => !oldList.includes(item));
                    const tmpList = newList.map((item: any) => {
                        return {
                            "name": item,
                            "dataType": "string",
                            "comment": ""
                        }
                    })
                    return [...pre, ...tmpList];
                })
                setParameters((pre) => {
                    const oldList = pre.map((item: any) => item.name);
                    const newList = varNames.filter((item: any) => !oldList.includes(item));
                    const tmpList = newList.map((item: any) => {
                        return {
                            "name": item,
                            "dataType": "string",
                            "comment": ""
                        }
                    })
                    return [...pre, ...tmpList];
                })
            }

        }, form);

        useEffect(() => {
            form.setFieldValue("name", question);
            const inputSql = chatMessage?.parseResult?.inputSql || ""
            form.setFieldValue("sql", inputSql);
        }, [question, chatMessage]);


        const handleOk = () => {
            setConfirmLoading(true);
            form
                .validateFields({validateOnly: true})
                .then(() => {
                    const params = form.getFieldsValue()
                    const tmpParams = {
                        ...params,
                        parameters: parameters
                    }
                    addSpecialExampleQuery(tmpParams).then(() => {
                        message.success("添加成功");
                        setConfirmLoading(false)
                        const timer = setTimeout(() => {
                            handleCancel()
                            clearTimeout(timer)
                        }, 300)
                    })
                })
                .catch(() => setConfirmLoading(false));
        };

        const handleCancel = () => {
            setConfirmLoading(false)
            setFormDataList([])
            setParameters([])
            onCloseExampleModal();
        };
        const onSpecialExampleFormChange = (values) => {
            setParameters((pre) => {
                return pre.map((item: any) => {
                    return item.name === values.name ? values : item;
                })
            })
        }
        return (
            <>
                <Modal
                    title="添加示例"
                    width="90vw"
                    open={open}
                    onOk={handleOk}
                    confirmLoading={confirmLoading}
                    onCancel={handleCancel}
                >
                    <div className={styles.specialExampleModalBox}>
                        <Form form={form} name="validateOnly" layout="vertical" autoComplete="off">
                            <Row gutter={16}>
                                <Col className="gutter-row" span={16}>
                                    <div className={styles.leftFormList}>
                                        <Form.Item name="name" label="查询内容" rules={[{required: true}]}>
                                            <Input/>
                                        </Form.Item>
                                        <Form.Item name="sql" label="大模型SQL" rules={[{required: true}]}>
                                            <CodeMirror height="200px" extensions={[sql()]}/>
                                        </Form.Item>
                                        <Form.Item name="usageGuidance" label="适用范围">
                                            <TextArea/>
                                        </Form.Item>
                                    </div>
                                </Col>
                                <Col className="gutter-row" span={8}>
                                    <div className={styles.rightFormList}>
                                        {formDataList.map((formData: any, index: number) => {
                                            return (
                                                <Card className={styles.rightFormItem}>
                                                    <SpecialExampleForm key={index} formData={formData}
                                                                        onChange={(values) => onSpecialExampleFormChange(values)}/>
                                                </Card>
                                            )
                                        })}
                                    </div>
                                </Col>
                            </Row>
                        </Form>
                    </div>
                </Modal>
            </>
        );
    }
;

export default SpecialExampleModal;
