import Text from '../components/Text';
import NextChatTable from "./NextChatTable";
import Loading from "../../components/ChatItem/Loading";

import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Collapse, Flex, message} from "antd";
import {ArrowRightOutlined, CaretDownOutlined, CodeOutlined, FundOutlined, PieChartOutlined} from "@ant-design/icons";

import {cloneDeep} from "lodash";
import {solarizedlight} from "react-syntax-highlighter/dist/esm/styles/prism";
import {format} from "sql-formatter";
import {Prism as SyntaxHighlighter} from 'react-syntax-highlighter';
import {CopyToClipboard} from 'react-copy-to-clipboard';

import styles from './style.module.less';
import {AgentMessageInfo, ChatMessage, makeUUID} from "./data";

import useChatService from "./service/service";
import {isMobile, updateMessageContainerScroll} from "../../utils/utils";
import NextChatMobileTable from "./NextChatMobileTable";
import ExampleModal from "./ExampleModal";
import SpecialExampleModal from "./SpecialExampleModal";
import StudentDetailDrawer from "./StudentDetailDrawer";


type Props = {
    id: string;
    isNewChat: boolean;
    chatId: number;
    userInputMsg: string;
    onHideLeft: (_flag: boolean) => void
};

const NextChatMessageContainer: React.FC<Props> = ({
                                                       id,
                                                       isNewChat,
                                                       chatId,
                                                       userInputMsg,
                                                       onHideLeft
                                                   }) => {

    const chatService = useChatService();

    const [openStudentDetailDrawer, setOpenStudentDetailDrawer] = useState(false);
    const [loading, setLoading] = useState<boolean>(false)
    const [errorStr, setErrorStr] = useState<string>()
    const [messageList, setMessageList] = useState<ChatMessage[]>([]);
    const [currentAgentMessage, setCurrentAgentMessage] = useState<ChatMessage>();
    const [openExampleModal, setOpenExampleModal] = useState(false);
    const [openSpecialExampleModal, setOpenSpecialExampleModal] = useState(false);
    const [currentAddExampleMessage, setCurrentAddExampleMessage] = useState<AgentMessageInfo>();
    const [currentAddExampleQuestion, setCurrentAddExampleQuestion] = useState<string>("");

    useEffect(() => {
        if (userInputMsg) {
            createNewMsg(userInputMsg)
            request()
            setErrorStr("")
        }
    }, [userInputMsg]);

    useEffect(() => {
        if (isNewChat) {
            setMessageList([])
            setErrorStr("")
            setLoading(false)
        }
    }, [isNewChat]);

    const createNewMsg = (msg: string) => {
        setMessageList((pre) => {
            const tmpMessage: ChatMessage = {
                role: 'user',
                chatId: chatId,
                messageData: msg,
            }
            return [...pre, tmpMessage]
        })
        updateMessageContainerScroll(id);
    }

    const request = () => {
        const params = {"question": userInputMsg}
        setLoading(true)
        chatService.stream(params, {
            onMessage: (tmpData: AgentMessageInfo) => {
                const dataId = makeUUID();
                setMessageList((pre) => {
                    return [...pre, {
                        id: dataId,
                        showSql: false,
                        currentShowSql: "",
                        role: 'agent',
                        chatId: chatId,
                        messageData: tmpData
                    }]
                });
                updateMessageContainerScroll(id);
            },
            onComplete: () => {
                setLoading(false);
                updateMessageContainerScroll(id);
            },
            onTimeout: () => {
                message.error("请求超时")
                setLoading(false)
            },
            onError: (_error) => {
                setErrorStr(_error as string)
                setLoading(false)
            }
        })
    }

    const handleCopy = (_: string, result: any) => {
        result ? message.success('复制SQL成功', 1) : message.error('复制SQL失败', 1);
    };

    const handlerShowSql = (chatMessage: ChatMessage, type: "inputSql" | "outputSql") => {
        const messageData = chatMessage.messageData as AgentMessageInfo
        const parseResult = messageData.parseResult;
        const sql = type === "inputSql" ? parseResult.inputSql : parseResult.outputSql;

        setMessageList((pre) => {
            return pre.map(item => {
                if (item.role === "user") {
                    return item;
                }
                const showSql = item.id === chatMessage.id ? true : item.showSql
                const currentShowSql = item.id === chatMessage.id ? sql : item.currentShowSql
                return {...item, showSql: showSql, currentShowSql: currentShowSql};
            })
        });
    }

    const genExtra = (chatMessage: ChatMessage) => (
        <CopyToClipboard
            text={format(chatMessage.currentShowSql || "")}
            onCopy={(text: any, result: any) => handleCopy(text, result)}
        >
            <Button type="text">复制代码</Button>
        </CopyToClipboard>
    );

    const getCollapseItems = (chatMessage: ChatMessage) => {
        return [
            {
                key: '1',
                label: <CodeOutlined/>,
                extra: genExtra(chatMessage),
                children: <SyntaxHighlighter
                    language="sql"
                    style={solarizedlight}
                >
                    {format(chatMessage.currentShowSql || "")}
                </SyntaxHighlighter>,
            },
        ]
    }

    const handlerAddExample = (chatMessage: ChatMessage, from: string) => {
        const messageData = chatMessage.messageData as AgentMessageInfo
        const index = messageList.indexOf(chatMessage)
        const newMessage = messageList[index - 1]

        setCurrentAddExampleMessage(messageData)
        setCurrentAddExampleQuestion(newMessage.messageData as string);
        setOpenExampleModal(from === "example")
        setOpenSpecialExampleModal(from !== "example")
    }
    const handlerTableAction = (chatMessage: ChatMessage, _record) => {
        const tmpChatMessage = cloneDeep(chatMessage);
        (tmpChatMessage.messageData as AgentMessageInfo).data = [_record]
        setCurrentAgentMessage(tmpChatMessage)
        onHideLeft(false)
        setOpenStudentDetailDrawer(true)
    }
    const createMsgDom = (chatMessage: ChatMessage) => {
        const messageData = chatMessage.messageData as AgentMessageInfo
        return (
            <>
                <div className={styles.messageItem} style={{backgroundColor: isMobile ? 'transparent' : '#fff'}}>
                    {!isMobile && <NextChatTable
                        key={makeUUID()}
                        agentMessage={messageData}
                        onAction={(_record) => handlerTableAction(chatMessage, _record)}/>
                    }
                    {isMobile && <NextChatMobileTable key={makeUUID()} agentMessage={messageData}/>}
                    {!isMobile &&
                        <div className={styles.messageItemBottom}>
                            <div className={styles.messageItemLink}
                                 key={makeUUID()}
                                 onClick={() => handlerShowSql(chatMessage, "inputSql")}>
                                <div>您是否想查看大语言SQL模版？</div>
                                <div><ArrowRightOutlined/></div>
                            </div>
                            <div className={styles.messageItemLink}
                                 key={makeUUID()}
                                 onClick={() => handlerShowSql(chatMessage, "outputSql")}>
                                <div>最终执行SQL是什么样？</div>
                                <div><ArrowRightOutlined/></div>
                            </div>
                        </div>
                    }
                </div>
                {chatMessage.showSql &&
                    <div className={styles.messageSqlBox}>
                        <Collapse
                            bordered={false}
                            expandIconPosition="end"
                            defaultActiveKey={['1']}
                            expandIcon={({isActive}) => <CaretDownOutlined rotate={isActive ? 90 : 0}/>}
                            items={getCollapseItems(chatMessage)}
                        />
                    </div>
                }
                {!isMobile &&
                    <div className={styles.messageTools}>
                        <Flex gap="middle">
                            <Flex gap="small" className={styles.messageTool}
                                  onClick={() => handlerAddExample(chatMessage, "example")}>
                                <div>
                                    <PieChartOutlined/>
                                </div>
                                <div>添加基础示例</div>
                            </Flex>
                            <Flex gap="small" className={styles.messageTool}
                                  onClick={() => handlerAddExample(chatMessage, "")}>
                                <div>
                                    <FundOutlined/>
                                </div>
                                <div>添加高级示例</div>
                            </Flex>
                        </Flex>
                    </div>
                }
            </>
        )
    }
    const createAgentMsgDom = (chatMessage: ChatMessage) => {
        return createMsgDom(chatMessage)
    }
    const handlerStudentDetailDrawerClose = () => {
        onHideLeft(true)
        setOpenStudentDetailDrawer(false)
    }
    //张杰的各科目成绩
    return (
        <>
            <StudentDetailDrawer chatMessage={currentAgentMessage}
                                 open={openStudentDetailDrawer}
                                 onClose={() => handlerStudentDetailDrawerClose()}/>
            {currentAddExampleMessage &&
                (<>
                    <ExampleModal
                        open={openExampleModal}
                        question={currentAddExampleQuestion}
                        chatMessage={currentAddExampleMessage}
                        onCloseExampleModal={() => setOpenExampleModal(false)}/>
                    <SpecialExampleModal
                        open={openSpecialExampleModal}
                        question={currentAddExampleQuestion}
                        chatMessage={currentAddExampleMessage}
                        onCloseExampleModal={() => setOpenSpecialExampleModal(false)}/>
                </>)
            }
            <div id={id} className={styles.nextChatMessageContainer}>
                <div className={styles.messageList}>

                    {messageList?.map((item, index) => {
                        return (
                            <div key={index}>
                                {item.role === 'user' && <Text position="right" data={item.messageData}/>}
                                {item.role === 'agent' && createAgentMsgDom(item)}
                            </div>
                        )
                    })}
                    {loading &&
                        <div className={styles.loading}>
                            <Flex>
                                任务已接收，请稍后
                                <Loading/>
                            </Flex>
                        </div>
                    }
                    {errorStr &&
                        <div className={styles.error}>
                            {errorStr}
                        </div>
                    }
                </div>

            </div>
        </>

    );
};


export default NextChatMessageContainer;
