import React, {useEffect, useState} from 'react';
import styles from './style.module.less';
import {AgentMessageInfo} from "../data";
import {ProTable} from '@ant-design/pro-components';
import {Button} from "antd";

type Props = {
    agentMessage: AgentMessageInfo;
    onAction?: (_record: any) => void;
};

const NextChatTable: React.FC<Props> = ({
                                            agentMessage,
                                            onAction
                                        }) => {


    const [tableListDataSource, setTableListDataSource] = useState<any>([]);
    const [tableColumns, setTableColumns] = useState<{
        title: string
        dataIndex: string
        key: string
    }[]>([]);

    useEffect(() => {
        if (agentMessage) {
            initTableColumns()
        }
    }, [agentMessage]);

    const initTableColumns = () => {
        const parseResult = agentMessage.parseResult;
        const columns = parseResult.columns;
        const tmp = columns.map(item => {
            return {
                title: item.description || item.name,
                dataIndex: item.name || item.description,
                key: item.name || item.description,
            }
        })
        const actionColumn: any = {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: (text, record, _, action) => [
                <Button type="link" onClick={() => onAction?.(record)}>
                    查看
                </Button>,
            ],
        }
        if (agentMessage?.relatedViewQueryGuidance!.availableViews.length > 0) {
            tmp.push(actionColumn);
        }
        setTableColumns(tmp);
        setTableListDataSource(agentMessage.data);
    }

    return (
        <div className={styles.nextChatTableBox}>
            <ProTable
                columns={tableColumns}
                dataSource={tableListDataSource}
                rowKey="key"
                search={false}
                dateFormatter="string"
            />
        </div>
    );
};


export default NextChatTable;
