import styles from './style.module.less';
import React, {useEffect, useState} from "react";
import {AgentMessageInfo, ChatMessage} from "../data";
import NextChatMobileTableItem from "../NextChatMobileTableItem";
import NextChatMobileTableDrawer from "../NextChatMobileTableDrawer";
import StudentDetailMobileDrawer from "../StudentDetailMobileDrawer";
import {cloneDeep} from "lodash";

type Props = {
    agentMessage: AgentMessageInfo;
};

const NextChatMobileTable: React.FC<Props> = ({
                                                  agentMessage
                                              }) => {

    const [currentAgentMessage, setCurrentAgentMessage] = useState<ChatMessage | undefined>();
    const [showStudentDetailMobileDrawer, setShowStudentDetailMobileDrawer] = useState(false);
    const [showMoreModel, setShowMoreModel] = useState(false);
    const [tableDataSource, setTableDataSource] = useState<any>([]);
    const [tableColumns, setTableColumns] = useState<{
        title: string
        dataIndex: string
        key: string
    }[]>([]);

    useEffect(() => {
        if (agentMessage) {
            initTableColumns()
        }
    }, [agentMessage]);

    const initTableColumns = () => {
        const parseResult = agentMessage.parseResult;
        const columns = parseResult.columns;
        const tmp = columns.map(item => {
            return {
                title: item.description || item.name,
                dataIndex: item.name || item.description,
                key: item.name || item.description,
            }
        })
        setTableColumns(tmp);
        setTableDataSource(agentMessage.data);
    }

    const handlerClickTableItem = (record) => {
        if (agentMessage.relatedViewQueryGuidance?.availableViews.length === 0) {
            return
        }
        const messageData = cloneDeep(agentMessage)
        messageData.data = [record]
        const tmp: ChatMessage = {
            role: 'agent',
            messageData: messageData
        }
        setCurrentAgentMessage(tmp)
        setShowStudentDetailMobileDrawer(true)
    }

    const createMobileTableCardDom = (record: any) => {
        return (
            <>
                <NextChatMobileTableItem
                    record={record}
                    tableColumns={tableColumns}
                    tableDataSourceLength={tableDataSource.length}
                    onClickTableItem={() => handlerClickTableItem(record)}/>

            </>
        )
    }
    const handlerCloseStudentDetailMobileDrawer = () => {
        setCurrentAgentMessage(undefined);
        setShowStudentDetailMobileDrawer(false)
    }
    return (
        <>
            <StudentDetailMobileDrawer
                open={showStudentDetailMobileDrawer}
                chatMessage={currentAgentMessage}
                onClose={() => handlerCloseStudentDetailMobileDrawer()}/>
            <NextChatMobileTableDrawer
                open={showMoreModel}
                tableColumns={tableColumns}
                tableDataSource={tableDataSource}
                onClickTableItem={(record) => handlerClickTableItem(record)}
                onCloseMoreModel={() => setShowMoreModel(false)}/>

            <div className={styles.nextChatMobileTableBox}>
                {tableDataSource.length > 1 &&
                    <div className={styles.mobileTableBoxMoreBtn} onClick={() => setShowMoreModel(true)}>更多</div>
                }
                {tableDataSource.length > 0 && createMobileTableCardDom(tableDataSource[0])}
            </div>
        </>

    );
};

export default NextChatMobileTable;
