export interface ParseResultColumn {
    name: string
    originField: string
    agg: string
    description: string
    field: boolean
    aggregation: boolean
}

export interface ParseResult {
    inputSql: string;
    outputSql: string;
    columns: ParseResultColumn[]
}

export interface AvailableViewsColumn {
    name: string
    originField: string
    agg: string
    description: string
    primaryKey: boolean
    dataType: string
    field: boolean
    aggregation: boolean
    originModelName: string
    originFieldName: string
}

export interface AvailableViews {
    title: string
    description: string
    baseModelName: string
    name: string
    columns: AvailableViewsColumn[]
    seq: string
    primary: boolean
}

export interface QueryGuidance {
    name: string
    baseModelName: string
    paramToDataColumnMapping: Record<string, string>
    children: QueryGuidance[]
}

export interface RelatedViewQueryGuidance {
    availableViews: AvailableViews[]
    queryGuidance: QueryGuidance[]
}

export interface AgentMessageInfo {
    parseResult: ParseResult;
    relatedViewQueryGuidance?: RelatedViewQueryGuidance
    data: any
}

export interface ChatMessage {
    id?: string;
    showSql?: boolean;
    currentShowSql?: string;
    role: 'user' | 'agent',
    chatId?: string | number,
    messageData: string | AgentMessageInfo,
}

export const makeUUID = () => {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    const uuid: string[] = [];
    for (let i = 0; i < 32; i++) {
        uuid[i] = chars[Math.floor(Math.random() * 62)];
    }
    return uuid.join('');
}
