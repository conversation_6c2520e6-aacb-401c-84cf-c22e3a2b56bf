import React from 'react';
import styles from './style.module.less';
import {ChatMessage} from "../data";
import StudentPortrait from "../../../components/StudentPortrait";
import {CloseOutlined} from "@ant-design/icons";
import SHU_JIA_IMG from "/public/images/shu_jia.png";
import {Button, Drawer} from "antd";

type Props = {
    open: boolean;
    chatMessage: ChatMessage | undefined;
    onClose: () => void;
};

const StudentDetailMobileDrawer: React.FC<Props> = ({
                                                        open,
                                                        onClose,
                                                        chatMessage
                                                    }) => {

    return (
        <Drawer
            zIndex={999999}
            title="学生画像"
            placement="bottom"
            closable={false}
            onClose={onClose}
            open={open}
            key="bottom"
            height="90vh"
            style={{borderRadius: '20px 20px 0px 0px'}}
            extra={
                <Button type="text" onClick={onClose} icon={<CloseOutlined/>}/>
            }
        >
            <div className={styles.studentDetailMobileDrawerBox}>
                <div className={styles.jiaZi}>
                    <img className={styles.jiaZiImg} src={SHU_JIA_IMG} alt="avatar"/>
                </div>
                <div className={styles.body}>
                    <div className={styles.studentDetail}>
                        {chatMessage && <StudentPortrait chatMessage={chatMessage}/>}
                    </div>

                </div>
            </div>
        </Drawer>
    );
};


export default StudentDetailMobileDrawer;
