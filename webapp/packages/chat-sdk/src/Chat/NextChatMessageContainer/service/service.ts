import {AgentMessageInfo} from "../data";

export interface ChatServiceCallbacks {
    //开始事件,每次流开始都有
    onStart?: () => void
    //普通消息
    onMessage?: (tmpData: AgentMessageInfo) => void
    //需要人机交互
    onInterrupt?: (_event: string, tmpData: AgentMessageInfo) => void
    //单次流结束，终止加载状态
    onComplete?: () => void
    //单次查询结束、feadback反馈信息清空
    onFinish?: () => void
    onError?: (_error: Error | string) => void
    onTimeout?: () => void
    onTerminate?: () => void
}

const TOKEN_KEY = process.env.APP_TARGET === 'inner' ? 'TME_TOKEN' : 'SUPERSONIC_TOKEN';
const AUTH_TOKEN_KEY = localStorage.getItem(TOKEN_KEY)

export class ChatService {
    //中断控制
    private abortController = new AbortController()

    //中断请求
    public cancelRequest() {
        this.abortController.abort()
    }

    public async stream(
        params: any,
        callbacks: ChatServiceCallbacks = {},
        timeout = 300000
    ): Promise<void> {
        // 设置超时
        let timeoutId: NodeJS.Timeout | undefined;
        try {
            timeoutId = setTimeout(() => {
                this.abortController.abort(); // 中断请求
                callbacks.onTimeout?.()
                console.error("请求超时")
            }, timeout);

            const response = await fetch("/api/v2/semantic/exec-sql", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    // Accept: 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    Connection: 'keep-alive',
                    Authorization: `Bearer ${AUTH_TOKEN_KEY}`,
                    "auth": `Bearer ${AUTH_TOKEN_KEY}`
                },
                body: JSON.stringify(params),
                signal: this.abortController.signal
            });
            clearTimeout(timeoutId); // 清除定时器
            if (!response.ok) {
                const errorData = await response.json();
                return callbacks.onError?.(new Error(
                    `HTTP error! status: ${response.status}${errorData.error?.message ? ` - ${errorData.error.message}` : ''}`
                ))
            }
            if (!response.body) {
                const errorData = await response.json();
                return callbacks.onError?.(new Error(
                    `HTTP error! status: ${response.status}${errorData.error?.message ? ` - ${errorData.error.message}` : ''}`
                ))
            }
            if (response.body) {
                const json = await response.json();
                const {code, data, msg} = json;
                if (200 !== code) {
                    return callbacks.onError?.(msg)
                }
                callbacks.onMessage?.(data)
                callbacks.onComplete?.()
            }
        } catch (error) {
            if ((error as Error).name === 'AbortError') {
                if (timeoutId) {
                    console.log('请求超时或被取消')
                    callbacks.onTimeout?.()
                } else {
                    console.log('请求被取消')
                    callbacks.onComplete?.()
                }
                return
            }
            const finalError = error instanceof Error ? error : new Error('未知错误')
            callbacks.onError?.(finalError)
            throw finalError
        } finally {
            clearTimeout(timeoutId)
        }
    }
}

export default function useChatService() {
    return new ChatService();
}
