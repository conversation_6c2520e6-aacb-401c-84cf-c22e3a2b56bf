import Text from '../components/Text';
import {memo, useCallback, useEffect, useState} from 'react';
import {isEqual} from 'lodash';
import {AgentType, MessageItem, MessageTypeEnum} from '../type';
import {isMobile, updateMessageContainerScroll} from '../../utils/utils';
import styles from './style.module.less';
import AgentTip from '../components/AgentTip';
import classNames from 'classnames';
import {MsgDataType} from '../../common/type';
import ChatItem from '../../components/ChatItem';

type Props = {
    id: string;
    chatId: number;
    messageList: MessageItem[];
    currentAgent?: AgentType;
    chatVisible?: boolean;
    isDeveloper?: boolean;
    integrateSystem?: string;
    isSimpleMode?: boolean;
    isDebugMode?: boolean;
    chatPerformanceMode: 'speed-first' | 'quality-first';
    onMsgDataLoaded: (
        data: MsgDataType,
        questionId: string | number,
        question: string,
        valid: boolean,
        isRefresh?: boolean
    ) => void;
    onSendMsg: (value: string) => void;
};

const MessageContainer: React.FC<Props> = ({
                                               id,
                                               chatId,
                                               messageList,
                                               currentAgent,
                                               chatVisible,
                                               isDeveloper,
                                               integrateSystem,
                                               isSimpleMode,
                                               isDebugMode,
                                               onMsgDataLoaded,
                                               onSendMsg,
                                               chatPerformanceMode,
                                           }) => {
    const [triggerResize, setTriggerResize] = useState(false);
    const onResize = useCallback(() => {
        setTriggerResize(true);
        setTimeout(() => {
            setTriggerResize(false);
        }, 0);
    }, []);

    useEffect(() => {
        window.addEventListener('resize', onResize);
        return () => {
            window.removeEventListener('resize', onResize);
        };
    }, []);

    useEffect(() => {
        onResize();
    }, [chatVisible]);

    const messageContainerClass = classNames(styles.messageContainer, {[styles.mobile]: isMobile});
    return (
        <div id={id} className={messageContainerClass}>
            <div className={styles.messageList}>
                {messageList.map((msgItem: MessageItem, index: number) => {
                    const {
                        id: msgId,
                        questionId,
                        modelId,
                        agentId,
                        type,
                        msg,
                        msgValue,
                        score,
                        identityMsg,
                        parseInfos,
                        parseTimeCost,
                        msgData,
                        filters,
                    } = msgItem;

                    return (
                        <div key={msgId} id={`${msgId}`} className={styles.messageItem}>
                            {type === MessageTypeEnum.TEXT && <Text position="left" data={msg}/>}
                            {type === MessageTypeEnum.AGENT_LIST && !isMobile && (
                                <AgentTip currentAgent={currentAgent} onSendMsg={onSendMsg}/>
                            )}
                            {type === MessageTypeEnum.QUESTION && (
                                <>
                                    <Text position="right" data={msg}/>
                                    {identityMsg && <Text position="left" data={identityMsg}/>}
                                    <ChatItem
                                        questionId={questionId}
                                        currentAgent={currentAgent}
                                        isSimpleMode={isSimpleMode}
                                        isDebugMode={isDebugMode}
                                        msg={msgValue || msg || ''}
                                        parseInfos={parseInfos}
                                        parseTimeCostValue={parseTimeCost}
                                        msgData={msgData}
                                        conversationId={chatId}
                                        modelId={modelId}
                                        agentId={agentId}
                                        score={score}
                                        filter={filters}
                                        triggerResize={triggerResize}
                                        isDeveloper={isDeveloper}
                                        integrateSystem={integrateSystem}
                                        onMsgDataLoaded={(data: MsgDataType, valid: boolean, isRefresh) => {
                                            onMsgDataLoaded(data, msgId, msgValue || msg || '', valid, isRefresh);
                                        }}
                                        onUpdateMessageScroll={updateMessageContainerScroll}
                                        onSendMsg={onSendMsg}
                                        isLastMessage={index === messageList.length - 1}
                                        chatPerformanceMode={chatPerformanceMode}
                                    />
                                </>
                            )}
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

function areEqual(prevProps: Props, nextProps: Props) {
    if (
        prevProps.id === nextProps.id &&
        isEqual(prevProps.messageList, nextProps.messageList) &&
        prevProps.currentAgent === nextProps.currentAgent &&
        prevProps.chatVisible === nextProps.chatVisible &&
        prevProps.isSimpleMode === nextProps.isSimpleMode &&
        prevProps.chatPerformanceMode === nextProps.chatPerformanceMode
    ) {
        return true;
    }
    return false;
}

export default memo(MessageContainer, areEqual);
