import {CloseOutlined, SendOutlined} from '@ant-design/icons';
import {Button, message} from 'antd';
import {forwardRef, ForwardRefRenderFunction, useEffect, useRef, useState} from 'react';
import styles from './style.module.less';
import AudioGif from '/public/images/audio.gif'
import AudioStaticImg from '/public/images/audio_static.png'
import {uploadVoiceApi} from "../../../service";

type Props = {
    openVoice: boolean;
    onSendMsg: (msg: string) => void;
    onVoiceClose: () => void;
};

const VoiceInput: ForwardRefRenderFunction<any, Props> = (
    {
        openVoice,
        onSendMsg,
        onVoiceClose
    },
    ref
) => {
    const [voiceText, setVoiceText] = useState<string>()
    const medisStream = useRef<any>(null)
    const mediaRecorder = useRef<any>(null)
    const mediaBlobs = useRef<any>([])

    useEffect(() => {
        setVoiceText(undefined)
        if (openVoice) {
            startRecord()
        }
    }, [openVoice]);

    //ADD BY LYP 新增语音识别功能 --start
    //-开始录音
    const startRecord = async () => {
        try {
            medisStream.current = await navigator.mediaDevices.getUserMedia({
                audio: true,
                video: false,
            });

            mediaRecorder.current = new MediaRecorder(medisStream.current);

            mediaBlobs.current = []; // 清空录音数据数组,实现每次录音都是单独的切片，而不是带有之前音频的缓存

            mediaRecorder.current.ondataavailable = (blobEvent) => {
                mediaBlobs.current.push(blobEvent.data);
            };

            mediaRecorder.current?.start();
        } catch (error) {
            message.error("申请麦克风权限被拒绝")
            console.log('error', error);
        }
    };

    //录音完成
    const stopRecord = async () => {
        mediaRecorder.current?.stop();
        medisStream.current?.getTracks().forEach((track) => track.stop());
        if (mediaRecorder.current) {
            mediaRecorder.current.onstop = () => {
                const blob = new Blob(mediaBlobs.current, {type: 'audio/wav'});
                const formData = new FormData();
                formData.append("file", blob)

                uploadVoiceApi(formData).then((response: any) => {
                    const transcription = response?.transcription || ""
                    if (transcription.length === 0) {
                        message.info("请说点什么")
                    } else {
                        setVoiceText(transcription)
                    }
                })
                    .catch(error => {
                        console.error('文件上传出错', error);
                        message.error("语音解析失败【语音文件上传出错】")
                    });
            };
        }
    };


    return (
        <>
            {openVoice &&
                <div className={styles.voiceInputBox}>
                    <div className={styles.voiceInputBoxRes}>
                        {!voiceText && <span>说话结束，点击识别出结果</span>}
                        {voiceText && <span>{voiceText}</span>}
                    </div>
                    <div className={styles.voiceInputAction}>
                        <div className={styles.voiceInputActionLeft}>
                            <Button size="large" shape="circle" icon={<CloseOutlined/>} onClick={() => onVoiceClose()}/>
                        </div>
                        <div className={styles.voiceInputActionCenter}>
                            <img width="100%" height="100%" src={voiceText ? AudioStaticImg : AudioGif} alt=""/>
                        </div>
                        <div className={styles.voiceInputActionRight}>
                            {!voiceText &&
                                <Button size="large" shape="circle" color="cyan" variant="solid" onClick={stopRecord}>
                                    识别
                                </Button>
                            }
                            {voiceText &&
                                <Button size="large" shape="circle"
                                        type="primary"
                                        icon={<SendOutlined/>}
                                        onClick={() => {
                                            onSendMsg(voiceText)
                                        }}/>
                            }
                        </div>
                    </div>
                </div>
            }
        </>

    );
};

export default forwardRef(VoiceInput);
