import {XStream} from "@ant-design/x";
import {
    AgentMessage, MESSAGE_EVENT_END,
    MESSAGE_EVENT_INTERRUPT,
    MESSAGE_EVENT_MESSAGE, MESSAGE_EVENT_NODE_COMPLETE, MESSAGE_EVENT_REASONING,
    MESSAGE_EVENT_START,
    MESSAGE_FINISH
} from "../data";
import {isProd} from '../../../utils/utils';

export interface ChatServiceCallbacks {
    //只要有数据来都会执行
    onData?: (_event: string, tmpData: AgentMessage) => void
    //开始事件,每次流开始都有
    onStart?: (tmpData: AgentMessage) => void
    //思考过程
    onReasoning?: (_event: string, tmpData: AgentMessage) => void
    //普通消息
    onMessage?: (_event: string, tmpData: AgentMessage) => void
    //需要人机交互
    onInterrupt?: (_event: string, tmpData: AgentMessage) => void
    //单次流结束，终止加载状态
    onComplete?: () => void
    //单次查询结束、feadback反馈信息清空
    onFinish?: () => void
    onError?: (_error: Error) => void
    onTerminate?: () => void
}

const TOKEN_KEY = process.env.APP_TARGET === 'inner' ? 'TME_TOKEN' : 'SUPERSONIC_TOKEN';
const AUTH_TOKEN_KEY = localStorage.getItem(TOKEN_KEY)
const API_URL = isProd() ? "/api/agent/chat/stream" : "http://127.0.0.1:9080/api/agent/chat/stream";

export class ChatService {
    //中断控制
    private abortController = new AbortController()

    //中断请求
    public cancelRequest() {
        this.abortController.abort()
    }

    public async stream(
        params: any,
        callbacks: ChatServiceCallbacks = {},
        timeout = 300000
    ): Promise<void> {
        // 设置超时
        let timeoutId: NodeJS.Timeout | undefined;
        try {
            timeoutId = setTimeout(() => {
                this.abortController.abort(); // 中断请求
                callbacks.onTerminate?.()
                console.error("请求超时")
            }, timeout);
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: '*/*',
                    'Cache-Control': 'no-cache',
                    Connection: 'keep-alive',
                    "source": "web",
                    Authorization: `Bearer ${AUTH_TOKEN_KEY}`,
                    "auth": `Bearer ${AUTH_TOKEN_KEY}`
                },
                body: JSON.stringify(params),
                signal: this.abortController.signal
            });

            if (!response.ok) {
                const errorData = await response.json();
                callbacks.onError?.(new Error(
                    `HTTP error! status: ${response.status}${errorData.error?.message ? ` - ${errorData.error.message}` : ''}`
                ))
                console.error(`HTTP error! status: ${response.status}`);
                return;
            }
            if (!response.body) {
                const errorData = await response.json();
                callbacks.onError?.(new Error(
                    `HTTP error! status: ${response.status}${errorData.error?.message ? ` - ${errorData.error.message}` : ''}`
                ))
                console.error(`HTTP error! status: ${response.status}`);
                return;
            }
            for await (const chunk of XStream({readableStream: response.body})) {
                const {event, data} = chunk;
                if (MESSAGE_FINISH !== data) {
                    const tmpData = JSON.parse(data) as AgentMessage;
                    callbacks.onData?.(event, tmpData);
                    const node = tmpData.node;
                    //开始事件,每次流开始都有
                    if (MESSAGE_EVENT_START === node) {
                        callbacks.onStart?.(tmpData);
                    }
                    //单次查询结束、feadback反馈信息清空
                    if (MESSAGE_EVENT_END === node) {
                        callbacks.onFinish?.()
                    }
                    switch (event) {
                        //思考过程
                        case MESSAGE_EVENT_REASONING:
                            callbacks.onReasoning?.(event, tmpData);
                            break
                        //中断事件
                        case MESSAGE_EVENT_INTERRUPT:
                            callbacks.onInterrupt?.(event, tmpData)
                            break
                        //消息事件
                        case MESSAGE_EVENT_MESSAGE:
                            callbacks.onMessage?.(event, tmpData)
                            break
                        //单次流结束，终止加载状态
                        case MESSAGE_EVENT_NODE_COMPLETE:
                            callbacks.onComplete?.()
                            break

                    }
                }
            }
        } catch (error) {
            if ((error as Error).name === 'AbortError') {
                if (timeoutId) {
                    console.log('请求超时或被取消')
                    callbacks.onTerminate?.()
                } else {
                    console.log('请求被取消')
                    callbacks.onComplete?.()
                }
                return
            }
            const finalError = error instanceof Error ? error : new Error('未知错误')
            callbacks.onError?.(finalError)
            console.error(error)
        } finally {
            clearTimeout(timeoutId)
        }
    }
}

export default function useChatService() {
    return new ChatService();
}
