import Text from '../components/Text';
import React, {useEffect, useRef, useState} from 'react';
import styles from './style.module.less';
import {Button, Flex, message, Popover} from "antd";
import {
    AgentMessage,
    AgentMessageInfo,
    ChatMessage,
    EXECUTE_NODE,
    MESSAGE_EVENT_INTERRUPT,
    MESSAGE_EVENT_MESSAGE, MESSAGE_EVENT_REASONING,
    PARSE_INTENT_NODE,
    PARSE_NODE,
    REVIEW_NODE
} from "./data";
import MarkDown from "../../components/ChatMsg/MarkDown";
import classNames from "classnames";
import Loading from "../../components/ChatItem/Loading";
import SqlItem from "../../components/ChatItem/SqlItem";
import PlanModeExecute from "./PlanModeExecute";
import Tools from "../../components/Tools";
import {cloneDeep} from "lodash";
import SimilarQuestionItem from "../../components/ChatItem/SimilarQuestionItem";
import {SEARCH_EXCEPTION_TIP} from "../../common/constants";
import {SimilarQuestionType} from "../../common/type";
import {isMobile, updateMessageContainerScroll} from "../../utils/utils";
import useChatService from "./service/service";
import {exportChatMsg, handlerAddDashboard} from "./utils/utils";
import {deleteQuery} from "../../service";
import Reasoning from "./Reasoning";
import QueueAnim from 'rc-queue-anim';
import {makeUUID} from "../NextChatMessageContainer/data";

type Props = {
    id: string;
    isNewChat: boolean;
    chatId: number;
    agentId: number;
    userInputMsg: string;
    onNeedFeedBack?: () => void;
    onReviewMsg: (_msg: string) => void;
};

const PlanModeMessageContainer: React.FC<Props> = ({
                                                       id,
                                                       isNewChat,
                                                       chatId,
                                                       agentId,
                                                       onReviewMsg,
                                                       userInputMsg,
                                                       onNeedFeedBack
                                                   }) => {

        const chatService = useChatService();

        const loadingText = useRef("")
        const currentConnectionId = useRef("");
        const currentThreadId = useRef("");

        const [messageList, setMessageList] = useState<ChatMessage[]>([]);
        const [loading, setLoading] = useState<boolean>(false)
        const [errorMsg, setErrorMsg] = useState<string>();
        const [fromFeedBackMsg, setFromFeedBackMsg] = useState<boolean>(false);

        useEffect(() => {
            if (isNewChat) {
                setErrorMsg("")
                setMessageList([])
                setLoading(false)
                // 在组件卸载或依赖变化时中止请求
                return () => {
                    //中断请求
                    chatService.cancelRequest();
                };
            }
        }, [isNewChat]);

        useEffect(() => {
            if (userInputMsg) {
                createNewMsg(userInputMsg)
                if (fromFeedBackMsg) {
                    sendFeedBackRequest()
                    setFromFeedBackMsg(false)
                } else {
                    request()
                }
            }
        }, [userInputMsg]);


        const sendMessage = (params: any) => {
            setLoading(() => true)
            chatService.stream(params, {
                onData: (_event: string, tmpData: AgentMessage) => {
                    console.log("数据来", tmpData)
                    loadingText.current = tmpData.loading_text || ""
                },
                onStart: (tmpData: AgentMessage) => {
                    currentConnectionId.current = tmpData.connection_id;
                    currentThreadId.current = tmpData.thread_id;
                },
                onReasoning: (_event: string, tmpData: AgentMessage) => {
                    handlerReasoning(_event, tmpData)
                },
                onInterrupt: (_event, msg: AgentMessage) => {
                    setFromFeedBackMsg(true)
                    //中断事件
                    handlerInterruptMessageCallBack(_event, msg)
                    updateMessageContainerScroll(id);
                },
                onMessage: (_event, msg: AgentMessage) => {
                    handlerMessageCallBack(_event, msg)
                    const timer = setTimeout(() => {
                        updateMessageContainerScroll(id)
                        clearTimeout(timer)
                    }, 800)
                },
                onComplete: () => {
                    setLoading(() => false)
                    currentConnectionId.current = "";
                    const timer = setTimeout(() => {
                        updateMessageContainerScroll(id)
                        clearTimeout(timer)
                    }, 800)
                },
                onError: () => {
                    setErrorMsg("出错啦~")
                    setLoading(() => false)
                },
                onTerminate: () => {
                    setErrorMsg("请求超时请重试")
                    setLoading(() => false)
                },
                onFinish: () => {
                    setLoading(() => false)
                    currentConnectionId.current = "";
                    currentThreadId.current = "";
                    updateMessageContainerScroll(id);
                }
            })
        }

        const handlerReasoning = (event: string, tmpData: AgentMessage) => {
            const reasoning_content = tmpData.choices[0].delta.reasoning_content
            //抛弃content为空的消息 张茜的成绩
            if (reasoning_content) {
                setMessageList((pre: any) => {
                    const tmpMessageList = cloneDeep(pre) || []

                    const tmpList = tmpMessageList?.filter((item: ChatMessage) => (item.messageData as AgentMessageInfo).event === MESSAGE_EVENT_REASONING && (currentConnectionId.current === item.connection_id)) || [];
                    let tmpMessage = tmpList.pop();

                    if (!tmpMessage) {
                        const tmp = {
                            role: 'agent',
                            connection_id: currentConnectionId.current,
                            chatId: chatId,
                            messageData: {event, data: tmpData}
                        }
                        return [...((pre || []) as ChatMessage[]), tmp]
                    }
                    const tmpMsg = tmpMessage as ChatMessage
                    const messageData = tmpMsg.messageData as AgentMessageInfo
                    messageData.data.choices[0].delta.reasoning_content += reasoning_content
                    return tmpMessageList.map((item: any) => {
                        const flag = (currentConnectionId.current === tmpMessage.connection_id) && (item.messageData as AgentMessageInfo).event === MESSAGE_EVENT_REASONING
                        return flag ? tmpMsg : item
                    })
                })
            }
        }


        function createNewMsg(msg: string) {
            setMessageList((pre) => {
                const tmpMessage: ChatMessage = {
                    role: 'user',
                    chat_id: chatId,
                    messageData: msg,
                }
                return [...pre, tmpMessage]
            })
            updateMessageContainerScroll(id);
        }

        function sendFeedBackRequest() {
            const params = {
                "messages": [],
                "feedback": userInputMsg,
                "chatId": chatId,
                "agentId": agentId,
                "threadId": currentThreadId.current
            }
            sendMessage(params);
        }

        function request() {
            const params = {
                "messages": [
                    {
                        "role": "user",
                        "content": userInputMsg
                    }
                ],
                "chatId": chatId,
                "agentId": agentId
            }
            sendMessage(params);
        }

        const handlerInterruptMessageCallBack = (event: string, tmpData: AgentMessage) => {
            const node = tmpData.node;
            if (PARSE_INTENT_NODE.includes(node) && !tmpData.extra_info) {
                setFromFeedBackMsg(true)
                //需要补充信息
                onNeedFeedBack?.()
            }
            setMessageList((pre) => {
                const tmpMessageList = cloneDeep(pre) || []
                let tmpMessage = tmpMessageList.pop();
                if (!tmpMessage || tmpMessage.role === 'user' || (tmpMessage.messageData as AgentMessageInfo).event !== MESSAGE_EVENT_INTERRUPT) {
                    const tmp: ChatMessage = {
                        role: 'agent',
                        connection_id: tmpData.connection_id,
                        chat_id: chatId,
                        messageData: {event, data: tmpData}
                    }
                    return [...pre, tmp]
                }

                const messageData = tmpMessage.messageData as AgentMessageInfo
                messageData.data.extra_info = tmpData.extra_info;
                messageData.data.node = tmpData.node;
                messageData.event = event;
                tmpMessage.messageData = messageData
                tmpMessageList.push(tmpMessage);
                return [...tmpMessageList]
            })
            updateMessageContainerScroll(id);
        }

        const handlerGeneralMessage = (event: string, tmpData: AgentMessage) => {
            const content = tmpData.choices[0].delta.content
            //抛弃content为空的消息
            const tmpStr = content.replace("    \n\n", "");
            if (!tmpStr || (tmpStr.length === "\n\n".length)) {
                return
            }
            if (content) {
                setMessageList((pre: any) => {
                    const tmpMessageList = cloneDeep(pre) || []
                    let tmpMessage = tmpMessageList?.pop();
                    const flag = !tmpMessage || tmpMessage.role === 'user' || (tmpMessage.messageData as AgentMessageInfo).event !== MESSAGE_EVENT_MESSAGE
                    if (flag) {
                        const tmp = {
                            role: 'agent',
                            connection_id: tmpData.connection_id,
                            chatId: chatId,
                            messageData: {event, data: tmpData}
                        }
                        return [...((pre || []) as ChatMessage[]), tmp]
                    }
                    const tmpMsg = tmpMessage as ChatMessage
                    const messageData = tmpMsg.messageData as AgentMessageInfo
                    messageData.data.choices[0].delta.content += content
                    tmpMessageList.push(tmpMsg);
                    return [...tmpMessageList]
                })
                updateMessageContainerScroll(id);
            }
        }

        const handlerMessageCallBack = (event: string, tmpData: AgentMessage) => {
            const node = tmpData.node;
            //普通消息
            if (node !== PARSE_NODE && node !== EXECUTE_NODE) {
                handlerGeneralMessage(event, tmpData);
                return;
            }
            //最终结果、parse 和执行阶段消息
            const content: any = tmpData.response
            if (content) {
                setMessageList((pre) => {
                    const tmpMessage: ChatMessage = {
                        role: 'agent',
                        connection_id: tmpData.connection_id,
                        chat_id: chatId,
                        messageData: {event, data: tmpData}
                    }
                    return [...pre, tmpMessage]
                })
            }
        }


        const onFeedBackConfirm = (record: AgentMessageInfo, action: string) => {
            const params = {
                "messages": [
                    {
                        "content": record.data.choices[0].delta.content,
                    }
                ],
                "agentId": agentId,
                "chatId": chatId,
                "feedback": action,
                "threadId": currentThreadId.current,
            }
            createNewMsg(record.data.choices[0].delta.content);
            setIsFeedBack(record)
            sendMessage(params);
            setFromFeedBackMsg(false)
        }

        const handlerReviewMsg = (record: AgentMessageInfo) => {
            const content = record.data.choices[0].delta.content
            onReviewMsg(content)
            setIsFeedBack(record)
        }

        const createFeedBackConfirmDom = (record: AgentMessageInfo) => {
            return (
                <QueueAnim
                    delay={800}
                    type={['right', 'left']}
                    leaveReverse>
                    <div key="FeedBackConfirm" className={styles.planMessageBox}>
                        <div className={styles.planMessageHeader} style={{marginBottom: '1rem'}}>
                            <div
                                className={styles.planMessageHeaderTip}>
                                {!record.isFeedBack &&
                                    <span>我将为您进行以下操作，如有需要调整的地方，点击右侧编辑按钮即可。</span>
                                }
                                {record.isFeedBack && <span>感谢确认。</span>}
                            </div>
                            {!record.isFeedBack &&
                                <Flex gap="small">
                                    <div className={styles.planModeMessageAction}>
                                        <Popover color="#BD4C43" content={<div style={{color: 'white'}}>点我编辑</div>}
                                                 open>
                                            <Button size="small" onClick={() => handlerReviewMsg(record)}>编辑</Button>
                                        </Popover>
                                    </div>
                                    <div className={styles.planModeMessageAction}>
                                        <Button type="primary" size="small"
                                                onClick={() => onFeedBackConfirm(record, "accepted")}>确定</Button>
                                    </div>
                                </Flex>
                            }
                        </div>
                        <div className={styles.editDiv}>
                            {record.data.choices[0].delta.content}
                        </div>
                    </div>
                </QueueAnim>
            )
        }

        const onPickerSelect = (record: AgentMessageInfo, picker: any) => {
            if (record.isFeedBack) {
                return
            }

            const selectionMode = record.data.extra_info?.selectionMode

            setMessageList((pre) => {
                const feedBackSelect = record.feedBackSelect || []
                record.feedBackSelect = [...feedBackSelect, picker]
                if ("single" === selectionMode) {
                    record.feedBackSelect = [picker]
                }
                return pre.map(item => {
                    return (item.messageData as AgentMessageInfo).data?.id === record.data?.id ? {
                        ...item,
                        messageData: record
                    } : item;
                })
            })
        }

        const onSubmitPickerSelect = (record: AgentMessageInfo) => {
            const feedBackSelect = record.feedBackSelect || []

            if (!record.data.extra_info) {
                feedBackSelect.push({value: record.data.choices[0].delta.content})
            }

            if (feedBackSelect.length === 0) {
                message.warning("请先选择")
                return
            }
            const feedback = feedBackSelect.map((item: any) => item.value).join(" ")
            const params = {
                "messages": [{"content": ""}],
                "feedback": feedback,
                "chatId": chatId,
                "agentId": agentId,
                "threadId": currentThreadId.current
            }
            setIsFeedBack(record)
            createNewMsg(feedback);
            sendMessage(params);
            setFromFeedBackMsg(false)
        }

        const setIsFeedBack = (record: AgentMessageInfo) => {
            setMessageList((pre) => {
                return pre.map(item => {
                    if (item.role === 'agent') {
                        return (item.messageData as AgentMessageInfo).data.id === record.data.id ? {
                            ...item,
                            messageData: {...record, isFeedBack: true}
                        } : item;
                    }
                    return item;
                })
            })
        }

        const createFeedBackGuideDom=(record: AgentMessageInfo)=>{
            return (
                <QueueAnim
                    delay={800}
                    type={['right', 'left']}
                    leaveReverse>
                    <div key="FeedBackSelect" className={styles.planMessageBox}>
                        <div className={styles.planMessageHeader}>
                            <div className={styles.planMessageHeaderTitle}>
                                <div className={styles.title}>需求确认</div>
                                {record.data.extra_info &&
                                    <div className={styles.planMessageHeaderTitleDesc}>
                                        {/*请选择需求描述，以便于AI更准确判断需求。*/}
                                        {record.data.choices[0].delta.content}
                                    </div>
                                }
                            </div>
                        </div>
                        <div className={styles.planMessageOptions}>
                            {record.data?.extra_info?.options?.map((item, index) => {
                                return <div key={makeUUID()} className={styles.planMessageOption}>{item.text}</div>
                            })}
                            {!record.data?.extra_info &&
                                <div
                                    className={classNames(styles.planMessageOption, {
                                        [styles.planMessageOptionActive]: true
                                    })}
                                    onClick={() => onPickerSelect(record, {value: record.data.choices[0].delta.content})}>
                                    {record.data.choices[0].delta.content}
                                </div>
                            }
                        </div>
                    </div>
                </QueueAnim>
            )
        }

        const createFeedBackSelectDom = (record: AgentMessageInfo) => {
            const type = record.data.extra_info?.type;
            if ("guide" === type) {
                return createFeedBackGuideDom(record)
            }

            return (
                <QueueAnim
                    delay={800}
                    type={['right', 'left']}
                    leaveReverse>
                    <div key="FeedBackSelect" className={styles.planMessageBox}>
                        <div className={styles.planMessageHeader}>
                            <div className={styles.planMessageHeaderTitle}>
                                <div className={styles.title}>需求确认</div>
                                {record.data.extra_info &&
                                    <div className={styles.planMessageHeaderTitleDesc}>
                                        {/*请选择需求描述，以便于AI更准确判断需求。*/}
                                        {record.data.choices[0].delta.content}
                                    </div>
                                }
                            </div>
                        </div>
                        <div className={styles.planMessageOptions}>
                            <Popover color="#BD4C43" content={<div style={{color: 'white'}}>1、选择需求</div>}
                                     placement="rightTop"
                                     open={!record.isFeedBack}>
                                {record.data?.extra_info?.options?.map((item, index) => {
                                    return (
                                        <div key={makeUUID()}
                                             className={classNames(styles.planMessageOption, {
                                                 [styles.planMessageOptionActive]: record?.feedBackSelect?.find(it => it === item)
                                             })}
                                             onClick={() => onPickerSelect(record, item)}>{item.text}</div>
                                    )
                                })}
                            </Popover>
                            {!record.data?.extra_info &&
                                <div
                                    className={classNames(styles.planMessageOption, {
                                        [styles.planMessageOptionActive]: true
                                    })}
                                    onClick={() => onPickerSelect(record, {value: record.data.choices[0].delta.content})}>
                                    {record.data.choices[0].delta.content}
                                </div>
                            }
                        </div>
                        {!record.isFeedBack && record.data.extra_info &&
                            <div className={styles.planModeMessageAction}>
                                <Popover color="#BD4C43" content={<div style={{color: 'white'}}>2、提交需求</div>}
                                         placement="rightTop"
                                         open>
                                    <Button type="primary" size="small"
                                            onClick={() => onSubmitPickerSelect(record)}>确定</Button>
                                </Popover>
                            </div>
                        }
                    </div>
                </QueueAnim>
            )
        }

        const onSelectQuestion = (question: SimilarQuestionType) => {
            const params = {
                "messages": [
                    {
                        "role": "user",
                        "content": question.queryText
                    }
                ],
                "chatId": chatId,
                "agentId": agentId
            }
            sendMessage(params);
            setFromFeedBackMsg(false)
        };

        const handlerExecuteDom = (messageData: AgentMessageInfo) => {
            const executeData = messageData.data.response;
            const textSummary = executeData.textSummary
            const chatContext = executeData?.chatContext
            const score = chatContext?.score || 0
            const queryId = executeData.queryId
            const queryState = executeData.queryState
            const queryColumns = executeData.queryColumns
            const queryResults = executeData.queryResults
            const queryMode = executeData.queryMode
            let executeTip = SEARCH_EXCEPTION_TIP;
            if (queryState !== 'SUCCESS') {
                executeTip = executeData.response && typeof executeData.response === 'string' ? executeData.response : SEARCH_EXCEPTION_TIP;
            } else if (
                (queryColumns && queryColumns.length > 0 && queryResults) ||
                queryMode === 'WEB_PAGE' ||
                queryMode === 'WEB_SERVICE' ||
                queryMode === 'PLAIN_TEXT'
            ) {
                executeTip = '';
            }
            if (chatContext) {
                executeTip = '';
            }

            return (
                <>
                    <QueueAnim
                        delay={1000}
                        type={['right', 'left']}
                        leaveReverse>
                        {chatContext &&
                            <div key="planModeExecute" className={styles.planMessageBox}>
                                <PlanModeExecute executeData={executeData}/>
                            </div>
                        }
                        {!chatContext &&
                            <div key="content" className={styles.planMessageBox}>
                                {messageData.data.choices[0].delta.content}
                            </div>
                        }
                        {textSummary &&
                            <div key="textSummary" className={styles.planMessageBox} style={{marginTop: '1rem'}}>
                                <MarkDown markdown={textSummary}></MarkDown>
                            </div>
                        }
                        <div key="similarQuestionItem" className={styles.planMessageBox} style={{marginTop: '1rem'}}>
                            <SimilarQuestionItem
                                queryId={queryId}
                                defaultExpanded={executeTip !== ''}
                                similarQueries={executeData?.similarQueries}
                                onSelectQuestion={onSelectQuestion}
                            />
                        </div>
                        <Tools
                            key="tools"
                            isLastMessage={true}
                            queryId={queryId || 0}
                            scoreValue={score}
                            isParserError={false}
                            addDashboardLoading={false}
                            showExportDataBtn={!!chatContext}
                            showAddDashboardBtn={!!chatContext}
                            onExportData={() => exportChatMsg(queryResults, queryColumns)}
                            onReExecute={queryId => {
                                deleteQueryInfo(queryId, chatContext);
                            }}
                            onAddDashboard={() => handlerAddDashboard(chatContext)}
                        />
                    </QueueAnim>
                </>
            )
        }

        const deleteQueryInfo = async (queryId: number, selectedParses: any) => {
            const {code}: any = await deleteQuery(queryId);
            if (code === 200) {
                const properties = selectedParses.properties
                const queryText = properties.CONTEXT.llmReq.queryText;
                setMessageList([])
                setLoading(false)
                createNewMsg(queryText)
                const params = {
                    "messages": [
                        {
                            "role": "user",
                            "content": queryText
                        }
                    ],
                    "chatId": chatId,
                    "agentId": agentId
                }
                sendMessage(params);
            }
        };

        const handlerParseDom = (messageData: AgentMessageInfo) => {
            const response = messageData.data.response;
            const selectedParses = response.selectedParses[0]
            const sqlInfo = selectedParses.sqlInfo
            return !isMobile && (
                <div className={styles.planMessageBox}>
                    <SqlItem
                        question={''}
                        sqlInfo={sqlInfo}
                        executeErrorMsg={''}/>
                </div>)
        }

        const createAgentMsgDom = (messageData: AgentMessageInfo) => {
            return (
                <>
                    {messageData.event === MESSAGE_EVENT_REASONING &&
                        <>
                            <Reasoning reasoning={messageData.data.choices[0].delta.reasoning_content || ""}
                                       loading={loading}/>
                        </>
                    }
                    {messageData.event === MESSAGE_EVENT_INTERRUPT &&
                        <>
                            {PARSE_INTENT_NODE.includes(messageData.data.node) && createFeedBackSelectDom(messageData)}
                            {messageData.data.node === REVIEW_NODE && createFeedBackConfirmDom(messageData)}
                        </>
                    }
                    {messageData.event === MESSAGE_EVENT_MESSAGE &&
                        <>
                            {messageData.data.node !== PARSE_NODE && messageData.data.node !== EXECUTE_NODE &&
                                <div className={styles.tip}>
                                    <MarkDown markdown={messageData.data.choices[0].delta.content}
                                              loading={false}/>
                                </div>
                            }
                            {messageData.data.node === PARSE_NODE && handlerParseDom(messageData)}
                            {messageData.data.node === EXECUTE_NODE && handlerExecuteDom(messageData)}
                        </>
                    }
                </>
            )
        }

        return (
            <div id={id} className={styles.planModeMessageContainer}>
                <div className={styles.messageList}>
                    {messageList?.map((item, index) => {
                        const messageData = item.messageData as AgentMessageInfo
                        return (
                            <div key={index}>
                                {item.role === 'user' && <Text position="right" data={item.messageData}/>}
                                {item.role === 'agent' && createAgentMsgDom(messageData)}
                            </div>
                        )
                    })}
                    <QueueAnim
                        type={['right', 'left']}
                        leaveReverse>
                        {loading ? [
                            <div key="loading" className={styles.loading}>
                                <Flex>
                                    {loadingText.current || "任务已接收，请稍后"}
                                    <Loading/>
                                </Flex>
                            </div>
                        ] : null}
                    </QueueAnim>

                    {errorMsg &&
                        <div key="errorMsg" className={styles.loading}>
                            {errorMsg}
                        </div>
                    }
                </div>
            </div>
        );
    }
;


export default PlanModeMessageContainer;
