import styles from './style.less'
import React, {useEffect, useState} from "react";
import {Flex, Table} from "antd";
import {DownOutlined, UpOutlined} from "@ant-design/icons";
import "./FancyTable.css"
import QueueAnim from "rc-queue-anim";
import {makeUUID} from "../utils";
import {QueryGuidance} from "../../../Chat/NextChatMessageContainer/data";
import {loadDataByView} from "../../../Chat/NextChatMessageContainer/service/api";

type Props = {
    icon: any,
    title: string
    msgData: any
    columns: any
    queryGuidance: QueryGuidance
};

const StripeTable: React.FC<Props> = ({icon, title, msgData, columns, queryGuidance}) => {

    const [openBody, setOpenBody] = useState<boolean>(false);
    const [dataSource, setDataSource] = useState<any>([]);
    const [columnList, setColumnList] = useState<any>([]);

    useEffect(() => {
        if (queryGuidance && msgData.length > 0) {
            const paramToDataColumnMapping = queryGuidance?.paramToDataColumnMapping || {}
            const viewName = queryGuidance?.name || ""
            const params = {}
            Object.keys(paramToDataColumnMapping)?.forEach(key => {
                params[key] = msgData[0][key]
            });
            const queryParams = {
                viewName: viewName,
                params: params
            }
            loadDataByView(queryParams).then((resp: any) => {
                const {data, code} = resp
                if (200 === code) {
                    setDataSource(data.data)
                }
            })
        }
    }, [queryGuidance]);

    useEffect(() => {
        if (columns.length > 0) {
            const tmpColumns = columns.map((item: any) => {
                const title = item.name || item.description;
                return {title: title, dataIndex: title, key: makeUUID(), align: 'center'}
            })
            setColumnList(tmpColumns);
        }
    }, [columns]);

    const setRowClassName = (_record, index: number) => {
        return index % 2 === 0 ? 'fancy-table-row' : 'table-row-odd';
    };

    const createBodyDom = () => {
        return (
            <div key={makeUUID()} className={styles.body}>
                <div className="fancy-table-container">
                    <Table
                        rowHoverable={false}
                        size='small'
                        dataSource={dataSource}
                        columns={columnList}
                        pagination={false}
                        rowClassName={setRowClassName}
                        className="fancy-table"
                    />
                </div>
            </div>
        )
    }

    return <div className={styles.stripeTableBox}>
        <div className={styles.header}>
            <Flex gap="small" align="center">
                <div className={styles.icon}>{icon}</div>
                <div className={styles.title}>{title}</div>
                <div className={styles.btn} onClick={() => setOpenBody(!openBody)}>
                    {openBody && <DownOutlined/>}
                    {!openBody && <UpOutlined/>}
                </div>
            </Flex>
        </div>
        <QueueAnim type={['bottom', 'top']} leaveReverse>
            {openBody ? createBodyDom() : null}
        </QueueAnim>
    </div>;
};

export default StripeTable;
