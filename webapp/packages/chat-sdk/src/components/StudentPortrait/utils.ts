//将一维数组拆成二维数组
export function chunkArray(arr: any[], size: number): any {
    if (arr.length === 0) {
        return arr;
    }
    const result = [];
    for (let i = 0; i < arr.length; i += size) {
        result.push(arr.slice(i, i + size));
    }
    return result;
}
export const makeUUID = () => {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    const uuid: string[] = [];
    for (let i = 0; i < 32; i++) {
        uuid[i] = chars[Math.floor(Math.random() * 62)];
    }
    return uuid.join('');
}
