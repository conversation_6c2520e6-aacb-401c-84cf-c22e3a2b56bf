import styles from './style.less'
import React, {useEffect, useState} from "react";
import StudentBaseInfo from "./StudentBaseInfo";
import {
    PieChartOutlined,
    TagsOutlined,
    SolutionOutlined,
    ScheduleOutlined,
    ContactsOutlined,
    InteractionOutlined, FileProtectOutlined, ShoppingCartOutlined, HomeOutlined, IdcardOutlined, FileTextOutlined
} from '@ant-design/icons'
import CustomTable from "./CustomTable";
import StripeTable from "./StripeTable";
import {AgentMessageInfo, AvailableViews, ChatMessage, QueryGuidance} from "../../Chat/NextChatMessageContainer/data";
import {loadDataByView} from "../../Chat/NextChatMessageContainer/service/api";
import {Empty} from "antd";
import {makeUUID} from "./utils";

type Props = {
    chatMessage: ChatMessage
};

const iconList: any[] = [
    <ContactsOutlined/>, <ScheduleOutlined/>, <PieChartOutlined/>, <InteractionOutlined/>,
    <SolutionOutlined/>, <TagsOutlined/>, <FileProtectOutlined/>,<ShoppingCartOutlined />,
    <HomeOutlined />,<IdcardOutlined />,<FileTextOutlined />]

const StudentPortrait: React.FC<Props> = ({chatMessage}) => {

    const [studentBaseInfoColumns, setStudentBaseInfoColumns] = useState<any>([]);
    const [studentBaseInfoData, setStudentBaseInfoData] = useState<any>([]);
    const [availableViewList, setAvailableViewList] = useState<AvailableViews[]>([]);
    const [queryGuidanceList, setQueryGuidanceList] = useState<QueryGuidance[]>([]);
    const [msgDataList, setMsgDataList] = useState<Record<any, any>[]>([]);

    useEffect(() => {
        if (chatMessage) {
            const messageData = chatMessage.messageData as AgentMessageInfo;
            const msgData = messageData.data || [];
            const relatedViewQueryGuidance = messageData?.relatedViewQueryGuidance
            const availableViews = relatedViewQueryGuidance?.availableViews || []
            const queryGuidance = relatedViewQueryGuidance?.queryGuidance || []
            const primary = availableViews.find(item => item.primary);
            const baseModelName = primary?.baseModelName
            const viewName = primary?.name || ""
            const primaryQueryGuidance = queryGuidance.find(item => item.baseModelName === baseModelName)
            const paramToDataColumnMapping = primaryQueryGuidance?.paramToDataColumnMapping || {}

            if (msgData.length > 0) {
                setMsgDataList(msgData)
                setQueryGuidanceList(queryGuidance.slice(1))
                setAvailableViewList(availableViews.slice(1));

                const params = {}
                Object.keys(paramToDataColumnMapping)?.forEach(key => {
                    params[key] = msgData[0][key] || ""
                });
                setStudentBaseInfoColumns(primary?.columns || [])

                const queryParams = {
                    viewName: viewName,
                    params: params
                }
                loadDataByView(queryParams).then((resp: any) => {
                    const {data, code} = resp
                    if (200 === code) {
                        setStudentBaseInfoData(data.data[0] || {});
                    }
                })
            }
        }
    }, [chatMessage]);

    return <div className={styles.studentPortraitBox}>
        {studentBaseInfoColumns.length === 0 && <Empty description="查无此人"/>}
        {studentBaseInfoColumns.length > 0 && (
            <>
                <StudentBaseInfo columns={studentBaseInfoColumns} dataSource={studentBaseInfoData} icon={iconList[0]}/>
                {availableViewList.map((item: any, index: number) => {
                    const {columns, title} = item;
                    return (
                        <div key={makeUUID()}>
                            {columns.length > 5 &&
                                <CustomTable msgData={msgDataList} queryGuidance={queryGuidanceList[index]}
                                             columns={columns}
                                             icon={iconList[(index + 1)] || iconList[5]} title={title}/>}
                            {columns.length < 6 &&
                                <StripeTable msgData={msgDataList} queryGuidance={queryGuidanceList[index]}
                                             columns={columns}
                                             icon={iconList[(index + 1)] || iconList[5]} title={title}/>}
                        </div>
                    )
                })}
            </>
        )}
    </div>;
};

export default StudentPortrait;
