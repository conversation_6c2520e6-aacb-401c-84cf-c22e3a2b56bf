import styles from './style.less'
import React, {useCallback, useEffect, useRef, useState} from "react";
import {chunkArray, makeUUID} from "../utils";
import {Flex} from "antd";
import {DownOutlined, UpOutlined} from "@ant-design/icons";
import QueueAnim from "rc-queue-anim";

type Props = {
    icon: any,
    title: string
    data?: any
};

const StudentOtherInfo: React.FC<Props> = ({icon, title, data}) => {

    const studentOtherInfoBoxRef = useRef<HTMLDivElement>(null);

    const [dataList, setDataList] = useState([]);
    const [openBody, setOpenBody] = useState<boolean>(true);

    const onResize = useCallback(() => {
        const divRef = studentOtherInfoBoxRef.current;
        if (divRef) {
            const tmp = [];
            for (let i = 0; i < 10; i++) {
                tmp.push(i);
            }
            const width = divRef.offsetWidth;
            const size = Number(width / 176).toFixed(0)
            setDataList(chunkArray(tmp, Number(size)))
            console.log("哈哈", size, divRef.offsetWidth);
        }
    }, []);

    useEffect(() => {
        onResize()
    }, []);

    useEffect(() => {
        window.addEventListener('resize', onResize);
        return () => {
            window.removeEventListener('resize', onResize);
        };
    }, []);

    const createBodyDom = () => {
        return (
            <div key={makeUUID()} className={styles.body}>
                {dataList.map((item: any, index: number) => {
                    return (
                        <div key={makeUUID()} className={styles.row} style={{width: `${item.length * 11.4}rem`}}>
                            {
                                item.map((record: any, _index: number) => {
                                    return (
                                        <div key={makeUUID()} className={styles.item}
                                             style={{borderRight: _index === (item.length - 1) ? 'none' : '1px solid #CBCBCB'}}>
                                            <div className={styles.itemLabel}>学号：{_index}-{item.length}</div>
                                            <div className={styles.itemValue}>************</div>
                                        </div>
                                    )
                                })
                            }
                        </div>
                    )
                })}
            </div>
        )
    }

    return <div ref={studentOtherInfoBoxRef} className={styles.studentOtherInfoBox}>
        <div className={styles.header}>
            <Flex gap="small" align="center">
                <div className={styles.icon}>{icon}</div>
                <div className={styles.title}>{title}</div>
                <div className={styles.btn} onClick={() => setOpenBody(!openBody)}>
                    {openBody && <DownOutlined/>}
                    {!openBody && <UpOutlined/>}
                </div>
            </Flex>
        </div>
        <QueueAnim type={['bottom', 'top']} leaveReverse>
            {openBody ? createBodyDom() : null}
        </QueueAnim>
    </div>;
};

export default StudentOtherInfo;
