import styles from './style.less'
import React from "react";
import {Flex} from "antd";
import {makeUUID} from "../utils";

type Props = {
    icon: any
    dataSource: any
    columns?: any
};

const StudentBaseInfo: React.FC<Props> = ({icon, dataSource, columns}) => {


    return <div className={styles.studentBaseInfoBox}>
        <div className={styles.header}>
            <Flex align="center" gap="small">
                <div className={styles.icon}>{icon}</div>
                <div className={styles.title}>基本信息</div>
            </Flex>
        </div>
        <div className={styles.body}>
            {columns.map((column: any, index: number) => {
                return (
                    <div key={makeUUID()} className={styles.item}>
                        <div className={styles.itemLabel}>{column.name || column.description}：</div>
                        <div className={styles.itemValue}>{dataSource[column.name]}</div>
                    </div>
                )
            })}
        </div>
    </div>;
};

export default StudentBaseInfo;
