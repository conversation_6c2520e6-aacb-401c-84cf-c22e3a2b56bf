.studentBaseInfoBox{
  background: #fff;
  .header{
    margin-top: 17px;
    margin-left: 10px;
    margin-bottom: 10px;
    .icon{
      color: #78231C;
      font-size: 1.2rem;
    }
    .title{
      font-weight: 500;
      font-size: 1.2rem;
      color: #1E1E1E;
    }
  }
  .body{
    display: flex;
    flex-wrap: wrap;
    .item{
      width: 11.4rem;
      margin: 10px;
      display: flex;
      .itemValue{
        font-weight: 400;
        font-size: .9rem;
        color: #1E1E1E;
      }
      .itemLabel{
        width: 5rem;
        text-align: right;
        font-weight: 500;
        font-size: .9rem;
        color: #1E1E1E;
      }
    }
  }
}
