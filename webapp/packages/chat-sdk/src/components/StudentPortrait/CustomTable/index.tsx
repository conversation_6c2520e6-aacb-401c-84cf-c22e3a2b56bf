import styles from './style.less'
import React, {useCallback, useEffect, useRef, useState} from "react";
import {chunkArray, makeUUID} from "../utils";
import {Empty, Flex} from "antd";
import {DownOutlined, UpOutlined} from "@ant-design/icons";
import QueueAnim from "rc-queue-anim";
import {QueryGuidance} from "../../../Chat/NextChatMessageContainer/data";
import {loadDataByView} from "../../../Chat/NextChatMessageContainer/service/api";
import {isMobile} from "../../../utils/utils";

type Props = {
    icon: any,
    title: string
    msgData: any
    columns: any
    queryGuidance: QueryGuidance
};

const CustomTable: React.FC<Props> = ({icon, title, msgData, queryGuidance, columns}) => {

    const customTableBoxRef = useRef<HTMLDivElement>(null);

    const [columnList, setColumnsList] = useState<any>([]);
    const [dataSource, setDataSource] = useState<any>([]);
    const [openBody, setOpenBody] = useState<boolean>(false);

    const onResize = useCallback(() => {
        const divRef = customTableBoxRef.current;
        if (divRef) {
            const width = divRef.offsetWidth;
            const size = Number(width / 176).toFixed(0)
            setColumnsList(chunkArray(columns, Number(size)))
        }
    }, []);

    useEffect(() => {

        if (queryGuidance && msgData.length > 0) {
            const paramToDataColumnMapping = queryGuidance?.paramToDataColumnMapping || {}
            const viewName = queryGuidance?.name || ""
            const params = {}
            Object.keys(paramToDataColumnMapping)?.forEach(key => {
                params[key] = msgData[0][key]
            });
            const queryParams = {
                viewName: viewName,
                params: params
            }
            loadDataByView(queryParams).then((resp: any) => {
                const {data, code} = resp
                if (200 === code) {
                    setDataSource(data.data)
                }
            })
        }
    }, [queryGuidance]);

    useEffect(() => {
        onResize()
    }, []);

    useEffect(() => {
        onResize()
    }, [columns]);

    useEffect(() => {
        window.addEventListener('resize', onResize);
        return () => {
            window.removeEventListener('resize', onResize);
        };
    }, []);

    const createBodyItemDom = (record: any) => {
        return (
            <div key={makeUUID()} className={styles.bodyItem}>
                {columnList.map((item: any) => {
                    return (
                        <div key={makeUUID()} className={styles.row}
                             style={{width: isMobile ? `${item.length * 9.6}rem` : `${item.length * 12}rem`}}>
                            {
                                item.map((childItem: any, _index: number) => {
                                    return (
                                        <div className={styles.item}
                                             key={makeUUID()}
                                             style={{borderRight: _index === (item.length - 1) ? 'none' : '1px solid #CBCBCB'}}>
                                            <div className={styles.itemLabel}>{childItem.name}</div>
                                            <div className={styles.itemValue}>
                                                {record[childItem.name || childItem.description] || "--"}
                                            </div>
                                        </div>
                                    )
                                })
                            }
                        </div>
                    )
                })}
            </div>
        )
    }

    const createBodyDom = () => {
        return (
            <div key={makeUUID()} className={styles.body}>
                {dataSource.length === 0 && <Empty/>}
                {dataSource.map((record: any) => {
                    return createBodyItemDom(record)
                })}
            </div>
        )
    }

    return <div ref={customTableBoxRef} className={styles.customTableBox}>
        <div className={styles.header}>
            <Flex gap="small" align="center">
                <div className={styles.icon}>{icon}</div>
                <div className={styles.title}>{title}</div>
                <div className={styles.btn} onClick={() => setOpenBody(!openBody)}>
                    {openBody && <DownOutlined/>}
                    {!openBody && <UpOutlined/>}
                </div>
            </Flex>
        </div>
        <QueueAnim type={['bottom', 'top']} leaveReverse>
            {openBody ? createBodyDom() : null}
        </QueueAnim>
    </div>;
};

export default CustomTable;
