.customTableBox {
  background: #fff;

  .header {
    margin-top: 17px;
    margin-left: 10px;
    margin-bottom: 10px;

    .btn {
      cursor: pointer;
      font-size: .8rem;
    }

    .icon {
      color: #78231C;
      font-size: 1.2rem;
    }

    .title {
      font-weight: 500;
      font-size: 1.2rem;
      color: #1E1E1E;
    }
  }

  .body {
    .bodyItem {
      margin-top: 10px;
      padding: 10px 10px 2px 10px;
      background-color: #E9E9E9;
      border-radius: 10px;

      .row {
        display: flex;
        background: white;
        border-radius: 4px;
        margin-bottom: .8rem;

        .item {
          width: 11rem;
          padding-left: 15px;
          margin: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          padding-right: 20px;
          border-right: 1px solid #CBCBCB;

          .itemValue {
            font-weight: 800;
            font-size: 1rem;
            color: #78231C;
          }

          .itemLabel {
            margin-bottom: 10px;
            font-weight: 300;
            font-size: .8rem;
            color: #797979;
          }
        }
      }
    }
  }
}
