import React from 'react';
import {Avatar} from 'antd';
import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import 'dayjs/locale/zh-cn';
import styles from './ThinkTime.style.less'
import MsgAgentIMg from '/public/msg_agent.png'
import {prefixCls} from "../ParseTipUtils";
import Loading from "../Loading";
import classNames from "classnames";
import {ChatContextType} from "../../../common/type";

dayjs.extend(quarterOfYear);
dayjs.locale('zh-cn');


type Props = {
    parseLoading: boolean;
    parseInfoOptions: ChatContextType[];
    parseTip: string;
    parseTimeCost?: number;
    isDeveloper?: boolean;
    isSimpleMode?: boolean;
};


const ThinkTime: React.FC<Props> = ({
                                        parseInfoOptions,
                                        isSimpleMode,
                                        parseLoading,
                                        parseTip,
                                        parseTimeCost,
                                        isDeveloper,
                                    }) => {

    if (parseLoading) {
        return (
            <div className={`${prefixCls}-title-bar`}>
                <div className={`${prefixCls}-step-title`}>
                    思考中
                    <Loading/>
                </div>
            </div>
        )
    }

    if (parseTip) {
        return (
            <>
                <div className={`${prefixCls}-title-bar`}>
                    <div className={`${prefixCls}-step-title`}>
                        <>
                            查询失败
                            {!!parseTimeCost && isDeveloper && (
                                <span className={`${prefixCls}-title-tip`}>(耗时: {parseTimeCost}ms)</span>
                            )}
                        </>
                    </div>
                </div>
                <div
                    className={classNames(
                        `${prefixCls}-content-container`,
                        `${prefixCls}-content-container-failed`
                    )}
                >
                    {parseTip}
                </div>
            </>
        )
    }

    if (isSimpleMode || parseInfoOptions.length === 0) {
        return null;
    }
    return (
        <div className={styles.thinkTimeBox}>
            <div>
                <Avatar shape="square" src={<img src={MsgAgentIMg} alt="avatar"/>}/>
            </div>
            <div className={styles.ytjxhs}>
                思考结束
                {!!parseTimeCost && isDeveloper && (
                    <span className={styles.time}>(耗时: {parseTimeCost}ms)</span>
                )}
            </div>
        </div>
    );
};

export default ThinkTime;
