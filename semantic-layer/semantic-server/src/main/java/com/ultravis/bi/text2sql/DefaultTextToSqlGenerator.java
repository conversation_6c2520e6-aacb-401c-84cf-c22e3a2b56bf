package com.ultravis.bi.text2sql;

import com.ultravis.bi.infra.PromptManager;
import com.ultravis.bi.semantic.SemanticMetadata;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Slf4j
@Component
public class DefaultTextToSqlGenerator implements TextToSqlGenerator {

    private final PromptManager promptManager;
    private final Text2SqlAssetsProvider semanticAssetsService;

    public DefaultTextToSqlGenerator(PromptManager promptManager, Text2SqlAssetsProvider semanticAssetsService) {
        this.promptManager = promptManager;
        this.semanticAssetsService = semanticAssetsService;
    }

    @Override
    public ParseResult genSql(
            String question,
            SemanticMetadata md,
            List<String> instructions,
            Function<String, ParseResult> sqlVerifier) {
        String sql = new SqlTemplateWorker(semanticAssetsService).doWork(question);
        if (sql != null) {
            return sqlVerifier.apply(sql);
        }

        sql = new LLMWorker(promptManager, semanticAssetsService, sqlVerifier)
                .doWork(question, md, instructions);
        if (sql != null) {
            return sqlVerifier.apply(sql);
        }

        throw new RuntimeException("TODO");
    }
}
