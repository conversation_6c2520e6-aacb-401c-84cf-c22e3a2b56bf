package com.ultravis.bi.semantic;

import com.ultravis.bi.infra.ViewRepo;
import com.ultravis.bi.semantic.rewrite.rewriter.*;
import com.ultravis.bi.utils.SqlTemplateCompiler;
import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.util.SqlShuttle;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toMap;

public class ViewManager {

    private static final Map<ParseSqlContext, ViewManager> MANAGERS = new ConcurrentHashMap<>();

    private final SemanticLangParser semanticLangParser;
    private final ParseSqlContext ctx;
    private final Collection<SemanticModel> semanticModels;
    private final Collection<SemanticRelationship> semanticRelationships;
    private final Map<String, ParsedView> PARSED_VIEWS = new ConcurrentHashMap<>();

    public ViewManager(SemanticLangParser semanticLangParser, ParseSqlContext ctx) {
        this.semanticLangParser = semanticLangParser;
        this.ctx = ctx;
        this.semanticModels = ctx.semanticMetadata.getModels().values();
        this.semanticRelationships = ctx.semanticMetadata.getRelationships().values();
    }

    private record Rel(String from, String to, String condition) {
    }

    public void parseRelatedViews() {
        Stream<Rel> relStream = semanticRelationships.stream()
                .flatMap(rel -> Stream.of(
                        new Rel(rel.fromModelName(), rel.toModelName(), rel.joinCondition()),
                        new Rel(rel.toModelName(), rel.fromModelName(), rel.joinCondition())
                ));
        relStream.forEach(rel -> {
            Optional<ParsedView> parsed = parseRelatedView(rel.from(), rel.to(), rel.condition());
            parsed.ifPresent(parsedView -> {
                PARSED_VIEWS.put(parsedView.name, parsedView);
            });
        });
    }

    public void parseViews() {
        ViewRepo.listViews().forEach(view -> {
            Optional<ParsedView> parse = parseView(view.name());
            parse.ifPresent(o -> {
                PARSED_VIEWS.put(o.name, o);
            });
        });
    }

    public static Optional<ParsedView> getView(String viewKey, ParseSqlContext ctx) {
        ViewManager manager = MANAGERS.computeIfAbsent(ctx, k -> {
            var m = new ViewManager(new SemanticLangParser(), ctx);
            m.parseRelatedViews();
            m.parseViews();
            return m;
        });
        return manager.getView(viewKey);
    }

    public static Optional<ParsedView> getView(String from, String to, ParseSqlContext ctx) {
        return getView(from + " -> " + to, ctx);
    }

    public Optional<ParsedView> getView(String viewKey) {
        return Optional.ofNullable(PARSED_VIEWS.get(viewKey));
    }

    public Optional<ParsedView> getView(String from, String to) {
        return getView(from + " -> " + to);
    }

    public record ParsedView(
            String name,
            String description,
            String baseModelName,
            String sqlTemplate,
            ParseResult parseResult,
            Map<String, String> paramTypeDesc) {

    }

    @NotNull
    private Optional<ParsedView> parseRelatedView(String driving, String driven, String joinCondition) {
        Optional<ViewRepo.ViewInfo> viewInfoOpt = ViewRepo.getViewInfo(driven);
        if (viewInfoOpt.isEmpty()) {
            return Optional.empty();
        }

        ViewRepo.ViewInfo viewInfo = viewInfoOpt.get();
        SemanticModel drivingModel = findSemanticModel(driving);

        ParsedView parsedView = parseView(viewInfo.name()).orElseThrow();
        String joinCondStr = rewriteJoinCondition(driven, joinCondition, parsedView.parseResult);

        var filter = makeFilterForPrimaryKeys(drivingModel);
        String sqlTemplate = """
                SELECT %s.*
                FROM %s
                INNER JOIN (%s) AS %s ON %s
                WHERE %s
                """
                .formatted(
                        driven,
                        driving,
                        StringUtils.stripEnd(viewInfo.sql(), " ;\n"),
                        driven,
                        joinCondStr,
                        filter.condition()
                );

        String compiledSql = SqlTemplateCompiler.compile(sqlTemplate, filter.paramExampleValues(), filter.paramTypeDesc());
        ParseResult parseResult = semanticLangParser.parseSql(compiledSql, ctx);

        return Optional.of(new ParsedView(
                "%s -> %s".formatted(driving, driven),
                viewInfo.description(),
                driven,
                sqlTemplate,
                parseResult,
                filter.paramTypeDesc()
        ));
    }

    private Optional<ParsedView> parseView(String viewName) {
        Optional<ViewRepo.ViewInfo> viewInfoOpt = ViewRepo.getViewInfo(viewName);
        if (viewInfoOpt.isEmpty()) {
            return Optional.empty();
        }

        ViewRepo.ViewInfo viewInfo = viewInfoOpt.get();
        SemanticModel model = findSemanticModel(viewName);

        ParseResult parseResult1 = semanticLangParser.parseSql(viewInfo.sql(), ctx);
        var filter = makeFilterForPrimaryKeys(model, parseResult1);

        String sqlTemplate = """
                SELECT %s.*
                FROM (%s) AS %s
                WHERE %s
                """
                .formatted(
                        viewName,
                        StringUtils.stripEnd(viewInfo.sql(), " ;\n"),
                        viewName,
                        filter.condition()
                );

        String compiledSql = SqlTemplateCompiler.compile(sqlTemplate, filter.paramExampleValues(), filter.paramTypeDesc());
        ParseResult parseResult = semanticLangParser.parseSql(compiledSql, ctx);

        return Optional.of(new ParsedView(
                viewName,
                viewInfo.description(),
                viewName,
                sqlTemplate,
                parseResult,
                filter.paramTypeDesc()
        ));
    }

    private static Object getExampleValueForType(String typeName) {
        return switch (typeName) {
            case "string" -> "example string";
            case "number", "integer", "double", "float" -> 1;
            case "boolean" -> true;
            case "date" -> "2024-12-25";
            default -> "example";
        };
    }

    record FilterInfo(String condition, Map<String, String> paramTypeDesc, Map<String, Object> paramExampleValues) {
    }

    @NotNull
    private static FilterInfo makeFilterForPrimaryKeys(SemanticModel fromModel) {
        List<SemanticColumn> primaryKeys = getPrimaryKeys(fromModel).toList();

        String condition = primaryKeys.stream()
                .map(ViewManager::makeParameterizedCondition)
                .collect(Collectors.joining(" AND "));

        Map<String, String> typeDesc = primaryKeys.stream()
                .collect(toMap(SemanticColumn::getName, col -> col.getType().getSimpleName().toLowerCase()));

        Map<String, Object> exampleValues = getExampleValues(typeDesc);

        return new FilterInfo(condition, typeDesc, exampleValues);
    }

    @NotNull
    private static FilterInfo makeFilterForPrimaryKeys(SemanticModel fromModel, ParseResult parseResult) {
        List<ColumnInfo> primaryKeys = parseResult.primaryKeysGroup().get(fromModel.getName());

        String condition = primaryKeys.stream()
                .map(ViewManager::makeParameterizedCondition)
                .collect(Collectors.joining(" AND "));

        Map<String, String> typeDesc = primaryKeys.stream()
                .collect(toMap(ColumnInfo::getOriginFieldName, ColumnInfo::getDataType));

        Map<String, Object> exampleValues = getExampleValues(typeDesc);

        return new FilterInfo(condition, typeDesc, exampleValues);
    }

    private static Map<String, Object> getExampleValues(Map<String, String> paramTypes) {
        return paramTypes.entrySet().stream()
                .collect(toMap(Map.Entry::getKey, ent -> getExampleValueForType(ent.getValue())));
    }

    @NotNull
    private static Stream<SemanticColumn> getPrimaryKeys(SemanticModel fromModel) {
        return fromModel.getColumns().stream()
                .filter(SemanticColumn::isPrimaryKey);
    }

    private SemanticModel findSemanticModel(String fromModelName) {
        return semanticModels.stream()
                .filter(m -> m.name.equals(fromModelName))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("模型未找到: " + fromModelName));
    }

    private static String makeParameterizedCondition(SemanticColumn col) {
        return col.qualifiedColumnName() + " = " + ":" + col.getName();
    }

    private static String makeParameterizedCondition(ColumnInfo col) {
        return col.getName() + " = " + ":" + col.getOriginFieldName();
    }

    private String rewriteJoinCondition(String baseModel, String joinCondition, ParseResult parseResult) {
        SqlParser parser = SqlParser.create(joinCondition);
        SqlNode sqlNode = null;
        try {
            sqlNode = parser.parseExpression();
        } catch (SqlParseException e) {
            throw new RuntimeException(e);
        }
        SqlNode joinConditionNode = sqlNode.accept(new SqlShuttle() {
            @Override
            public SqlNode visit(SqlIdentifier id) {
                for (ColumnInfo columnInfo : parseResult.getColumns()) {
                    if (id.toString().equalsIgnoreCase(columnInfo.getOriginField())) {
                        return new SqlIdentifier(baseModel + "." + columnInfo.getName(), id.getParserPosition());
                    }
                }
                return super.visit(id);
            }
        });
        assert joinConditionNode != null;
        return joinConditionNode.toString().replaceAll("`", "");
    }
}
