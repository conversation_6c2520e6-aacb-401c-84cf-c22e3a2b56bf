package com.ultravis.bi.semantic;

import com.ultravis.bi.semantic.rewrite.rewriter.SemanticColumn;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * 语义模型元数据. 把语义层常用的数据合并到一起, 便于使用.
 */
@Getter
public class SemanticMetadata {

    private final Map<String, SemanticModel> models;
    private final Map<String, SemanticRelationship> relationships;
    private final Map<String, List<SensitiveField>> sensitiveFields;

    public SemanticMetadata(Map<String, SemanticModel> models, Map<String, SemanticRelationship> relationships) {
        this.models = models;
        this.relationships = relationships;

        this.sensitiveFields = createSensitiveFields(models);
    }

    public Optional<SemanticColumn> getColumn(String modelName, String dimName) {
        return Optional.ofNullable(models.get(modelName))
                .orElseThrow(() -> new RuntimeException("未找到模型: " + modelName))
                .getColumns().stream()
                .filter(col -> col.getName().equalsIgnoreCase(dimName))
                .findFirst();
    }

    public BiFunction<String, String, Optional<SensitiveField>> createSensitiveFieldMatcher() {
        return (modelName, fieldName) -> sensitiveFields.getOrDefault(modelName, List.of()).stream()
                .filter(f -> f.matches(modelName, fieldName))
                .findFirst();
    }

    public Optional<SensitiveField> getSensitiveField(String modelName, String fieldName) {
        return sensitiveFields.getOrDefault(modelName, List.of()).stream()
                .filter(f -> f.matches(modelName, fieldName))
                .findFirst();
    }

    private Map<String, List<SensitiveField>> createSensitiveFields(Map<String, SemanticModel> models) {
        return models.entrySet().stream()
                .flatMap(ent -> {
                    SemanticModel model = ent.getValue();
                    return model.getColumns().stream()
                            .map(col -> {
                                Object val = col.getExt().get("sensitiveLevel");
                                if (val instanceof Integer level && level > 0) {
                                    return new SensitiveField()
                                            .setModelName(model.getName())
                                            .setFieldName(col.getName())
                                            .setSensitiveLevel((int) val)
                                            .setProtectionPolicy(SensitiveField.ProtectionPolicy.MASK);
                                }
                                return null;
                            });
                })
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(SensitiveField::getModelName, Collectors.toList()));
    }
}
