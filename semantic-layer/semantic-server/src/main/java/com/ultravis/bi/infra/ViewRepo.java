package com.ultravis.bi.infra;

import java.util.List;
import java.util.Optional;

public class ViewRepo {

    public record ViewInfo(String name, String description, String sql) {

        public static ViewInfo of(String name, String description, String sql) {
            return new ViewInfo(name, description, sql);
        }
    }

    private static final List<ViewInfo> VIEW_INFO_LIST = List.of(
            new ViewInfo(
                    "DWD_XSJBSJZLB",
                    "基本信息",
                    "SELECT XH AS 学号, CSRQ AS 出生日期, MZM AS 民族码,BJMC AS 班级名称,XBM AS 性别码,ZZMMM AS 政治面貌,XM AS 姓名,XJZT AS 学籍状态,YXMC AS 学院名称,ZYMC  AS 专业名称,RXNJ AS 入学年级,SFZJ AS 是否在籍 FROM  DWD_XSJBSJZLB"
            ),
            new ViewInfo(
                    "kustXSKSCJXX",
                    "成绩信息",
                    "SELECT  XH AS 学号, XNXQ AS 考试学期学年,KCMC 考试课程名称, ZCJ AS 总成绩, JD AS 成绩绩点, KCXF AS 学分 FROM kustXSKSCJXX"
            ),
            new ViewInfo(
                    "DWD_XSJLZLB",
                    "获奖评优",
                    "SELECT  XH AS 学号, JLMC AS 奖励名称, HJSJ AS 获奖时间, JLJBM AS 奖励级别码, JLDJM AS 奖励等级码 FROM DWD_XSJLZLB"
            ),
            ViewInfo.of(
                    "DWD_ZHCPCJXXZLB",
                    "综合测评",
                    "SELECT XH AS 学号, XQ AS 获得成绩学期, CJ AS 综合测评成绩, BJPM AS 班级排名, ZYPM AS 专业排名  FROM DWD_ZHCPCJXXZLB"
            ),
            ViewInfo.of(
                    "DWD_ZHCPZBCJZLB",
                    "综测成绩",
                    "SELECT XH AS 学号, CPLXMC AS 测评类型名称, XNXQ 测评指标学年学期,  PCFS AS 测评分数 FROM DWD_ZHCPZBCJZLB"
            ),
            ViewInfo.of(
                    "kustBKSJSXX",
                    "图书借阅",
                    "SELECT XH AS 借书人, TSMC AS 图书名称, JYSJ AS 借书时间,  SJGHSJ AS 归还时间 FROM kustBKSJSXX"
            ),
            ViewInfo.of(
                    "DWD_XJYDSJL",
                    "学籍异动",
                    "SELECT XH AS 学号, YDYY AS 异动原因, YDLXMC AS 异动类型名称, YDSJ AS 异动时间 FROM DWD_XJYDSJL"
            ),
            ViewInfo.of(
                    "kustBKSXFXX",
                    "一卡通消费",
                    "SELECT XH AS 学号, JYJE AS 交易金额, RZRQSJ AS 消费时间, DZ AS 消费地址, SHMC AS 消费商户名称  FROM kustBKSXFXX"
            ),
            ViewInfo.of(
                    "DWD_SQ_MJXXSJZLB",
                    "社区门禁",
                    "SELECT XH AS 学号, JCFX AS 社区进出方向, JCSJ AS 进出时间  FROM DWD_SQ_MJXXSJZLB WHERE EXTRACT(DAY FROM JCSJ) < 7;"
            ),
            ViewInfo.of(
                    "DWD_QJXXZLB",
                    "请假信息",
                    "SELECT XH AS 学号, QJNR 请假内容, JJLXRDH AS 紧急联系人电话, QJSJ AS 请假申请时间,KSSJ 开始时间 , JSSJ 结束时间, QJZT AS 请假状态  FROM DWD_QJXXZLB"
            ),
            ViewInfo.of(
                    "kustXSQDXX",
                    "签到信息",
                    "SELECT XH AS 学号, QDSJ AS 签到时间,QDJG AS 签到结果,BT AS 签到标题, NR AS 签到内容, XQMC AS 签到校区名称   FROM kustXSQDXX"
            ),
            ViewInfo.of(
                    "DWD_TSGMJTGXXSJZLB",
                    "图书馆进出信息",
                    "SELECT XH AS 学号, FX AS 方向,TSG AS 图书馆, TGSJ AS 通过时间  FROM DWD_TSGMJTGXXSJZLB"
            ),
            ViewInfo.of(
                    "DWD_XSZSXXSJZLB",
                    "宿舍信息",
                    "SELECT XH AS 学号, XQMC AS 宿舍所属校区名称,LD AS 宿舍所属楼栋,LC AS 宿舍所在楼层,FJH AS 宿舍房间号,CWH AS 床位号  FROM DWD_XSZSXXSJZLB"
            )
    );

    public static Optional<ViewInfo> getViewInfo(String viewName) {
        return VIEW_INFO_LIST.stream()
                .filter(v -> v.name().equals(viewName))
                .findFirst();
    }

    public static List<ViewInfo> listViews() {
        return VIEW_INFO_LIST;
    }
}
