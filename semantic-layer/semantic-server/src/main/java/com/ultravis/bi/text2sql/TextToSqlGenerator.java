package com.ultravis.bi.text2sql;

import com.ultravis.bi.semantic.SemanticMetadata;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;

import java.util.List;
import java.util.function.Function;

public interface TextToSqlGenerator {
    ParseResult genSql(
            String question,
            SemanticMetadata md,
            List<String> instructions,
            Function<String, ParseResult> sqlVerifier);
}
