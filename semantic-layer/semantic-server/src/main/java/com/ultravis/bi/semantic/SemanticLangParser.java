package com.ultravis.bi.semantic;

import com.ultravis.bi.semantic.rewrite.adapters.oracle.UltravisOracleSqlDialect;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import com.ultravis.bi.semantic.rewrite.rewriter.SchemaSymbolEvaluator;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticSqlParser;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticSqlParserConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.dialect.OracleSqlDialect;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.validate.SqlConformanceEnum;
import org.apache.calcite.sql.validate.SqlValidatorException;
import org.springframework.stereotype.Component;

/**
 * 语义层语言解析器. 语义层语言包括: SQL, JSON, GraphQL, DSL 等.
 */
@Slf4j
@Component
public class SemanticLangParser {

    public ParseResult parseSql(String sql, ParseSqlContext parseSqlContext) {
        ParseResult parseResult;
        try {
            SchemaSymbolEvaluator schemaSymbolEvaluator =
                    new SchemaSymbolEvaluator(
                            parseSqlContext.semanticMetadata.getModels().values(),
                            parseSqlContext.semanticMetadata.getRelationships().values());
            SemanticSqlParser parser =
                    new SemanticSqlParser(
                            schemaSymbolEvaluator,
                            parseSqlContext.dimensionValueConvertor,
                            new SemanticSqlParserConfig()
//                                    .withSqlDialect(OracleSqlDialect.DEFAULT)
                                    .withSqlDialect(new UltravisOracleSqlDialect(OracleSqlDialect.DEFAULT_CONTEXT))
                                    .withSqlConformance(SqlConformanceEnum.LENIENT)
                                    .withCompactColumnAlias(true)
                                    .withStrictJoin(true));
            parseResult = parser.parse(sql,
                    SemanticSqlParser.withPagination(0, 100, false));
        } catch (Exception e) {
            log.error("解析 SQL 失败. inputSql: {}", sql, e);
            Throwable cause = e;
            while (cause != null) {
                switch (cause) {
                    case SqlParseException sqlParseException:
                        throw new RuntimeException(sqlParseException.getMessage());
                    case SqlValidatorException sqlValidatorException:
                        throw new RuntimeException(sqlValidatorException.getMessage());
                    default:
                        break;
                }
                cause = cause.getCause();
            }
            throw e;
        }
        return parseResult;
    }

}
