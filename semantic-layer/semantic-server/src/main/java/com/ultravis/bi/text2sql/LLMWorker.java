package com.ultravis.bi.text2sql;

import com.ultravis.bi.infra.LLMs;
import com.ultravis.bi.infra.PromptManager;
import com.ultravis.bi.semantic.SemanticMetadata;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.output.structured.Description;
import dev.langchain4j.service.AiServices;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Function;

@Slf4j
public class LLMWorker {

    public static final int MAX_TRIES = 5;
    private static final double THRESHOLD_EXAMPLE_SQL = 0.8;

    private final PromptManager promptManager;
    private final Text2SqlAssetsProvider semanticAssetsService;
    private final Function<String, ParseResult> sqlParser;

    public LLMWorker(PromptManager promptManager, Text2SqlAssetsProvider semanticAssetsService, Function<String, ParseResult> sqlParser) {
        this.promptManager = promptManager;
        this.semanticAssetsService = semanticAssetsService;
        this.sqlParser = sqlParser;
    }

    public String doWork(
            String question,
            SemanticMetadata md,
            List<String> instructions) {
        List<ExampleQuery> exampleQueries = semanticAssetsService.getExampleQueries(question, THRESHOLD_EXAMPLE_SQL);
        Prompt prompt = promptManager.textToSql(question, md,
                instructions,
                exampleQueries);
        log.info("message: {}", prompt.text());

        ChatModel llm = LLMs.createLlmWithDefault(0.0);
        ChatMemory chatMemory = MessageWindowChatMemory.withMaxMessages(20);

        SqlAssistant assistant = AiServices.builder(SqlAssistant.class)
                .chatModel(llm)
                .chatMemory(chatMemory)
                .build();

        String text = prompt.text();
        SqlAssistantMessage sqlAssistantMessage = null;

        String sql = null;
        for (int i = 0; i < MAX_TRIES; i++) {
            sqlAssistantMessage = assistant.chat(text);
            sql = sqlAssistantMessage.sql;
            // TODO 无法生成 SQL 时, 怎么办
            if (sql.isEmpty()) {
                throw new RuntimeException("Sorry, I can not answer the question: " + sqlAssistantMessage.explainToNonTechnicalAudience);
            }
            try {
                sqlParser.apply(sql);
                return sql;
            } catch (Exception e) {
                text = "Your SQL parsing failed: " + e.getMessage() + ".\nPlease correct the SQL.";
            }
        }
        throw new RuntimeException("Failed to parse SQL: " + sql);
    }

    @Data
    public static class SqlAssistantMessage {
        @Description("Summary of the thought process.")
        private String thinkingSummary;
        //        @Description("Explanation of your intention for non-technical audience. **Use the same language as the original QUESTION.**")
        @Description("Explanation of your intention(in first person) for non-technical audience. Use `**` and `**` to highlight the key elements. **Use Chinese**.")
        private String explainToNonTechnicalAudience;
        @Description("The generated SQL")
        private String sql;
        @Description("If you do not have enough information to make a determination, say 'I don't know' and ask for clarification. **Use Chinese**.")
        private String clarification;
    }

    interface SqlAssistant {
        SqlAssistantMessage chat(String text);
    }

}
