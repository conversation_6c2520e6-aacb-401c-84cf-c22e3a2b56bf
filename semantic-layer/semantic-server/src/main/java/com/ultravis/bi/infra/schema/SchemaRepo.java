package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.common.pojo.ModelRela;
import com.tencent.supersonic.headless.api.pojo.MetaFilter;
import com.tencent.supersonic.headless.api.pojo.SchemaItem;
import com.tencent.supersonic.headless.api.pojo.request.SchemaFilterReq;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.tencent.supersonic.headless.api.pojo.response.SemanticSchemaResp;
import com.tencent.supersonic.headless.server.manager.SemanticSchemaManager;
import com.tencent.supersonic.headless.server.service.ModelRelaService;
import com.tencent.supersonic.headless.server.service.ModelService;
import com.tencent.supersonic.headless.server.service.SchemaService;
import com.ultravis.bi.semantic.SemanticMetadata;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.function.Function;

import static java.util.stream.Collectors.toMap;

@Repository
public class SchemaRepo {

    private final ModelService modelService;
    private final ModelRelaService modelRelaService;
    private final SchemaService schemaService;
    private final SemanticSchemaManager semanticSchemaManager;

    public SchemaRepo(ModelService modelService, ModelRelaService modelRelaService, SchemaService schemaService, SemanticSchemaManager semanticSchemaManager) {
        this.modelService = modelService;
        this.modelRelaService = modelRelaService;
        this.schemaService = schemaService;
        this.semanticSchemaManager = semanticSchemaManager;
    }

    public SemanticMetadata getSemanticMetadata(Set<Long> dataSetIds) {
        SupersonicSchemaAdapter supersonicSchemaAdapter = new SupersonicSchemaAdapter(getSupersonicSemanticSchema(dataSetIds.iterator().next()), new OracleColumnTypeToJavaTypeMapper());

        List<SemanticModel> semanticModels = supersonicSchemaAdapter.getSemanticModels();
        List<SemanticRelationship> semanticRelationships = supersonicSchemaAdapter.getSemanticRelationships();

        return new SemanticMetadata(
                semanticModels.stream().collect(toMap(SemanticModel::getName, Function.identity())),
                semanticRelationships.stream().collect(toMap(it -> it.fromModelName() + " -> " + it.toModelName(), Function.identity()))
        );
    }

    public Pair<List<ModelResp>, List<ModelRela>> getSupersonicModelsAndRelationships(long dataSetId) {
        List<ModelResp> supersonicModels = getSupersonicModels(dataSetId);
        List<Long> modelIds = supersonicModels.stream().map(SchemaItem::getId).toList();
        List<ModelRela> relationships = modelRelaService.getModelRela(modelIds);
        return Pair.of(supersonicModels, relationships);
    }

    public SemanticSchemaResp getSupersonicSemanticSchema(Long datasetId) {
        SchemaFilterReq schemaFilterReq = new SchemaFilterReq();
        schemaFilterReq.setDataSetId(datasetId);
        return schemaService.fetchSemanticSchema(schemaFilterReq);
    }

    private List<ModelResp> getSupersonicModels(long dataSetId) {
        MetaFilter metaFilter = new MetaFilter();
        metaFilter.setDataSetId(dataSetId);
        return modelService.getModelList(metaFilter);
    }

}
