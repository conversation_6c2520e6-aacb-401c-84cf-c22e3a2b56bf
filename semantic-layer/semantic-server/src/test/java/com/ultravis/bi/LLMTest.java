package com.ultravis.bi;

import dev.langchain4j.http.client.jdk.JdkHttpClientBuilder;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import dev.langchain4j.model.openai.internal.OpenAiClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.net.http.HttpClient;

import static dev.langchain4j.model.openai.internal.OpenAiUtils.DEFAULT_USER_AGENT;

@Slf4j
public class LLMTest {

    @Test
    void test_() {
        StreamingChatModel model = OpenAiStreamingChatModel.builder()
                .apiKey("sk-exupisfjaksbmwyqkglxrwdmbfkhykssrjimsduhriyfluuj")
                .baseUrl("https://api.siliconflow.cn/v1")
                .modelName("Qwen/Qwen2.5-32B-Instruct")
//                .apiKey("123")
//                .baseUrl("http://172.16.1.145:6006/v1")
//                .modelName("Qwen2.5-32B-Instruct")
                .logRequests(true)
                .logResponses(true)
//                .maxRetries(2)
                .temperature(0.6)
                .maxTokens(2000)
                .httpClientBuilder(OpenAiClient.builder().apiKey(apiKey).baseUrl(baseUrl)
                        .organizationId(organizationId).connectTimeout(timeout)
                        .readTimeout(timeout).logRequests(logRequests)
                        .logResponses(logResponses).userAgent(DEFAULT_USER_AGENT)
                        .customHeaders(customHeaders)
                        // TODO  默认的 HTTP 2.0 导致 vllm 报错. See: https://github.com/spring-projects/spring-ai/issues/2042
                        .httpClientBuilder(new JdkHttpClientBuilder().httpClientBuilder(HttpClient.newBuilder().version(HttpClient.Version.HTTP_1_1)))
                        .build();)
                .build();

        model.chat("hello", new StreamingChatResponseHandler() {
            @Override
            public void onPartialResponse(String partialResponse) {
                log.info(partialResponse);
            }

            @Override
            public void onCompleteResponse(ChatResponse completeResponse) {
                log.info(completeResponse.toString());
            }

            @Override
            public void onError(Throwable error) {
                log.info(error.getMessage());
            }
        });

        try {
            Thread.sleep(5 * 1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
