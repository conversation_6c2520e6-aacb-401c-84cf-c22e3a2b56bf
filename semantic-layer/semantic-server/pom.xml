<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tencent.supersonic</groupId>
        <artifactId>supersonic</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <groupId>com.ultravis.bi</groupId>
    <artifactId>semantic-server</artifactId>
    <name>BI - Semantic Server</name>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tencent.supersonic</groupId>
            <artifactId>headless-server</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.tencent.supersonic</groupId>
            <artifactId>semantic-layer</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.langfuse</groupId>
            <artifactId>langfuse-java</artifactId>
            <version>0.0.6</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j</artifactId>
        </dependency>
    </dependencies>
</project>
