package com.ultravis.bi.semantic.rewrite.rewriter;

import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.validate.*;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class SchemaSymbolEvaluator {

    private final Collection<SemanticModel> schemas;
    private final Collection<SemanticRelationship> relationships;

    public SchemaSymbolEvaluator(Collection<SemanticModel> schemas) {
        this(schemas, List.of());
    }

    public SchemaSymbolEvaluator(Collection<SemanticModel> schemas, Collection<SemanticRelationship> relationships) {
        this.schemas = schemas;
        this.relationships = relationships;
    }

    Optional<SemanticColumn> getSemanticColumn(SqlValidatorScope scope, SqlNode sqlNode) {
        if (!(sqlNode instanceof SqlIdentifier)) {
            return Optional.empty();
        }
        SqlQualified sqlQualified = scope.fullyQualify((SqlIdentifier) sqlNode);
        SqlValidatorNamespace namespace = sqlQualified.namespace;
        if (namespace instanceof IdentifierNamespace) {
            IdentifierNamespace identifierNamespace = (IdentifierNamespace) namespace;
            return getSemanticColumn(identifierNamespace, sqlQualified);
        }
        return Optional.empty();
    }

    Optional<SemanticColumn> getSemanticColumn(IdentifierNamespace identifierNamespace,
                                               SqlQualified sqlQualified) {
        String tableName = identifierNamespace.getId().getSimple().toLowerCase();
        String columnName = sqlQualified.suffix().stream().map(String::toLowerCase)
                .collect(Collectors.joining("."));
        return getSemanticColumn(tableName, columnName);
    }

    Optional<SemanticColumn> getSemanticColumn(String tableName, SqlQualified sqlQualified) {
        String columnName = sqlQualified.suffix().stream().map(String::toLowerCase)
                .collect(Collectors.joining("."));
        return getSemanticColumn(tableName, columnName);
    }

    Optional<SemanticColumn> getSemanticColumn(String tableName, String columnName) {
        return schemas.stream().filter(model -> model.name.equalsIgnoreCase(tableName)).findFirst()
                .flatMap(model -> model.columns.stream()
                        .filter(col -> col.getName().equalsIgnoreCase(columnName)).findFirst());
    }

    Optional<SemanticColumn> getSemanticColumn(SqlValidatorTable table, SqlQualified qualifiedId) {
        String tableName =
                table.getQualifiedName().get(table.getQualifiedName().size() - 1).toLowerCase();
        String columnName = qualifiedId.suffix().stream().map(String::toLowerCase)
                .collect(Collectors.joining("."));
        return getSemanticColumn(tableName, columnName);
    }

    Optional<SemanticModel> getSemanticModel(String tableName) {
        return schemas.stream().filter(model -> model.name.equalsIgnoreCase(tableName)).findFirst();
    }

    public Optional<SemanticModel> getSemanticModel(@Nullable SqlValidatorTable table) {
        if (table == null) {
            return Optional.empty();
        }
        return getSemanticModel(table.getQualifiedName().stream().map(String::toLowerCase)
                .collect(Collectors.joining(".")));
    }

    public Collection<SemanticModel> getSemanticModels() {
        return schemas;
    }

    public Collection<SemanticRelationship> getRelationships() {
        return relationships;
    }
}
