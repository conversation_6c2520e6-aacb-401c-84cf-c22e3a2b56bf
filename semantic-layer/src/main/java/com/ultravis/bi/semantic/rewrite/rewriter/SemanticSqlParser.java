package com.ultravis.bi.semantic.rewrite.rewriter;

import lombok.Getter;
import org.apache.calcite.config.CalciteConnectionConfig;
import org.apache.calcite.config.CalciteConnectionConfigImpl;
import org.apache.calcite.config.CalciteConnectionProperty;
import org.apache.calcite.jdbc.CalciteSchema;
import org.apache.calcite.jdbc.JavaTypeFactoryImpl;
import org.apache.calcite.plan.RelOptTable;
import org.apache.calcite.prepare.CalciteCatalogReader;
import org.apache.calcite.rel.type.RelDataType;
import org.apache.calcite.rel.type.RelDataTypeFactory;
import org.apache.calcite.schema.Function;
import org.apache.calcite.schema.SchemaPlus;
import org.apache.calcite.schema.impl.AbstractTable;
import org.apache.calcite.schema.impl.ScalarFunctionImpl;
import org.apache.calcite.sql.*;
import org.apache.calcite.sql.fun.SqlLibrary;
import org.apache.calcite.sql.fun.SqlLibraryOperatorTableFactory;
import org.apache.calcite.sql.fun.SqlStdOperatorTable;
import org.apache.calcite.sql.parser.SqlParserPos;
import org.apache.calcite.sql.util.SqlShuttle;
import org.apache.calcite.sql.validate.SqlConformanceEnum;
import org.apache.calcite.sql.validate.SqlValidator;
import org.apache.calcite.sql.validate.SqlValidatorUtil;
import org.apache.calcite.util.Pair;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;

import java.util.*;
import java.util.stream.Stream;

import static java.util.Objects.requireNonNull;

public class SemanticSqlParser {

    private static final RelOptTable.ViewExpander NOOP_EXPANDER =
            (type, query, schema, path) -> null;

    private static final SqlValidator.Config DEFAULT_VALIDATORCONFIG = SqlValidator.Config.DEFAULT
            .withColumnReferenceExpansion(true)
            .withConformance(SqlConformanceEnum.PRESTO)
            .withTypeCoercionEnabled(true);

    public final SqlValidator validator;
    private final StatementAnalyzer statementAnalyzer;
    protected final Analysis analysis;
    protected final UltravisSqlParser sqlParser;
    private final SemanticSqlParserConfig config;
    /**
     * TODO 测试用
     */
    protected final SchemaSymbolEvaluator schemaSymbolEvaluator;
    protected DimensionValueConvertor dimensionValueConvertor;

    ///  configuration
    private final SqlDialect sqlDialect;
    private final boolean compactColumnAlias;
    private final boolean strictJoin;
    private boolean columnExprIsFromSemanticLayer = true;

    record Pagination(int offset, int limit, boolean force) {
    }

    public SemanticSqlParser(SchemaSymbolEvaluator schemaSymbolEvaluator, DimensionValueConvertor dimensionValueConvertor, SemanticSqlParserConfig config) {
        this.compactColumnAlias = config.compactColumnAlias();
        this.strictJoin = config.strictJoin();
        this.sqlDialect = config.sqlDialect();
        this.schemaSymbolEvaluator = schemaSymbolEvaluator;
        this.dimensionValueConvertor = dimensionValueConvertor;
        this.config = config;
        this.analysis = new Analysis();

        // Create the schema and table data types
        RelDataTypeFactory typeFactory = new JavaTypeFactoryImpl();
        CalciteSchema calciteSchema = CalciteSchema.createRootSchema(true);
        initCalciteSchema(this.schemaSymbolEvaluator.getSemanticModels(), typeFactory, calciteSchema);
        SchemaPlus plus = calciteSchema.plus();
        try {
            Function aggregate = ScalarFunctionImpl.create(
                    UltravisAggregateFunction.class.getMethod("aggregate", Number.class));
//            plus.add("TO_CHAR", requireNonNull(ScalarFunctionImpl.create(UltravisSqlOperatorTable.ToCharFunction.class, "toChar")));
//            plus.add("TO_DATE", requireNonNull(ScalarFunctionImpl.create(UltravisSqlOperatorTable.ToDateFunction.class, "toDate")));
            plus.add("AGGREGATE", aggregate);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }

        Properties props = new Properties();
        props.setProperty(CalciteConnectionProperty.CASE_SENSITIVE.camelName(), "false");
        CalciteConnectionConfig config1 = new CalciteConnectionConfigImpl(props);
        CalciteCatalogReader catalogReader =
                new CalciteCatalogReader(calciteSchema, Collections.singletonList("bs"),
                        typeFactory, config1);

        SqlOperatorTable oracleOperatorTable = SqlLibraryOperatorTableFactory.INSTANCE.getOperatorTable(
                EnumSet.of(SqlLibrary.ORACLE));
        SqlStdOperatorTable ultravisOperatorTable = UltravisSqlOperatorTable.instance();
        validator =
                SqlValidatorUtil.newValidator(ultravisOperatorTable, catalogReader,
                        typeFactory,
                        DEFAULT_VALIDATORCONFIG.withConformance(config.sqlConformance())
                );

        this.sqlParser = new UltravisSqlParser(config.sqlParserConfig());
        this.statementAnalyzer =
                new StatementAnalyzer(validator, this.schemaSymbolEvaluator, this.analysis, this.sqlParser);

    }

    protected void initCalciteSchema(Collection<SemanticModel> semanticModels,
                                     RelDataTypeFactory typeFactory1, CalciteSchema calciteSchema1) {
        for (SemanticModel table : semanticModels) {
            RelDataTypeFactory.Builder builder = new RelDataTypeFactory.Builder(typeFactory1);
            for (SemanticColumn column : table.columns) {
                try {
                    RelDataType type = typeFactory1.createJavaType(column.getType());
                    builder.add(column.getName(), type.getSqlTypeName()).nullable(true);
                } catch (Exception e) {
                    throw new RuntimeException("Can not resolve column type: " + column.getName() + ", " + column.getType(), e);
                }
            }
            calciteSchema1.add(table.name, new AbstractTable() {
                @Override
                public RelDataType getRowType(RelDataTypeFactory typeFactory) {
                    return builder.build();
                }
            });
        }
    }

    SqlNode parseQueryNoAnalyze(String sql) {
        sql = StringUtils.stripEnd(sql, " ;\n");
        SqlNode sqlNode = sqlParser.parseQuery(sql);
        SqlNode validated = validator.validate(sqlNode);
        return validated;
    }

    SqlNode parseQuery(String sql) {
        sql = StringUtils.stripEnd(sql, " ;\n");
        SqlNode sqlNode = sqlParser.parseQuery(sql);
        SqlNode validated = validator.validate(sqlNode);
        statementAnalyzer.analyze(validated);
        return validated;
    }

    @FunctionalInterface
    public interface ParseOption {
        void apply(ParseOptions parser);
    }

    @Getter
    public static class ParseOptions {
        private Pagination pagination;
        private boolean detailQuerylDecoration;
    }

    public static ParseOption withPagination(int offset, int limit, boolean force) {
        return parser -> {
            parser.pagination = new Pagination(offset, limit, force);
        };
    }

    public static ParseOption withDetailDecoration(boolean value) {
        return parser -> {
            parser.detailQuerylDecoration = value;
        };
    }

    public SqlNode parseExpression(String sql) {
        return sqlParser.parseExpression(sql);
    }

    public ParseResult parse(String sql, ParseOption... options) {
        ParseOptions parseOptions = new ParseOptions();
        for (ParseOption option : options) {
            option.apply(parseOptions);
        }

        SqlNode rewritten = transformQuery_(sql);

        Pagination pagination = parseOptions.pagination;
        if (pagination != null) {
            SqlSelect topLevelQuery = (SqlSelect) analysis.getTopLevelQuery();
            boolean hasOffset = topLevelQuery.getOffset() != null;
            boolean hasLimit = topLevelQuery.getFetch() != null;

            int offset = pagination.offset;
            int limit = pagination.limit;

            if (!hasOffset || pagination.force()) {
                topLevelQuery.setOffset(SqlLiteral.createExactNumeric(String.valueOf(offset), SqlParserPos.ZERO));
            }
            if (!hasLimit || pagination.force()) {
                topLevelQuery.setFetch(SqlLiteral.createExactNumeric(String.valueOf(limit), SqlParserPos.ZERO));
            }
        }

        if (parseOptions.detailQuerylDecoration) {
            rewritten = decorateWithDetailsQuery(rewritten);
        }

        return new ParseResult(sql, toSqlString(rewritten), analysis.topLevelSelectDimensions().getValue());
    }

    private SqlSelect decorateWithDetailsQuery(SqlNode target) {
        SemanticSqlParser parser2 = new SemanticSqlParser(
                schemaSymbolEvaluator, dimensionValueConvertor, config
        );
        String detailInfoSql = """
                SELECT "XH", "XM"
                FROM DWD_XSJBSJZLB
                """;
        // TODO 这里不要输出string
        ParseResult parsedDetail = parser2.parse(detailInfoSql);
        SqlNode basicInfo = sqlParser.parseQuery(parsedDetail.getOutputSql());

        SqlNode left = new SqlAsOperator().createCall(SqlParserPos.ZERO, target, new SqlIdentifier("_main", SqlParserPos.ZERO));
        List<SqlNode> innerSelectList = ((SqlSelect) analysis.getTopLevelQuery()).getSelectList().stream().map(it -> {
            // TODO 为 toplevel select list 保存一份元数据，减少重复代码
            if (it instanceof SqlIdentifier id) {
                return new SqlIdentifier(List.of("_main", id.names.getLast()), id.getParserPosition());
            }
            return it;
        }).toList();
        List<SqlNode> basicInfoSelectList = ((SqlSelect) basicInfo).getSelectList().stream().map(it -> {
            if (it instanceof SqlIdentifier id) {
                return new SqlIdentifier(List.of("_detail", id.names.getLast()), id.getParserPosition());
            }
            return it;
        }).toList();
        basicInfo = new SqlAsOperator().createCall(
                SqlParserPos.ZERO,
                basicInfo,
                new SqlIdentifier("_detail", SqlParserPos.ZERO));
        SqlNodeList selectList = SqlNodeList.of(
                SqlParserPos.ZERO,
                Stream.concat(innerSelectList.stream(), basicInfoSelectList.stream()).toList()
        );
        SqlJoin sqlJoin = new SqlJoin(
                SqlParserPos.ZERO,
                left,
                SqlLiteral.createBoolean(false, SqlParserPos.ZERO),
                JoinType.LEFT.symbol(SqlParserPos.ZERO),
                basicInfo,
                JoinConditionType.ON.symbol(SqlParserPos.ZERO),
                SqlStdOperatorTable.EQUALS.createCall(
                        SqlParserPos.ZERO,
                        new SqlIdentifier(List.of("_detail", "XH"), SqlParserPos.ZERO),
                        new SqlIdentifier(List.of("_main", "XH"), SqlParserPos.ZERO))
        );
        return new SqlSelect(
                SqlParserPos.ZERO,
                null,
                selectList,
                sqlJoin,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );
    }

    public String transformQuery(String sql) {
        SqlNode transformedSqlNode = transformQuery_(sql);
        return toSqlString(transformedSqlNode);
    }

    protected SqlNode transformQuery_(String sql) {
        SqlNode validNode = parseQuery(sql);
        if (strictJoin) {
            new JoinValidator(schemaSymbolEvaluator.getRelationships()).validate(validator, validNode, analysis);
        }
        SqlNode transformedSqlNode = requireNonNull(transformQuery(validNode));

        // TODO 如此处理最外层 select * 比较丑陋
        SqlSelect topLevelQuery = (SqlSelect) analysis.originalTopLevelQuery();
        SqlNodeList sqlNodes = validator.expandStar(
                topLevelQuery.getSelectList(),
                topLevelQuery,
                false
        );
        sqlNodes.forEach(it -> statementAnalyzer.analyzeSelectListItem(it, validator.getSelectScope(topLevelQuery), false,
                topLevelQuery, validator.getSelectScope(topLevelQuery), null));
        analysis.setTopLevelSelectList(sqlNodes);
        return transformedSqlNode;
    }

    protected SqlNode transformQuery(SqlNode query) {
        List<DimensionValueConvertor.DimValue> dimValues = analysis.allDimensionValues().stream()
                .map(it -> {
                    String value = it.literal().node().toValue();
                    SemanticColumn column = it.column();
                    String modelName = column.getSemanticModel().getName();
                    String dimName = column.getName();
                    return new DimensionValueConvertor.DimValue(modelName, dimName, value, it.isPatternMatching());
                }).toList();
        Map<DimensionValueConvertor.DimValue, Optional<Object>> newDimValues = dimensionValueConvertor.convert(dimValues);

        return query.accept(new SqlShuttle() {

            @Override
            public @Nullable SqlNode visit(SqlLiteral literal) {
                Optional<Object> newDimValueOpt = analysis.getDimensionValue(literal)
                        .flatMap(valNode -> {
                            DimensionValueConvertor.DimValue key = new DimensionValueConvertor.DimValue(
                                    valNode.column().getSemanticModel().getName(),
                                    valNode.column().getName(),
                                    literal.toValue(),
                                    valNode.isPatternMatching());
                            return newDimValues.getOrDefault(key, Optional.empty());
                        });
                if (newDimValueOpt.isEmpty()) {
                    return super.visit(literal);
                }
                String newDimValue = newDimValueOpt.get().toString();
                final SqlParserPos parserPosition = literal.getParserPosition();
                // TODO 类型应该于 Dimension 一致
                return switch (literal) {
                    case SqlCharStringLiteral ignored -> SqlLiteral.createCharString(newDimValue,
                            parserPosition);
                    case SqlNumericLiteral ignored -> SqlLiteral.createExactNumeric(newDimValue,
                            parserPosition);
                    default -> throw new IllegalStateException("Unexpected value: " + literal);
                };
            }

            @Override
            public @Nullable SqlNode visit(SqlIdentifier id) {
                if (analysis.isModel(id)) {
                    return rewriteModel(id);
                }
                return super.visit(id);
            }

            @Override
            public @Nullable SqlNode visit(SqlCall call) {
                Pair<SemanticColumn, SqlNode> measure = analysis.getMeasure(call);
                if (measure != null) {
                    return rewriteMeasure(measure, call);
                }

                Optional<Analysis.DimPredicateNode> dimPredicateNode = analysis.getDimensionPredicateNode(call);
                if (dimPredicateNode.isPresent()) {
                    return rewriteDimPredicate(call, dimPredicateNode.get());
                }

                if (call.getKind() == SqlKind.SELECT && call == analysis.originalTopLevelQuery()) {
                    SqlNode visited = super.visit(call);
                    analysis.setTopLevelQuery(visited);
                    return visited;
                }

                return super.visit(call);
            }

            private SqlCall rewriteDimPredicate(SqlCall call, Analysis.DimPredicateNode dimPredicateNode) {
                SqlCall newNode = (SqlCall) super.visit(call);

                if (dimPredicateNode.isPatternMatching()) {
                    return rewritePatternMatching(dimPredicateNode, newNode);
                }

                return newNode;
            }

            private SqlCall rewritePatternMatching(Analysis.DimPredicateNode dimPredicateNode, SqlCall newNode) {
                @SuppressWarnings("all")
                DimensionValueConvertor.DimValue key = new DimensionValueConvertor.DimValue(
                        dimPredicateNode.column().getSemanticModel().getName(),
                        dimPredicateNode.column().getName(),
                        dimPredicateNode.literal().node().toValue(),
                        dimPredicateNode.isPatternMatching());
                Optional<Object> newValuesOpt = newDimValues.getOrDefault(key, Optional.empty());

                if (newValuesOpt.isEmpty()) {
                    return newNode;
                }

                assert newNode != null;
                SqlNode dimRef = newNode.operand(0);
                SqlNodeList literalOperands = valueObjectToLiteralNodes(dimPredicateNode, newValuesOpt.get());

                SqlOperator operator = dimPredicateNode.predicateNode().node().getOperator() == SqlStdOperatorTable.NOT_LIKE ?
                        SqlStdOperatorTable.NOT_IN : SqlStdOperatorTable.IN;
                return operator.createCall(SqlParserPos.ZERO, dimRef, literalOperands);
            }

            private SqlNodeList valueObjectToLiteralNodes(Analysis.DimPredicateNode dimPredicateNode, Object valueObject) {
                @SuppressWarnings("unchecked")
                List<Object> newValues = (List<Object>) valueObject;
                List<SqlLiteral> literals = newValues.stream()
                        .map(dimVal -> toSqlLiteral(dimVal, dimPredicateNode.literal()))
                        .toList();
                return new SqlNodeList(literals, SqlParserPos.ZERO);
            }

            private static SqlLiteral toSqlLiteral(Object dimVal, NodeRef<SqlLiteral> literal) {
                final String dimValString = dimVal.toString();
                return switch (literal.node()) {
                    case SqlCharStringLiteral ignored -> SqlLiteral.createCharString(dimValString,
                            SqlParserPos.ZERO);
                    case SqlNumericLiteral ignored -> SqlLiteral.createExactNumeric(dimValString,
                            SqlParserPos.ZERO);
                    default -> throw new IllegalStateException("Unexpected value: " + literal.node());
                };
            }

            @Override
            public @Nullable SqlNode visit(SqlDataTypeSpec type) {
                return sqlDialect.getCastSpec(type.deriveType(validator));
            }
        });
    }

    private @Nullable SqlNode rewriteMeasure(Pair<SemanticColumn, SqlNode> measure, SqlCall call) {
        SemanticColumn semanticColumn = measure.left;
        if (!semanticColumn.isCalculated()) {
            return sqlParser.parseExpression(semanticColumn.getExpr());
        } else {
            return sqlParser.parseExpression(semanticColumn.getExpr()).accept(new SqlShuttle() {
                @Override
                public @Nullable SqlNode visit(SqlIdentifier id) {
                    SqlNode idNode = analysis.getIdNode(call, id.toString());
                    if (idNode != null) {
                        return idNode;
                    }
                    return super.visit(id);
                }
            });
        }
    }

    private @Nullable SqlNode rewriteModel(SqlIdentifier id) {
        return new ModelRenderer(analysis.getModel(id), analysis, compactColumnAlias, sqlParser).render();
    }

    protected void setDimensionValueConvertor(DimensionValueConvertor dimensionValueConvertor) {
        this.dimensionValueConvertor = dimensionValueConvertor;
    }

    private String toSqlString(SqlNode sqlNode) {
        return sqlNode.toSqlString(sqlDialect).getSql();
    }
}
