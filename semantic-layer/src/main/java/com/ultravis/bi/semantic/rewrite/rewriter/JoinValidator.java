package com.ultravis.bi.semantic.rewrite.rewriter;

import org.apache.calcite.sql.*;
import org.apache.calcite.sql.util.SqlBasicVisitor;
import org.apache.calcite.sql.validate.SqlValidator;
import org.apache.calcite.sql.validate.SqlValidatorScope;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

public class JoinValidator {

    private final Graph graph;

    public JoinValidator(Collection<SemanticRelationship> relationships) {
        graph = new Graph();
        relationships.forEach(relationship -> {
            String from = relationship.fromModelName();
            String to = relationship.toModelName();
            // TODO 检查 joinCondition?
            // String condition = relationship.joinCondition;
            graph.addEdge(from, to);
        });
    }

    public void validate(SqlValidator validator, SqlNode sqlNode, Analysis analysis) {
        JoinVisitor visitor = new JoinVisitor(validator);
        sqlNode.accept(visitor);
        List<Pair<SqlIdentifier, SqlIdentifier>> fieldPairs = visitor.getJoinFieldPairs2();

        for (Pair<SqlIdentifier, SqlIdentifier> fieldPair : fieldPairs) {
            SqlIdentifier left = fieldPair.getLeft();
            Optional<String> leftModelName = getOriginSemanticColumn(analysis, left).map(it -> it.getSemanticModel().getName());
            SqlIdentifier right = fieldPair.getRight();
            Optional<String> rightModelName = getOriginSemanticColumn(analysis, right).map(it -> it.getSemanticModel().getName());
            if (leftModelName.isEmpty() || rightModelName.isEmpty()) {
                continue;
            }
            String src = leftModelName.get();
            String dst = rightModelName.get();
            if (!graph.hasPath(src, dst)) {
                throw new RuntimeException("非法字段连接: " + left + " ↔ " + right + ", 因为 " + src + " 与 " + dst + " 未定义关联关系");
            }
        }
    }

    private static Optional<SemanticColumn> getOriginSemanticColumn(Analysis analysis, SqlIdentifier identifier) {
        ColumnRef field1 = analysis.getCollectedField(identifier);
        if (field1 != null) {
            return Optional.of(field1.column());
        }
        return analysis.getReferencedColumn(identifier);
    }
}

class Graph {
    private final Map<String, Set<String>> neighbors = new HashMap<>();

    public void addEdge(String nodeA, String nodeB) {
        neighbors.computeIfAbsent(nodeA, k -> new HashSet<>()).add(nodeB);
        neighbors.computeIfAbsent(nodeB, k -> new HashSet<>()).add(nodeA);
    }

    public boolean hasPath(String src, String dst) {
        return neighbors.containsKey(src) && neighbors.get(src).contains(dst);
    }
}

class JoinVisitor extends SqlBasicVisitor<Void> {
    private final Map<String, String> aliasToTable = new HashMap<>();
    private final List<Pair<String, String>> fieldPairs = new ArrayList<>();
    private final List<Pair<SqlIdentifier, SqlIdentifier>> fieldPairs2 = new ArrayList<>();
    private final SqlValidator validator;

    public JoinVisitor(SqlValidator validator) {
        this.validator = validator;
    }

    @Override
    public Void visit(SqlCall call) {
        if (call instanceof SqlJoin join) {
            return visit(join);
        }
        return super.visit(call);
    }

    protected Void visit(SqlJoin join) {
        processNode(join.getLeft());
        processNode(join.getRight());

        extractJoinCondition(validator.getJoinScope(join), join.getCondition());

        join.getLeft().accept(this);
        join.getRight().accept(this);
        return null;
    }

    private void processNode(SqlNode node) {
        if (node instanceof SqlIdentifier id && id.names.size() == 2) {
            aliasToTable.put(id.names.get(1), id.names.get(0));
        } else if (node instanceof SqlCall call && call.getOperator().getKind() == SqlKind.AS) {
            SqlNode table = call.getOperandList().get(0);
            if (table instanceof SqlIdentifier identifier) {
                SqlIdentifier alias = (SqlIdentifier) call.getOperandList().get(1);
                aliasToTable.put(alias.toString(), identifier.toString());
            }
        }
    }

    private void extractJoinCondition(SqlValidatorScope joinScope, SqlNode condition) {
        if (condition instanceof SqlBasicCall call && call.getKind() == SqlKind.EQUALS) {
            SqlNode left = call.operand(0);
            SqlNode right = call.operand(1);
            // TODO 处理函数调用和其他情况
            if (left instanceof SqlIdentifier l && right instanceof SqlIdentifier r) {
                fieldPairs.add(Pair.of(left.toString(), right.toString()));
                fieldPairs2.add(Pair.of(l, r));
            }
        }
    }

    public List<Pair<String, String>> getJoinFieldPairs() {
        return fieldPairs;
    }

    public List<Pair<SqlIdentifier, SqlIdentifier>> getJoinFieldPairs2() {
        return fieldPairs2;
    }

    public String resolveFullField(String field) {
        String[] parts = field.split("\\.");
        if (parts.length != 2) return field;
        String alias = parts[0];
        String col = parts[1];
        String table = aliasToTable.getOrDefault(alias, alias);
        return table + "." + col;
    }
}
