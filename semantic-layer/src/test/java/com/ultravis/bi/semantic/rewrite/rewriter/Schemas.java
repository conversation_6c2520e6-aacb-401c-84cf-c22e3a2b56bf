package com.ultravis.bi.semantic.rewrite.rewriter;

import java.util.Date;
import java.util.List;

import static java.util.Arrays.asList;

public class Schemas {

    static final List<SemanticModel> schemas = asList(new SemanticModel("Sales1", null,
                    "select product_id, amount from db.sales where 1=1",
                    asList(SemanticColumn.dimColumn("amount", Double.class, "amount"),
                            SemanticColumn.dimColumn("productId", String.class, "product_id"))),
            new SemanticModel("Orders", "db.orders", null,
                    asList(SemanticColumn.dimColumn("totalPrice", Double.class, "total_price"),
                            SemanticColumn.dimColumn("orderId", String.class, "id"),
                            SemanticColumn.dimColumn("status", String.class, "order_status"),
                            SemanticColumn.builder()
                                    .name("canRefund")
                                    .type(Boolean.class)
                                    .expr("CASE WHEN status = 'completed' THEN true ELSE false END")
                                    .semanticType("Dimension")
                                    .isPhysicalExpression(false)
                                    .build(),
                            SemanticColumn.builder()
                                    .name("isGoldenDeal")
                                    .type(Boolean.class)
                                    .expr("CASE WHEN totalPrice > 1000 THEN true ELSE false END")
                                    .semanticType("Dimension")
                                    .isPhysicalExpression(false)
                                    .build())),
            new SemanticModel("order_line_items", "db.order_line_items", null,
                    asList(SemanticColumn.dimColumn("price", Double.class, "price"),
                            SemanticColumn.dimColumn("orderId", String.class, "order_id"),
                            SemanticColumn.dimColumn("productId", String.class, "product_id"),
                            SemanticColumn.builder().name("l_shipdate").type(Date.class).expr("TO_DATE(l_shipdate, 'yyyy-MM-dd')").semanticType("Dimension").isPhysicalExpression(false).build()
                    )),
            new SemanticModel("Sales", "Sales", null,
                    asList(SemanticColumn.dimColumn("amount", Double.class, "sales_amount"),
                            SemanticColumn.dimColumn("cost", Double.class, "sales_cost"),
                            SemanticColumn.semColumn("profit", Double.class, "amount - cost", null,
                                    "Dimension", null),
                            SemanticColumn.dimColumn("productId", String.class, "product_id"),
                            SemanticColumn.dimColumn("salesDepartment", String.class, "sales_department"),
                            SemanticColumn.builder()
                                    .name("isStarProduct")
                                    .type(Boolean.class)
                                    .expr("CASE WHEN amount > 100 THEN true ELSE false END")
                                    .semanticType("Dimension")
                                    .isPhysicalExpression(false)
                                    .build(),
                            SemanticColumn.dimColumn("salesDate", Date.class, "sales_date"),
                            SemanticColumn.builder()
                                    .name("quarter")
                                    .type(String.class)
                                    .expr("to_char(salesDate, 'YYYY-MM')")
                                    .semanticType("Dimension")
                                    .isPhysicalExpression(false)
                                    .build())),
            new SemanticModel("Products", "Products", null,
                    asList(SemanticColumn.dimColumn("id", String.class, "id"),
                            SemanticColumn.dimColumn("name", String.class, "prod_name"),
                            SemanticColumn.dimColumn("price", Double.class, "unit_price"))),
            new SemanticModel("ViewSales", "Sales", null,
                    asList(SemanticColumn.column("totalSales", Double.class, "salesAmount", null,
                                    "metrics", "sum"),
                            SemanticColumn.dimColumn("salesAmount", Double.class, "sales_amount"),
                            SemanticColumn.createSemanticColumn("goodSales", Integer.class, "productId", null,
                                    "metrics", "count", "salesAmount > 100"),
                            SemanticColumn.createSemanticColumn("totalCount", Integer.class, "productId", null,
                                    "metrics", "count", null),
                            SemanticColumn.createSemanticColumn("avgSales", Integer.class, "totalSales / totalCount", null, "metrics",
                                    null, null),
                            SemanticColumn.dimColumn("productId", String.class, "product_id"))));


    static final List<SemanticModel> schemas2
            // @formatter:off
            =asList(new SemanticModel("CustomerInfo", null, """
                    SELECT * FROM customer c
                    LEFT JOIN nation n ON c.c_nationKey = n.n_nationKey
                    """, asList(SemanticColumn.dimColumn("customerId", Integer.class, "c_custkey"),
                    SemanticColumn.dimColumn("customer_name", String.class, "c_name"),
                    SemanticColumn.dimColumn("address", String.class, "c_address"),
                    SemanticColumn.dimColumn("nationKey", Integer.class, "c_nationkey"),
                    SemanticColumn.dimColumn("phone", String.class, "c_phone"),
                    SemanticColumn.dimColumn("accountBalance", Double.class, "c_acctbal"),
                    SemanticColumn.dimColumn("marketSegment", String.class, "c_mktsegment"),
                    SemanticColumn.dimColumn("customer_comment", String.class, "c_comment"),
                    SemanticColumn.dimColumn("nation_name", String.class, "n_name"),
                    SemanticColumn.dimColumn("regionKey", Integer.class, "n_regionkey"),
                    SemanticColumn.dimColumn("nation_comment", String.class, "n_comment"))),
            new SemanticModel("Customer", "customer", null,
                    asList(SemanticColumn.dimColumn("customerId", Integer.class, "c_custkey"),
                            SemanticColumn.dimColumn("name", String.class, "c_name"),
                            SemanticColumn.dimColumn("address", String.class, "c_address"),
                            SemanticColumn.dimColumn("nationKey", Integer.class, "c_nationkey"),
                            SemanticColumn.dimColumn("phone", String.class, "c_phone"),
                            SemanticColumn.dimColumn("accountBalance", Double.class, "c_acctbal"),
                            SemanticColumn.dimColumn("marketSegment", String.class, "c_mktsegment"),
                            SemanticColumn.dimColumn("comment", String.class, "c_comment"))),

            new SemanticModel("LineItem", "lineitem", null,
                    asList(SemanticColumn.dimColumn("orderId", Integer.class, "l_orderkey"),
                            SemanticColumn.dimColumn("partId", Integer.class, "l_partkey"),
                            SemanticColumn.dimColumn("supplierId", Integer.class, "l_suppkey"),
                            SemanticColumn.dimColumn("lineNumber", Integer.class, "l_linenumber"),
                            SemanticColumn.dimColumn("quantity", Double.class, "l_quantity"),
                            SemanticColumn.dimColumn("extendedPrice", Double.class, "l_extendedprice"),
                            SemanticColumn.dimColumn("discount", Double.class, "l_discount"),
                            SemanticColumn.dimColumn("tax", Double.class, "l_tax"),
                            SemanticColumn.dimColumn("returnFlag", String.class, "l_returnflag"),
                            SemanticColumn.dimColumn("lineStatus", String.class, "l_linestatus"),
                            SemanticColumn.dimColumn("shipDate", Date.class, "l_shipdate"),
                            SemanticColumn.dimColumn("commitDate", Date.class, "l_commitdate"),
                            SemanticColumn.dimColumn("receiptDate", Date.class, "l_receiptdate"),
                            SemanticColumn.dimColumn("shipInstruction", String.class, "l_shipinstruct"),
                            SemanticColumn.dimColumn("shipMode", String.class, "l_shipmode"),
                            SemanticColumn.dimColumn("comment", String.class, "l_comment"))),

            new SemanticModel("Nation", "nation", null,
                    asList(SemanticColumn.dimColumn("nationKey", Integer.class, "n_nationkey"),
                            SemanticColumn.dimColumn("name", String.class, "n_name"),
                            SemanticColumn.dimColumn("regionKey", Integer.class, "n_regionkey"),
                            SemanticColumn.dimColumn("comment", String.class, "n_comment"))),

            new SemanticModel("Orders", "orders", null,
                    asList(SemanticColumn.dimColumn("orderId", Integer.class, "o_orderkey"),
                            SemanticColumn.dimColumn("customerId", Integer.class, "o_custkey"),
                            SemanticColumn.dimColumn("status", String.class, "o_orderstatus"),
                            SemanticColumn.dimColumn("totalPrice", Double.class, "o_totalprice"),
                            SemanticColumn.builder()
                                .name("isGoodSales")
                                .type(Boolean.class)
                                .expr("CASE WHEN totalPrice > 500 THEN true ELSE false END")
                                .semanticType("Dimension")
                                .isPhysicalExpression(false)
                                .build(),
                            SemanticColumn.dimColumn("orderDate", Date.class, "o_orderdate"),
                            SemanticColumn.dimColumn("orderPriority", String.class, "o_orderpriority"),
                            SemanticColumn.dimColumn("clerk", String.class, "o_clerk"),
                            SemanticColumn.dimColumn("shipPriority", Integer.class, "o_shippriority"),
                            SemanticColumn.dimColumn("comment", String.class, "o_comment"),
                            SemanticColumn.builder()
                                .name("canRefund")
                                .type(Boolean.class)
                                .expr("CASE WHEN status = 'Completed' THEN true ELSE false END")
                                .semanticType("Dimension")
                                .isPhysicalExpression(false)
                                .build())),

            new SemanticModel("Part", "part", null,
                    asList(SemanticColumn.dimColumn("partId", Integer.class, "p_partkey"),
                            SemanticColumn.dimColumn("name", String.class, "p_name"),
                            SemanticColumn.dimColumn("manufacturer", String.class, "p_mfgr"),
                            SemanticColumn.dimColumn("brand", String.class, "p_brand"),
                            SemanticColumn.dimColumn("type", String.class, "p_type"),
                            SemanticColumn.dimColumn("size", Integer.class, "p_size"),
                            SemanticColumn.dimColumn("container", String.class, "p_container"),
                            SemanticColumn.dimColumn("retailPrice", Double.class, "p_retailprice"),
                            SemanticColumn.dimColumn("comment", String.class, "p_comment"))),

            new SemanticModel("ViewTotalSales", "lineitem", null,
                    asList(SemanticColumn.column("totalSales", Double.class, "extendedPrice", null,
                                    "metrics", "sum"),
                            SemanticColumn.builder()
                                .name("discountedSales")
                                .type(Double.class)
                                .expr("SUM(extendedPrice * (1 - discount))")
                                .semanticType("metrics")
                                .isPhysicalExpression(false)
                                .build(),
                            SemanticColumn.column("orderCount", Integer.class, "orderkey", null,
                                    "metrics", "count"),
                            SemanticColumn.dimColumn("extendedPrice", Double.class, "l_extendedprice"),
                            SemanticColumn.dimColumn("orderkey", Integer.class, "l_orderkey"),                     SemanticColumn.dimColumn("discount", Double.class, "l_discount"),
                            SemanticColumn.dimColumn("partId", Integer.class, "l_partkey"))));
            // @formatter:on

    static final List<SemanticModel> kustStudentsSchemas =
            // @formatter:off
            asList(new SemanticModel("DWD_XSJBSJZLB", "LY_DW2.DWD_XSJBSJZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("XM", String.class, "XM"),
                            SemanticColumn.dimColumn("HYZKM", String.class, "HYZKM"),
                            SemanticColumn.dimColumn("XBM", String.class, "XBM"),
                            SemanticColumn.dimColumn("SFDSZN", String.class, "SFDSZN"),
                            SemanticColumn.dimColumn("GJDQM", String.class, "GJDQM"),
                            SemanticColumn.dimColumn("JGM", String.class, "JGM"),
                            SemanticColumn.dimColumn("XYDM", String.class, "XYDM"),
                            SemanticColumn.dimColumn("BJMC", String.class, "BJMC"),
                            SemanticColumn.dimColumn("TC", String.class, "TC"),
                            SemanticColumn.dimColumn("AH", String.class, "AH"),
                            SemanticColumn.dimColumn("YHKH", String.class, "YHKH"),
                            SemanticColumn.dimColumn("CSRQ", String.class, "CSRQ"),
                            SemanticColumn.dimColumn("SFZJH", String.class, "SFZJH"),
                            SemanticColumn.dimColumn("SFZJLXM", String.class, "SFZJLXM"),
                            SemanticColumn.dimColumn("GATQWM", String.class, "GATQWM"),
                            SemanticColumn.dimColumn("JKZKM", String.class, "JKZKM"),
                            SemanticColumn.dimColumn("ZZMMM", String.class, "ZZMMM"),
                            SemanticColumn.dimColumn("XXM", String.class, "XXM"),
                            SemanticColumn.dimColumn("MZM", String.class, "MZM"),
                            SemanticColumn.dimColumn("XYZJM", String.class, "XYZJM"),
                            SemanticColumn.dimColumn("NJ", String.class, "NJ"),
                            SemanticColumn.dimColumn("RXNJ", String.class, "RXNJ"),
                            SemanticColumn.dimColumn("ZYMC", String.class, "ZYMC"),
                            SemanticColumn.dimColumn("XSLBM", String.class, "XSLBM"),
                            SemanticColumn.dimColumn("YXMC", String.class, "YXMC"),
                            SemanticColumn.dimColumn("XJZT", String.class, "XJZT"),
                            SemanticColumn.dimColumn("SFZX", String.class, "SFZX"),
                            SemanticColumn.dimColumn("SFZJ", String.class, "SFZJ"),
                            SemanticColumn.dimColumn("SFYXJ", String.class, "SFYXJ"))),
                            
            new SemanticModel("DWD_XSSFXXZLB", "LY_DW2.DWD_XSSFXXZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("SFXMMC", String.class, "SFXMMC"),
                            SemanticColumn.dimColumn("SFQJMC", String.class, "SFQJMC"),
                            SemanticColumn.dimColumn("XM", String.class, "XM"),
                            SemanticColumn.dimColumn("LXND", String.class, "LXND"),
                            SemanticColumn.column("DWD_XSSFXXZLB_JMJE", double.class, "JMJE", null, "Measure", "sum"),
                            SemanticColumn.column("DWD_XSSFXXZLB_TFJE", double.class, "TFJE", null, "Measure", "sum"),
                            SemanticColumn.column("DWD_XSSFXXZLB_QFJE", double.class, "QFJE", null, "Measure", "sum"),
                            SemanticColumn.column("DWD_XSSFXXZLB_SJJE", double.class, "SJJE", null, "Measure", "sum"),
                            SemanticColumn.column("DWD_XSSFXXZLB_DKJE", double.class, "DKJE", null, "Measure", "sum"),
                            SemanticColumn.column("DWD_XSSFXXZLB_YJJE", double.class, "YJJE", null, "Measure", "sum"))),
                            
            new SemanticModel("DWD_XSCCZLB", "LY_DW2.DWD_XSCCZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("WJJK", String.class, "WJJK"),
                            SemanticColumn.dimColumn("CFZTM", String.class, "CFZTM"),
                            SemanticColumn.dimColumn("CFMCM", String.class, "CFMCM"),
                            SemanticColumn.dimColumn("CLBM", String.class, "CLBM"),
                            SemanticColumn.dimColumn("CFYY", String.class, "CFYY"),
                            SemanticColumn.dimColumn("CFGYR", String.class, "CFGYR"),
                            SemanticColumn.dimColumn("CFCXWH", String.class, "CFCXWH"),
                            SemanticColumn.dimColumn("SWHSYJL", String.class, "SWHSYJL"),
                            SemanticColumn.dimColumn("WJRQ", String.class, "WJRQ"),
                            SemanticColumn.dimColumn("CFRQ", String.class, "CFRQ"))),
                            
            new SemanticModel("DWD_SSWGWGXXSJB", "LY_DW2.DWD_SSWGWGXXSJB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("JCFX", String.class, "JCFX"),
                            SemanticColumn.dimColumn("DQBZ", String.class, "DQBZ"),
                            SemanticColumn.dimColumn("YGSSJ", Date.class, "YGSSJ"),
                            SemanticColumn.dimColumn("WJFS", String.class, "WJFS"),
                            SemanticColumn.dimColumn("JCSJ", Date.class, "JCSJ"))),
                            
            new SemanticModel("DWD_SQ_MJXXSJZLB", "LY_DW2.DWD_SQ_MJXXSJZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("JCFX", String.class, "JCFX"),
                            SemanticColumn.dimColumn("JCSJ", Date.class, "JCSJ"),
                            SemanticColumn.dimColumn("DQBZ", String.class, "DQBZ"))),
                            
            new SemanticModel("DWD_TSGMJTGXXSJZLB", "LY_DW2.DWD_TSGMJTGXXSJZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("ZJBH", String.class, "ZJBH"),
                            SemanticColumn.dimColumn("ZJMC", String.class, "ZJMC"),
                            SemanticColumn.dimColumn("FX", String.class, "FX"),
                            SemanticColumn.dimColumn("TSG", String.class, "TSG"),
                            SemanticColumn.dimColumn("XSLB", String.class, "XSLB"),
                            SemanticColumn.dimColumn("TGSJ", Date.class, "TGSJ"),
                            SemanticColumn.dimColumn("SKFS", String.class, "SKFS"))),
                            
            new SemanticModel("DWD_QJXXZLB", "LY_DW2.DWD_QJXXZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("XJSJ", String.class, "XJSJ"),
                            SemanticColumn.dimColumn("QJLX", String.class, "QJLX"),
                            SemanticColumn.dimColumn("QJZT", String.class, "QJZT"),
                            SemanticColumn.dimColumn("SFLX", String.class, "SFLX"),
                            SemanticColumn.dimColumn("JJLXRDH", String.class, "JJLXRDH"),
                            SemanticColumn.dimColumn("JSSJ", Date.class, "JSSJ"),
                            SemanticColumn.dimColumn("QJNR", String.class, "QJNR"),
                            SemanticColumn.physicalExpression("KSSJ", Date.class, "TO_DATE(KSSJ, 'yyyy-MM-dd')"),
                            SemanticColumn.dimColumn("QJSJ", Date.class, "QJSJ"))),
                            
            new SemanticModel("DWD_XJYDSJL", "LY_DW2.DWD_XJYDSJL", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("YDYY", String.class, "YDYY"),
                            SemanticColumn.dimColumn("YDLXMC", String.class, "YDLXMC"),
                            SemanticColumn.dimColumn("YDSJ", String.class, "YDSJ"))),
                            
            new SemanticModel("DWD_XSJLZLB", "LY_DW2.DWD_XSJLZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("JLMC", String.class, "JLMC"),
                            SemanticColumn.dimColumn("HJSJ", .class, "HJSJ"),
                            SemanticColumn.dimColumn("HJXQ", String.class, "HJXQ"),
                            SemanticColumn.dimColumn("BJDW", String.class, "BJDW"),
                            SemanticColumn.dimColumn("JLJE", double.class, "JLJE"),
                            SemanticColumn.dimColumn("JLJBM", String.class, "JLJBM"),
                            SemanticColumn.dimColumn("JLYY", String.class, "JLYY"),
                            SemanticColumn.dimColumn("HJXND", String.class, "HJXND"),
                            SemanticColumn.dimColumn("JLDJM", String.class, "JLDJM"))),
                            
            new SemanticModel("DWD_ZHCPCJXXZLB", "LY_DW.DWD_ZHCPCJXXZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("XQ", String.class, "XQ"),
                            SemanticColumn.dimColumn("DWD_ZHCPCJXXZLB_CJ", double.class, "CJ"),
                            SemanticColumn.dimColumn("DWD_ZHCPCJXXZLB_BJPM", double.class, "BJPM"),
                            SemanticColumn.dimColumn("DWD_ZHCPCJXXZLB_ZYPM", double.class, "ZYPM"),
                            SemanticColumn.column("DWD_ZHCPCJXXZLB_CJ_sum", double.class, "DWD_ZHCPCJXXZLB_CJ", null, "Measure", "sum"),
                            SemanticColumn.column("DWD_ZHCPCJXXZLB_BJPM_max", double.class, "DWD_ZHCPCJXXZLB_BJPM", null, "Measure", "max"),
                            SemanticColumn.column("DWD_ZHCPCJXXZLB_ZYPM_max", double.class, "DWD_ZHCPCJXXZLB_ZYPM", null, "Measure", "max"))
            ),
                            
            new SemanticModel("DWD_ZHCPZBCJZLB", "LY_DW2.DWD_ZHCPZBCJZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("CPLXMC", String.class, "CPLXMC"),
                            SemanticColumn.dimColumn("XNXQ", String.class, "XNXQ"),
                            SemanticColumn.column("DWD_ZHCPZBCJZLB_PCFS", double.class, "PCFS", null, "Measure", "sum"))),
                            
            new SemanticModel("kustBKSJSXX", null, 
                    "select t1.XGH as XH,t2.TSMC,t2.JYSJ,t2.SJGHSJ\r\nfrom LY_DW2.DWD_DZJBSJZLB t1 \r\njoin LY_DW2.DWD_TSLSJYSJZLB t2 on t1.JSZH = t2.JSZH and t1.DZZT = '有效' and t1.DZLBMC = '本科生'",
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("TSMC", String.class, "TSMC"),
                            SemanticColumn.dimColumn("JYSJ", String.class, "JYSJ"),
                            SemanticColumn.dimColumn("SJGHSJ", String.class, "SJGHSJ"))),
                            
            new SemanticModel("kustXSQDXX", null, 
                    "select t1.XH,t1.QDSJ,t1.QDJG,t2.BT,t2.NR,t1.FZMC,t2.XQMC\r\nfrom LY_DW2.DWD_XSQDXXZLB t1\r\njoin LY_DW2.DWD_QDNRFBXXZLB t2 on t1.QDID = t2.WYBS",
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("QDSJ", Date.class, "QDSJ"),
                            SemanticColumn.dimColumn("QDJG", String.class, "QDJG"),
                            SemanticColumn.dimColumn("BT", String.class, "BT"),
                            SemanticColumn.dimColumn("NR", String.class, "NR"),
                            SemanticColumn.dimColumn("FZMC", String.class, "FZMC"),
                            SemanticColumn.dimColumn("XQMC", String.class, "XQMC"))),
                            
            new SemanticModel("kustXSKSCJXX", null, 
                    "select t1.XH,t1.XNXQ,t1.SFCXS,\r\n  t2.KCMC,t2.XF KCXF,t2.ZXS KCZXS,t2.MZXS KCMZXS,t2.KCFZR,\r\n  t3.JD,t3.PSCJ,t3.KCCJ,t3.ZCJ,t3.PJXFJD,t3.DJLKSCJ,t3.FSLKSCJ,t3.KSRQ,t3.SFJG\r\nfrom LY_DW2.DWD_XKSJLB t1\r\njoin LY_DW2.DWD_KCSJLB t2 on t1.KCH = t2.KCH\r\njoin LY_DW2.DWD_CJZLB t3 on t1.XH = t3.XH and t1.KCH = t3.KCH",
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("XNXQ", String.class, "XNXQ"),
                            SemanticColumn.dimColumn("SFCXS", String.class, "SFCXS"),
                            SemanticColumn.dimColumn("KCMC", String.class, "KCMC"),
                            SemanticColumn.dimColumn("KCFZR", String.class, "KCFZR"),
                            SemanticColumn.dimColumn("KSRQ", String.class, "KSRQ"),
                            SemanticColumn.dimColumn("SFJG", String.class, "SFJG"),
                            SemanticColumn.column("KCXF", double.class, "KCXF", null, "Dimension", null),
                            SemanticColumn.column("KCZXS", double.class, "KCZXS", null, "Dimension", null),
                            SemanticColumn.column("KCMZXS", double.class, "KCMZXS", null, "Dimension", null),
                            SemanticColumn.column("JD", double.class, "JD", null, "Dimension", null),
                            SemanticColumn.column("PSCJ", double.class, "PSCJ", null, "Dimension", null),
                            SemanticColumn.column("KCCJ", double.class, "KCCJ", null, "Dimension", null),
                            SemanticColumn.column("ZCJ", double.class, "ZCJ", null, "Dimension", null),
                            SemanticColumn.column("PJXFJD", double.class, "PJXFJD", null, "Dimension", null),
                            SemanticColumn.column("DJLKSCJ", double.class, "DJLKSCJ", null, "Dimension", null),
                            SemanticColumn.column("FSLKSCJ", double.class, "FSLKSCJ", null, "Dimension", null),

                            SemanticColumn.column("kustXSKSCJXX_KCXF", double.class, "KCXF", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_KCZXS", double.class, "KCZXS", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_KCMZXS", double.class, "KCMZXS", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_JD", double.class, "JD", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_PSCJ", double.class, "PSCJ", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_KCCJ", double.class, "KCCJ", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_ZCJ", double.class, "ZCJ", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_PJXFJD", double.class, "PJXFJD", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_DJLKSCJ", double.class, "DJLKSCJ", null, "Measure", "sum"),
                            SemanticColumn.column("kustXSKSCJXX_FSLKSCJ", double.class, "FSLKSCJ", null, "Measure", "sum")
                    )),
                            
            new SemanticModel("kustBKSXFXX", null, 
                    "select\r\n    t1.XGH XH,t1.ZJH,t1.ZHYE,t1.KH,t1.KNYE,t1.ZHKHRQ,t1.ZHYXRQ,t1.ZHZT,\r\n\t\tt2.ZDZH,t2.JYJE,t2.RZRQSJ,t2.YKCS,\r\n\t\tt3.DZ,t3.SHMC\r\nfrom LY_DW2.DWD_YKTZHXXZLB t1\r\njoin LY_DW2.DWD_YKTXFXXZLB t2 on t1.ZH = t2.ZH\r\njoin LY_DW2.DWD_YKTSHXXZLB t3 on t2.ZDZH = t3.ZH",
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("KH", String.class, "KH"),
                            SemanticColumn.dimColumn("ZHKHRQ", Date.class, "ZHKHRQ"),
                            SemanticColumn.dimColumn("ZHYXRQ", Date.class, "ZHYXRQ"),
                            SemanticColumn.dimColumn("ZHZT", String.class, "ZHZT"),
                            SemanticColumn.dimColumn("RZRQSJ", Date.class, "RZRQSJ"),
                            SemanticColumn.dimColumn("DZ", String.class, "DZ"),
                            SemanticColumn.dimColumn("SHMC", String.class, "SHMC"),
                            SemanticColumn.column("kustBKSXFXX_ZHYE", double.class, "ZHYE", null, "Measure", "sum"),
                            SemanticColumn.column("kustBKSXFXX_KNYE", double.class, "KNYE", null, "Measure", "sum"),
                            SemanticColumn.column("kustBKSXFXX_JYJE", double.class, "JYJE", null, "Measure", "sum"),
                            SemanticColumn.column("kustBKSXFXX_YKCS", double.class, "YKCS", null, "Measure", "sum"))),
                            
            new SemanticModel("DWD_XSZSXXSJZLB", "LY_DW2.DWD_XSZSXXSJZLB", null,
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("XQMC", String.class, "XQMC"),
                            SemanticColumn.dimColumn("LC", String.class, "LC"),
                            SemanticColumn.dimColumn("RZSJ", Date.class, "RZSJ"),
                            SemanticColumn.dimColumn("CWH", String.class, "CWH"),
                            SemanticColumn.dimColumn("FJH", String.class, "FJH"),
                            SemanticColumn.dimColumn("LD", String.class, "LD"),
                            SemanticColumn.dimColumn("ZT", String.class, "ZT"))),
                            
            new SemanticModel("DWD_XSZZZLB", null, 
                    "SELECT T.XH,T.XXSQBZ,T.DQZT ZZDQZT,T.XQ  ZZXQ, T.PROJECT_NAME,T.ZZJE FROM LY_DW2.DWD_XSZZZLB T",
                    asList(SemanticColumn.idColumn("XH", String.class, "XH"),
                            SemanticColumn.dimColumn("XXSQBZ", String.class, "XXSQBZ"),
                            SemanticColumn.dimColumn("ZZDQZT", String.class, "ZZDQZT"),
                            SemanticColumn.dimColumn("ZZXQ", String.class, "ZZXQ"),
                            SemanticColumn.dimColumn("PROJECT_NAME", String.class, "PROJECT_NAME"),
                            SemanticColumn.column("DWD_XSZZZLB_ZZJE", double.class, "ZZJE", null, "Measure", "sum"))));
            // @formatter:on

    public static final List<SemanticRelationship> kustStudentRelationships = asList(
            new SemanticRelationship("DWD_XJYDSJL", "DWD_XSJBSJZLB", "inner join", "DWD_XJYDSJL.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("kustBKSXFXX", "DWD_XSJBSJZLB", "inner join", "kustBKSXFXX.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_QJXXZLB", "DWD_XSJBSJZLB", "inner join", "DWD_QJXXZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_TSGMJTGXXSJZLB", "DWD_XSJBSJZLB", "inner join", "DWD_TSGMJTGXXSJZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_SQ_MJXXSJZLB", "DWD_XSJBSJZLB", "inner join", "DWD_SQ_MJXXSJZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSSFXXZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSSFXXZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_SSWGWGXXSJB", "DWD_XSJBSJZLB", "inner join", "DWD_SSWGWGXXSJB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSCCZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSCCZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("kustXSQDXX", "DWD_XSJBSJZLB", "inner join", "kustXSQDXX.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_ZHCPZBCJZLB", "DWD_XSJBSJZLB", "inner join", "DWD_ZHCPZBCJZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_ZHCPCJXXZLB", "DWD_XSJBSJZLB", "left join", "DWD_ZHCPCJXXZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("kustXSKSCJXX", "DWD_XSJBSJZLB", "inner join", "kustXSKSCJXX.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSJLZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSJLZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSZZZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSZZZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSZSXXSJZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSZSXXSJZLB.XH = DWD_XSJBSJZLB.XH")
    );
}
