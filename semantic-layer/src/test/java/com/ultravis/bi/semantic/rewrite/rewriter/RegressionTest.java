package com.ultravis.bi.semantic.rewrite.rewriter;

import com.ultravis.bi.semantic.rewrite.adapters.oracle.UltravisOracleSqlDialect;
import org.apache.calcite.sql.validate.SqlConformanceEnum;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.params.provider.Arguments.of;

public class RegressionTest extends BaseSemanticSqlParserTest {

    RegressionTest() {
        super(Schemas.kustStudentsSchemas, Schemas.kustStudentRelationships, new SemanticSqlParserConfig()
                .withSqlDialect(UltravisOracleSqlDialect.DEFAULT)
                .withSqlConformance(SqlConformanceEnum.LENIENT)
                .withStrictJoin(true)
                .withCompactColumnAlias(true));
    }

    static Stream<Arguments> ALL_CASES = Stream.of(
            of(
                    "张杰成绩最好的科目-2025-06-25",
                    """
                            SELECT K.XH, K.KCMC, K.ZCJ FROM kustXSKSCJXX K INNER JOIN DWD_XSJBSJZLB S ON K.XH = S.XH WHERE S.XM = '张杰' ORDER BY K.ZCJ DESC LIMIT 1;
                            """,
                    """
                            SELECT "K"."XH", "K"."KCMC", "K"."ZCJ"
                            FROM (SELECT "XH", "KCMC", "ZCJ"
                            FROM (SELECT "t1"."XH", "t1"."XNXQ", "t1"."SFCXS", "t2"."KCMC", "t2"."XF" "KCXF", "t2"."ZXS" "KCZXS", "t2"."MZXS" "KCMZXS", "t2"."KCFZR", "t3"."JD", "t3"."PSCJ", "t3"."KCCJ", "t3"."ZCJ", "t3"."PJXFJD", "t3"."DJLKSCJ", "t3"."FSLKSCJ", "t3"."KSRQ", "t3"."SFJG"
                            FROM "LY_DW2"."DWD_XKSJLB" "t1"
                            INNER JOIN "LY_DW2"."DWD_KCSJLB" "t2" ON "t1"."KCH" = "t2"."KCH"
                            INNER JOIN "LY_DW2"."DWD_CJZLB" "t3" ON "t1"."XH" = "t3"."XH" AND "t1"."KCH" = "t3"."KCH")) "K"
                            INNER JOIN (SELECT "XH", "XM"
                            FROM "LY_DW2"."DWD_XSJBSJZLB") "S" ON "K"."XH" = "S"."XH"
                            WHERE "S"."XM" = '张杰'
                            ORDER BY "K"."ZCJ" DESC
                            FETCH NEXT 1 ROWS ONLY
                            """
            ),
            of(
                    "上个月迟到的学生人数-2025-06-25",
                    """
                            SELECT COUNT(DISTINCT kustXSQDXX.XH) AS 迟到学生人数
                            FROM kustXSQDXX
                            INNER JOIN DWD_XSJBSJZLB ON kustXSQDXX.XH = DWD_XSJBSJZLB.XH
                            WHERE kustXSQDXX.QDSJ >= DATE '2025-05-01'
                              AND kustXSQDXX.QDSJ < DATE '2025-06-01'
                              AND kustXSQDXX.QDJG = '迟到';
                            
                            """,
                    """
                            SELECT COUNT(DISTINCT "kustXSQDXX"."XH") "迟到学生人数"
                            FROM (SELECT "XH", "QDSJ", "QDJG"
                            FROM (SELECT "t1"."XH", "t1"."QDSJ", "t1"."QDJG", "t2"."BT", "t2"."NR", "t1"."FZMC", "t2"."XQMC"
                            FROM "LY_DW2"."DWD_XSQDXXZLB" "t1"
                            INNER JOIN "LY_DW2"."DWD_QDNRFBXXZLB" "t2" ON "t1"."QDID" = "t2"."WYBS")) "kustXSQDXX"
                            INNER JOIN (SELECT "XH"
                            FROM "LY_DW2"."DWD_XSJBSJZLB") "DWD_XSJBSJZLB" ON "kustXSQDXX"."XH" = "DWD_XSJBSJZLB"."XH"
                            WHERE "kustXSQDXX"."QDSJ" >= CAST(TO_DATE('2025-05-01', 'YYYY-MM-DD') AS TIMESTAMP(0)) AND "kustXSQDXX"."QDSJ" < CAST(TO_DATE('2025-06-01', 'YYYY-MM-DD') AS TIMESTAMP(0)) AND "kustXSQDXX"."QDJG" = '迟到'         
                            """
            ),
            of(
                    "张杰的基本信息-2025-06-25",
                    """
                            SELECT DWD_XSJBSJZLB.* FROM DWD_XSJBSJZLB WHERE DWD_XSJBSJZLB.XM = '张杰';
                            """,
                    """
                            SELECT "DWD_XSJBSJZLB".*
                            FROM (SELECT "XH", "XM", "HYZKM", "XBM", "SFDSZN", "GJDQM", "JGM", "XYDM", "BJMC", "TC", "AH", "YHKH", "CSRQ", "SFZJH", "SFZJLXM", "GATQWM", "JKZKM", "ZZMMM", "XXM", "MZM", "XYZJM", "NJ", "RXNJ", "ZYMC", "XSLBM", "YXMC", "XJZT", "SFZX", "SFZJ", "SFYXJ"
                            FROM "LY_DW2"."DWD_XSJBSJZLB") "DWD_XSJBSJZLB"
                            WHERE "DWD_XSJBSJZLB"."XM" = '张杰'
                            """
            ),
            of(
                    "过去 3 个月迟到超过 3 次的学生",
                    """
                            SELECT DWD_XSJBSJZLB.XH, DWD_XSJBSJZLB.XM
                            FROM DWD_XSJBSJZLB
                            INNER JOIN DWD_SSWGWGXXSJB ON DWD_XSJBSJZLB.XH = DWD_SSWGWGXXSJB.XH
                            WHERE DWD_SSWGWGXXSJB.JCSJ > DWD_SSWGWGXXSJB.YGSSJ
                              AND DWD_SSWGWGXXSJB.JCSJ >= CURRENT_DATE - Interval '90' DAY
                            GROUP BY DWD_XSJBSJZLB.XH, DWD_XSJBSJZLB.XM
                            HAVING COUNT(*) > 3
                            """,
                    """
                            SELECT "DWD_XSJBSJZLB"."XH", "DWD_XSJBSJZLB"."XM"
                            FROM (SELECT "XH", "XM"
                            FROM "LY_DW2"."DWD_XSJBSJZLB") "DWD_XSJBSJZLB"
                            INNER JOIN (SELECT "XH", "YGSSJ", "JCSJ"
                            FROM "LY_DW2"."DWD_SSWGWGXXSJB") "DWD_SSWGWGXXSJB" ON "DWD_XSJBSJZLB"."XH" = "DWD_SSWGWGXXSJB"."XH"
                            WHERE "DWD_SSWGWGXXSJB"."JCSJ" > "DWD_SSWGWGXXSJB"."YGSSJ" AND "DWD_SSWGWGXXSJB"."JCSJ" >= CAST(CURRENT_DATE - INTERVAL '90' DAY AS TIMESTAMP(0))
                            GROUP BY "DWD_XSJBSJZLB"."XH", "DWD_XSJBSJZLB"."XM"
                            HAVING COUNT(*) > CAST(3 AS NUMBER(19))
                            """
            ),
            of(
                    "去年的请假类型分布",
                    """
                            SELECT DWD_QJXXZLB.QJLX AS 请假类型, COUNT(*) AS 请假次数
                            FROM DWD_QJXXZLB
                            INNER JOIN DWD_XSJBSJZLB ON DWD_QJXXZLB.XH = DWD_XSJBSJZLB.XH
                            WHERE EXTRACT(YEAR FROM DWD_QJXXZLB.QJSJ) = EXTRACT(YEAR FROM CURRENT_DATE)
                            GROUP BY DWD_QJXXZLB.QJLX;
                            """,
                    """
                            SELECT "DWD_QJXXZLB"."QJLX" "请假类型", COUNT(*) "请假次数"
                            FROM (SELECT "XH", "QJLX", "QJSJ"
                            FROM "LY_DW2"."DWD_QJXXZLB") "DWD_QJXXZLB"
                            INNER JOIN (SELECT "XH"
                            FROM "LY_DW2"."DWD_XSJBSJZLB") "DWD_XSJBSJZLB" ON "DWD_QJXXZLB"."XH" = "DWD_XSJBSJZLB"."XH"
                            WHERE EXTRACT(YEAR FROM "DWD_QJXXZLB"."QJSJ") = EXTRACT(YEAR FROM CURRENT_DATE)
                            GROUP BY "DWD_QJXXZLB"."QJLX"
                            """
            ),
            of("去年的请假类型分布",
                    """
                            SELECT DWD_QJXXZLB.QJLX AS "请假类型", COUNT(*) AS "数量"
                            FROM DWD_QJXXZLB
                            WHERE EXTRACT(YEAR FROM DWD_QJXXZLB.KSSJ) = EXTRACT(YEAR FROM CURRENT_DATE)
                            GROUP BY DWD_QJXXZLB.QJLX;
                            """,
                    """
                            SELECT "DWD_QJXXZLB"."QJLX" "请假类型", COUNT(*) "数量"
                            FROM (SELECT "QJLX", "TO_DATE"("KSSJ", 'yyyy-MM-dd') "KSSJ"
                            FROM "LY_DW2"."DWD_QJXXZLB") "DWD_QJXXZLB"
                            WHERE EXTRACT(YEAR FROM "DWD_QJXXZLB"."KSSJ") = EXTRACT(YEAR FROM CURRENT_DATE)
                            GROUP BY "DWD_QJXXZLB"."QJLX"
                            """
            ),
            of(
                    "请假超过 7 天的学生",
                    """
                            SELECT DWD_QJXXZLB.XH, DWD_QJXXZLB.KSSJ, DWD_QJXXZLB.JSSJ, TIMESTAMPDIFF(DAY, DWD_QJXXZLB.KSSJ, DWD_QJXXZLB.JSSJ) AS leave_duration_days
                            FROM DWD_QJXXZLB
                            WHERE TIMESTAMPDIFF(DAY, DWD_QJXXZLB.KSSJ, DWD_QJXXZLB.JSSJ) > 7;
                            """,
                    """
                            SELECT "DWD_QJXXZLB"."XH", "DWD_QJXXZLB"."KSSJ", "DWD_QJXXZLB"."JSSJ", ("DWD_QJXXZLB"."JSSJ"  - "DWD_QJXXZLB"."KSSJ") "leave_duration_days"
                            FROM (SELECT "XH", "JSSJ", "TO_DATE"("KSSJ", 'yyyy-MM-dd') "KSSJ"
                            FROM "LY_DW2"."DWD_QJXXZLB") "DWD_QJXXZLB"
                            WHERE ("DWD_QJXXZLB"."JSSJ"  - "DWD_QJXXZLB"."KSSJ") > 7
                            """
            ),
            of(
                    "查询学籍异动信息视图",
                    """
                            SELECT DWD_XJYDSJL.*
                            FROM DWD_XSJBSJZLB
                            INNER JOIN (SELECT XH AS 学号, YDYY AS 异动原因, YDLXMC AS 异动类型名称, YDSJ AS 异动时间 FROM DWD_XJYDSJL) AS DWD_XJYDSJL ON DWD_XJYDSJL.学号 = DWD_XSJBSJZLB.XH \s
                            WHERE DWD_XSJBSJZLB.XH = '1234'
                            """,
                    """
                            SELECT "DWD_XJYDSJL".*
                            FROM (SELECT "XH"
                            FROM "LY_DW2"."DWD_XSJBSJZLB") "DWD_XSJBSJZLB"
                            INNER JOIN (SELECT "XH" "学号", "YDYY" "异动原因", "YDLXMC" "异动类型名称", "YDSJ" "异动时间"
                            FROM (SELECT "XH", "YDYY", "YDLXMC", "YDSJ"
                            FROM "LY_DW2"."DWD_XJYDSJL") "DWD_XJYDSJL") "DWD_XJYDSJL" ON "DWD_XJYDSJL"."学号" = "DWD_XSJBSJZLB"."XH"
                            WHERE "DWD_XSJBSJZLB"."XH" = '1234'
                            """
            ),
            of(
                    "头天晚上晚归第二天未签到的学生",
                    """
                            SELECT DWD_XSJBSJZLB.XH, DWD_XSJBSJZLB.XM
                            FROM DWD_SQ_MJXXSJZLB
                            INNER JOIN DWD_XSJBSJZLB ON DWD_SQ_MJXXSJZLB.XH = DWD_XSJBSJZLB.XH
                            LEFT JOIN kustXSQDXX ON DWD_XSJBSJZLB.XH = kustXSQDXX.XH
                            AND kustXSQDXX.QDSJ = CAST(TIMESTAMPADD(DAY, 1, DWD_SQ_MJXXSJZLB.JCSJ) AS TIMESTAMP(0))
                            WHERE DWD_SQ_MJXXSJZLB.JCFX = '进入'
                            AND EXTRACT(HOUR FROM DWD_SQ_MJXXSJZLB.JCSJ) >= 22
                            AND kustXSQDXX.QDSJ IS NULL
                            """,
                    """
                            SELECT "DWD_XSJBSJZLB"."XH", "DWD_XSJBSJZLB"."XM"
                            FROM (SELECT "XH", "JCFX", "JCSJ"
                            FROM "LY_DW2"."DWD_SQ_MJXXSJZLB") "DWD_SQ_MJXXSJZLB"
                            INNER JOIN (SELECT "XH", "XM"
                            FROM "LY_DW2"."DWD_XSJBSJZLB") "DWD_XSJBSJZLB" ON "DWD_SQ_MJXXSJZLB"."XH" = "DWD_XSJBSJZLB"."XH"
                            LEFT JOIN (SELECT "XH", "QDSJ"
                            FROM (SELECT "t1"."XH", "t1"."QDSJ", "t1"."QDJG", "t2"."BT", "t2"."NR", "t1"."FZMC", "t2"."XQMC"
                            FROM "LY_DW2"."DWD_XSQDXXZLB" "t1"
                            INNER JOIN "LY_DW2"."DWD_QDNRFBXXZLB" "t2" ON "t1"."QDID" = "t2"."WYBS")) "kustXSQDXX" ON "DWD_XSJBSJZLB"."XH" = "kustXSQDXX"."XH" AND "kustXSQDXX"."QDSJ" = CAST("DWD_SQ_MJXXSJZLB"."JCSJ"  + INTERVAL '1 ' DAY AS TIMESTAMP(0))
                            WHERE "DWD_SQ_MJXXSJZLB"."JCFX" = '进入' AND EXTRACT(HOUR FROM "DWD_SQ_MJXXSJZLB"."JCSJ") >= CAST(22 AS NUMBER(19)) AND "kustXSQDXX"."QDSJ" IS NULL
                            """
            ),
            of(
                    "张杰上个月迟到了几次",
                    """
                            SELECT COUNT(*) AS "迟到次数"
                            FROM DWD_XSJLZLB
                            INNER JOIN DWD_XSJBSJZLB ON DWD_XSJLZLB.XH = DWD_XSJBSJZLB.XH
                            WHERE DWD_XSJBSJZLB.XM = '张杰'
                                AND DWD_XSJLZLB.HJSJ >= TIMESTAMPADD(MONTH, -1, LAST_DAY(CURRENT_DATE))
                                AND DWD_XSJLZLB.HJSJ < CURRENT_DATE AND DWD_XSJLZLB.JLMC LIKE '%迟到%'
                            """,
                    """
                            SELECT COUNT(*) "迟到次数"
                            FROM (SELECT "XH", "JLMC", "HJSJ"
                            FROM "LY_DW2"."DWD_XSJLZLB") "DWD_XSJLZLB"
                            INNER JOIN (SELECT "XH", "XM"
                            FROM "LY_DW2"."DWD_XSJBSJZLB") "DWD_XSJBSJZLB" ON "DWD_XSJLZLB"."XH" = "DWD_XSJBSJZLB"."XH"
                            WHERE "DWD_XSJBSJZLB"."XM" = '张杰' AND "DWD_XSJLZLB"."HJSJ" >= CAST(ADD_MONTHS(LAST_DAY(CURRENT_DATE) , -1) AS TIMESTAMP(0)) AND "DWD_XSJLZLB"."HJSJ" < CAST(CURRENT_DATE AS TIMESTAMP(0)) AND "DWD_XSJLZLB"."JLMC" LIKE '%迟到%'
                            """
            )
    );

    public static Stream<Arguments> allCases() {
        // FIXME org/apache/calcite/sql/dialect/OracleSqlDialect.java:204
        // FIXME MONTH 无法被转为 SQLLiteral
        // TODO Oracle 对 FLOOR(dateTime TO timeUnit) 的解析有BUG
        // TODO INTERVAL 不被支持(可选: TIMESTAMPADD(MONTH, -1, CURRENT_DATE))
        // TODO DATE_TRUNC 不被支持
        // COUNT 被用作别名，导致解析器报错
        //
        return ALL_CASES;
    }

    @ParameterizedTest(name = "{index}-{0}")
    @MethodSource("allCases")
    void test_all_cases(String displayName, String originalSql, String expectedSql) {
        assertTransformOk(originalSql, expectedSql);
    }
}
