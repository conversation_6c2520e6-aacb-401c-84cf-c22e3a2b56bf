package com.ultravis.bi.semantic.rewrite.rewriter;

import org.apache.calcite.avatica.util.Casing;
import org.apache.calcite.sql.parser.SqlParser;
import org.junit.jupiter.api.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.ultravis.bi.semantic.rewrite.rewriter.SemanticSqlParser.withPagination;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class SemanticSqlParserTest extends BaseSemanticSqlParserTest {


    public SemanticSqlParserTest() {
        super(
                Schemas.schemas,
                List.of(),
                new SemanticSqlParserConfig()
                        .withStrictJoin(false)
                        .withSqlParserConfig(SqlParser.config()
                                .withCaseSensitive(false).withQuotedCasing(Casing.TO_UPPER)
                                .withUnquotedCasing(Casing.TO_UPPER))
        );
    }

    @Test
    public void test_parse_simple_select_query() {
        // @formatter:off
        String originalSql =
                "SELECT productId, amount " +
                        "FROM Sales s ";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "PRODUCTID", "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "S"\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_where() {
        // @formatter:off
        String originalSql = """
                SELECT productId, amount
                FROM Sales s
                WHERE amount > 100
                """;
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "PRODUCTID", "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "S"
WHERE "S"."amount" > CAST(100 AS DOUBLE PRECISION)\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_group_by() {
        // @formatter:off
        String originalSql =
                "SELECT productId, sum(s.amount) as amount " +
                        "FROM Sales s " +
                        "GROUP BY productId";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "S"
GROUP BY "S"."productId"\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_group_by_of_qualified_name() {
        // @formatter:off
        String originalSql =
                "SELECT productId, sum(s.amount) as amount " +
                        "FROM Sales s " +
                        "GROUP BY s.productId";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "S"
GROUP BY "S"."productId"\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_grouping_sets() {
        // @formatter:off
        String originalSql = """
                SELECT productId, sum(s.amount) as amount, GROUPING(s.productId) AS prod_grouped, GROUPING(s.salesDepartment) AS dept_grouped
                FROM Sales s
                GROUP BY GROUPING SETS(
                    (s.productId, s.salesDepartment),
                    (s.productId),
                    (s.salesDepartment),
                    ()
                )
                """;
        // @formatter:on

        // @formatter:off
        String expectedSql = """
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT", GROUPING("S"."PRODUCTID") AS "PROD_GROUPED", GROUPING("S"."SALESDEPARTMENT") AS "DEPT_GROUPED"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "S"
                GROUP BY GROUPING SETS(("S"."productId", "S"."salesDepartment"), "S"."productId", "S"."salesDepartment", ())\
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_rollup() {
        // @formatter:off
        String originalSql =
                "SELECT productId, sum(s.amount) as amount " +
                        "FROM Sales s " +
                        "GROUP BY ROLLUP(s.productId, s.salesDepartment)";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
            SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
            FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
            FROM "SALES") AS "S"
            GROUP BY ROLLUP("S"."productId", "S"."salesDepartment")\
            """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_cube() {
        // @formatter:off
        String originalSql =
                "SELECT productId, sum(s.amount) as amount " +
                        "FROM Sales s " +
                        "GROUP BY CUBE(s.productId, s.salesDepartment)";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
FROM "SALES") AS "S"
GROUP BY CUBE("S"."productId", "S"."salesDepartment")\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_having() {
        // @formatter:off
        String originalSql =
                "SELECT productId, sum(s.amount) as amount " +
                        "FROM Sales s " +
                        "GROUP BY productId " +
                        "HAVING sum(s.amount) > 100";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "S"
GROUP BY "S"."productId"
HAVING SUM("S"."amount") > CAST(100 AS DOUBLE PRECISION)\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_order_by() {
        // @formatter:off
        String originalSql =
                "SELECT productId, amount " +
                        "FROM Sales s ORDER BY " +
                        "s.amount DESC";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "PRODUCTID", "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "S"
ORDER BY "S"."amount" DESC\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_limit() {
        // @formatter:off
        String originalSql =
                "SELECT productId, amount " +
                        "FROM Sales s " +
                        "ORDER BY s.amount DESC LIMIT 10 OFFSET 5";
        // @formatter:on

        // @formatter:off
        String expectedSql =
                """
SELECT "PRODUCTID", "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "S"
ORDER BY "S"."amount" DESC
OFFSET 5 ROWS
FETCH NEXT 10 ROWS ONLY\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_select_query_with_join() {
        // @formatter:off
        String originalSql =
                "SELECT name, sum(s.amount) as amount " +
                        "FROM Sales s " +
                        "INNER JOIN Products p ON s.productId = p.id " +
                        "WHERE 1 = 1 AND name = 'apple' " +
                        "GROUP BY name";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "NAME", SUM("S"."AMOUNT") AS "AMOUNT"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "S"
INNER JOIN (SELECT "ID" AS "ID", "PROD_NAME" AS "NAME"
FROM "PRODUCTS") AS "P" ON "S"."productId" = "P"."id"
WHERE 1 = 1 AND "P"."name" = 'apple'
GROUP BY "P"."name"\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_cte_query() {
        // @formatter:off
        String originalSql = """
                WITH ss as (select "amount", "productId" from Sales),
                pp as (select "id", "name", "price" from Products)
                SELECT name, sum(s.amount) as amount
                from ss s
                INNER JOIN pp p ON s.ProductId = p.Id
                GROUP BY name
                """;
        // @formatter:on

        // @formatter:off
        String expectedSql = """
WITH "SS" AS (SELECT "AMOUNT", "PRODUCTID"
FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM "SALES") AS "Sales"), "PP" AS (SELECT "ID", "NAME", "PRICE"
FROM (SELECT "ID" AS "ID", "PROD_NAME" AS "NAME", "UNIT_PRICE" AS "PRICE"
FROM "PRODUCTS") AS "Products") SELECT "NAME", SUM("S"."AMOUNT") AS "AMOUNT"
FROM "SS" AS "S"
INNER JOIN "PP" AS "P" ON "S"."PRODUCTID" = "P"."ID"
GROUP BY "P"."NAME"\
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_calculated_dimensions_in_select_list() {
        // @formatter:off
        String originalSql =
                "SELECT orderId, status, canRefund " +
                "FROM Orders o";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "ORDERID", "STATUS", "CANREFUND"
FROM (SELECT "ORDERID", "STATUS", CASE WHEN "STATUS" = 'completed' THEN TRUE ELSE FALSE END AS "CANREFUND"
FROM (SELECT "ID" AS "ORDERID", "ORDER_STATUS" AS "STATUS"
FROM "DB"."ORDERS")) AS "O"
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_self_join() {
        // @formatter:off
        String originalSql = """
            SELECT o1.orderId, o1.status, o2.orderId, o2.status
            FROM Orders o1
            INNER JOIN Orders o2 ON o2.orderId = 's_' || o1.orderId
            """;
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "O1"."ORDERID", "O1"."STATUS", "O2"."ORDERID", "O2"."STATUS"
FROM (SELECT "ID" AS "ORDERID", "ORDER_STATUS" AS "STATUS"
FROM "DB"."ORDERS") AS "O1"
INNER JOIN (SELECT "ID" AS "ORDERID", "ORDER_STATUS" AS "STATUS"
FROM "DB"."ORDERS") AS "O2" ON "O2"."orderId" = 's_' || "O1"."orderId"
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_calculated_dimension_in_where_clause() {
        // @formatter:off
        String originalSql =
                "SELECT orderId, status, canRefund " +
                "FROM Orders o " +
                "WHERE canRefund = true";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
SELECT "ORDERID", "STATUS", "CANREFUND"
FROM (SELECT "ORDERID", "STATUS", CASE WHEN "STATUS" = 'completed' THEN TRUE ELSE FALSE END AS "CANREFUND"
FROM (SELECT "ID" AS "ORDERID", "ORDER_STATUS" AS "STATUS"
FROM "DB"."ORDERS")) AS "O"
WHERE "O"."canRefund" = TRUE
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_multiple_calculated_dimensions_in_where_clause() {
        // @formatter:off
        String originalSql = """
                SELECT orderId, status, canRefund, isGoldenDeal
                FROM Orders o
                WHERE canRefund = true AND isGoldenDeal = true
                """;
        // @formatter:on
        // @formatter:off
        String expectedSql = """
                SELECT "id" AS "ORDERID", "order_status" AS "STATUS", CASE WHEN "ORDER_STATUS" = 'completed' THEN TRUE ELSE FALSE END AS "CANREFUND", CASE WHEN "TOTAL_PRICE" > 1000 THEN TRUE ELSE FALSE END AS "ISGOLDENDEAL"
                FROM (SELECT *, CASE WHEN "ORDER_STATUS" = 'completed' THEN TRUE ELSE FALSE END AS "CANREFUND", CASE WHEN "TOTAL_PRICE" > 1000 THEN TRUE ELSE FALSE END AS "ISGOLDENDEAL"
                FROM "DB"."ORDERS") AS "O"
                WHERE "O"."canRefund" = TRUE AND "O"."isGoldenDeal" = TRUE
                """;

        String expectedSql2 = """
SELECT "ORDERID", "STATUS", "CANREFUND", "ISGOLDENDEAL"
FROM (SELECT "TOTALPRICE", "ORDERID", "STATUS", CASE WHEN "STATUS" = 'completed' THEN TRUE ELSE FALSE END AS "CANREFUND", CASE WHEN "TOTALPRICE" > 1000 THEN TRUE ELSE FALSE END AS "ISGOLDENDEAL"
FROM (SELECT "TOTAL_PRICE" AS "TOTALPRICE", "ID" AS "ORDERID", "ORDER_STATUS" AS "STATUS"
FROM "DB"."ORDERS")) AS "O"
WHERE "O"."canRefund" = TRUE AND "O"."isGoldenDeal" = TRUE
""";
        // @formatter:on
        assertTransformOk(originalSql, expectedSql2);
    }

    @Test
    public void test_parse_calculated_dimension_expressions_in_group_by() {
        // @formatter:off
        String originalSql = """
                SELECT s.quarter, s.salesDepartment, SUM(s.amount) as amount
                FROM Sales s
                GROUP BY s.quarter, s.salesDepartment
                """;
        // @formatter:on
        String expectedSql = """
                SELECT "S"."QUARTER", "S"."SALESDEPARTMENT", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "AMOUNT", "SALESDEPARTMENT", "SALESDATE", "TO_CHAR"("SALESDATE", 'YYYY-MM') AS "QUARTER"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "SALES_DEPARTMENT" AS "SALESDEPARTMENT", "SALES_DATE" AS "SALESDATE"
                FROM "SALES")) AS "S"
                GROUP BY "S"."quarter", "S"."salesDepartment"
                """;
        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_dimension_expressions_in_order_by() {
        // @formatter:off
        String originalSql = """
                SELECT orderId, status, canRefund
                FROM Orders o
                ORDER BY o.orderId
                """;
        // @formatter:on
        String expectedSql = """
                SELECT "ORDERID", "STATUS", "CANREFUND"
                FROM (SELECT "ORDERID", "STATUS", CASE WHEN "STATUS" = 'completed' THEN TRUE ELSE FALSE END AS "CANREFUND"
                FROM (SELECT "ID" AS "ORDERID", "ORDER_STATUS" AS "STATUS"
                FROM "DB"."ORDERS")) AS "O"
                ORDER BY "O"."orderId"
                """;

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_calculated_dimension_in_order_by() {
        // @formatter:off
        String originalSql = """
                SELECT orderId, status, canRefund
                FROM Orders o
                ORDER BY o.canRefund
                """;
        // @formatter:on
        String expectedSql = """
                SELECT "ORDERID", "STATUS", "CANREFUND"
                FROM (SELECT "ORDERID", "STATUS", CASE WHEN "STATUS" = 'completed' THEN TRUE ELSE FALSE END AS "CANREFUND"
                FROM (SELECT "ID" AS "ORDERID", "ORDER_STATUS" AS "STATUS"
                FROM "DB"."ORDERS")) AS "O"
                ORDER BY "O"."canRefund"
                """;

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_dimension_expressions_in_join_conditions() {
        // @formatter:off
        String originalSql = """
                SELECT p.name, s.amount
                FROM Products p
                JOIN Sales s ON p.id = s.productId
                """;
        // @formatter:on
        String expectedSql = """
                SELECT "P"."NAME", "S"."AMOUNT"
                FROM (SELECT "ID" AS "ID", "PROD_NAME" AS "NAME"
                FROM "PRODUCTS") AS "P"
                INNER JOIN (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S" ON "P"."id" = "S"."productId"
                """;

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_calculated_dimension_in_join_conditions() {
        // @formatter:off
        String originalSql = """
                SELECT p.name, s.amount
                FROM Products p
                JOIN Sales s ON p.id = s.productId AND s.isStarProduct = true
                """;
        String expectedSql = """
                SELECT "P"."NAME", "S"."AMOUNT"
                FROM (SELECT "ID" AS "ID", "PROD_NAME" AS "NAME"
                FROM "PRODUCTS") AS "P"
                INNER JOIN (SELECT "AMOUNT", "PRODUCTID", CASE WHEN "AMOUNT" > 100 THEN TRUE ELSE FALSE END AS "ISSTARPRODUCT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES")) AS "S" ON "P"."id" = "S"."productId" AND "S"."isStarProduct" = TRUE
                """;
        // @formatter:on
        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_multiple_calculated_dimension_in_join_conditions() {
        // @formatter:off
        String originalSql = """
                SELECT p.name, s.amount
                FROM Products p
                JOIN Sales s ON p.id = s.productId AND s.isStarProduct = true AND s.profit > 200
                """;
        String expectedSql = """
                SELECT "P"."NAME", "S"."AMOUNT"
                FROM (SELECT "ID" AS "ID", "PROD_NAME" AS "NAME"
                FROM "PRODUCTS") AS "P"
                INNER JOIN (SELECT "AMOUNT", "COST", "PRODUCTID", "AMOUNT" - "COST" AS "PROFIT", CASE WHEN "AMOUNT" > 100 THEN TRUE ELSE FALSE END AS "ISSTARPRODUCT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "SALES_COST" AS "COST", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES")) AS "S" ON "P"."id" = "S"."productId" AND "S"."isStarProduct" = TRUE AND "S"."profit" > CAST(200 AS DOUBLE PRECISION)
                """;
        // @formatter:on
        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_table_based_semantic_models() {
        // @formatter:off
        String originalSql = """
                SELECT name, sum(o.price) as price
                FROM order_line_items o
                INNER JOIN Products p ON o.productId = p.id
                WHERE 1 = 1 AND p.name = 'apple'
                GROUP BY name
                """;
        // @formatter:on

        // @formatter:off
        String expectedSql = """
                SELECT "NAME", SUM("O"."PRICE") AS "PRICE"
                FROM (SELECT "PRICE" AS "PRICE", "PRODUCT_ID" AS "PRODUCTID"
                FROM "DB"."ORDER_LINE_ITEMS") AS "O"
                INNER JOIN (SELECT "ID" AS "ID", "PROD_NAME" AS "NAME"
                FROM "PRODUCTS") AS "P" ON "O"."productId" = "P"."id"
                WHERE 1 = 1 AND "P"."name" = 'apple'
                GROUP BY "P"."name"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_sql_based_semantic_model() {
        // @formatter:off
        String originalSql = "select productId, amount from Sales1";
        String expectedSql = """
SELECT "PRODUCTID", "AMOUNT"
FROM (SELECT "AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM (SELECT "PRODUCT_ID", "AMOUNT"
FROM "DB"."SALES"
WHERE 1 = 1)) AS "Sales1"
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_sql_based_semantic_model_with_join() {
        // @formatter:off
        String originalSql = "select p.name, s.amount from products p join Sales1 s on s.productId = p.id";
        String expectedSql = """
SELECT "P"."NAME", "S"."AMOUNT"
FROM (SELECT "ID" AS "ID", "PROD_NAME" AS "NAME"
FROM "PRODUCTS") AS "P"
INNER JOIN (SELECT "AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
FROM (SELECT "PRODUCT_ID", "AMOUNT"
FROM "DB"."SALES"
WHERE 1 = 1)) AS "S" ON "S"."productId" = "P"."id"
""";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_measure() {
        String originalSql = "SELECT AGGREGATE(s.totalSales) as total_sales FROM ViewSales s";
        String expectedSql = """
                SELECT SUM("SALESAMOUNT") AS "TOTAL_SALES"
                FROM (SELECT "SALES_AMOUNT" AS "SALESAMOUNT"
                FROM "SALES") AS "S"\
                """;

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_multiple_measures() {
        String originalSql =
                "SELECT AGGREGATE(s.totalSales) as total_sales, AGGREGATE(s.totalCount) as total_count FROM ViewSales s";
        String expectedSql = """
                SELECT SUM("SALESAMOUNT") AS "TOTAL_SALES", COUNT("PRODUCTID") AS "TOTAL_COUNT"
                FROM (SELECT "SALES_AMOUNT" AS "SALESAMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"\
                """;

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_multiple_measures_no_alias() {
        String originalSql =
                "SELECT AGGREGATE(s.totalSales), AGGREGATE(s.totalCount) FROM ViewSales s";
        String expectedSql = """
                SELECT SUM("SALESAMOUNT"), COUNT("PRODUCTID")
                FROM (SELECT "SALES_AMOUNT" AS "SALESAMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"\
                """;

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_cte_with_simple_measure() {
        // @formatter:off
        String originalSql =
                "WITH s as (select productId, AGGREGATE(totalSales) as totalSales from ViewSales GROUP BY productId) " +
                "SELECT p.id AS product_id, totalSales AS total_sales FROM products p " +
                "JOIN s ON s.productId = p.id";

        String expectedSql = """
                WITH "S" AS (SELECT "PRODUCTID", SUM("SALESAMOUNT") AS "TOTALSALES"
                FROM (SELECT "SALES_AMOUNT" AS "SALESAMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "ViewSales"
                GROUP BY "VIEWSALES"."productId") SELECT "P"."ID" AS "PRODUCT_ID", "TOTALSALES" AS "TOTAL_SALES"
                FROM (SELECT "ID" AS "ID"
                FROM "PRODUCTS") AS "P"
                INNER JOIN "S" ON "S"."PRODUCTID" = "P"."id"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    // TODO 彻底处理大小写问题
    @DisplayName("Measure with filter")
    @Disabled("暂不考虑支持")
    @Test
    public void test_parse_cte_with_measure_filter() {
        // @formatter:off
        String originalSql = "SELECT AGGREGATE(s.goodSales) as good_sales FROM ViewSales s WHERE s.productId <> '1' or s.productId = '2'";
        String expectedSql = """
                SELECT COUNT("PRODUCT_ID") AS "GOOD_SALES"
                FROM "Sales" AS "S"
                WHERE ("S"."product_id" <> '1' OR "S"."product_id" = '2') AND "SALES_AMOUNT" > 100\
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_measure_in_where() {
        // @formatter:off
        String originalSql = "SELECT s.productId FROM ViewSales s WHERE s.goodSales > 10";
        String expectedSql = """
                SELECT "product_id" AS "PRODUCTID"
                FROM "Sales" AS "S"
                WHERE (SELECT COUNT("PRODUCT_ID")
                FROM "Sales" AS "X_X"
                WHERE "X_X"."product_id" = "S"."product_id" AND "SALES_AMOUNT" > 100) > 10\
                """;
        // @formatter:on

        assertTransformFailed(originalSql, "不支持直接引用 Measure 字段: S.goodSales");
    }

    // TODO 彻底处理 Identifier 的大小写问题
    // TODO 处理 metrics filter 中 Identifier 完全限定名问题
    @Disabled
    @Test
    public void test_measure_in_where_() {
        // @formatter:off
        String originalSql = "SELECT s.productId FROM ViewSales s WHERE s.goodSales > 10";
        String expectedSql =
                "SELECT `product_id` AS `PRODUCTID`\n" +
                        "FROM `Sales` AS `S`\n" +
                        "WHERE (SELECT COUNT(`PRODUCT_ID`)\n" +
                        "FROM `SALES` AS `X_X`\n" +
                        "WHERE `X_X`.`product_id` = `S`.`product_id` AND `X_X`.`SALES_AMOUNT` > 100) > 10";
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_measure_in_having() {
        // @formatter:off
        String originalSql = """
            SELECT s.productId, AGGREGATE(s.goodSales) as good_sales
            FROM ViewSales s
            GROUP BY s.productId
            HAVING AGGREGATE(s.goodSales) > 10
            """;
        String expectedSql = """
            SELECT "S"."PRODUCTID", COUNT("PRODUCTID") AS "GOOD_SALES"
            FROM (SELECT "PRODUCT_ID" AS "PRODUCTID"
            FROM "SALES") AS "S"
            GROUP BY "S"."productId"
            HAVING COUNT("PRODUCTID") > 10
            """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_measure_in_order_by() {
        // @formatter:off
        String originalSql = """
            SELECT s.productId, AGGREGATE(s.goodSales) as good_sales
            FROM ViewSales s
            GROUP BY s.productId
            ORDER BY AGGREGATE(s.goodSales) DESC
            """;
        String expectedSql = """
            SELECT "S"."PRODUCTID", COUNT("PRODUCTID") AS "GOOD_SALES"
            FROM (SELECT "PRODUCT_ID" AS "PRODUCTID"
            FROM "SALES") AS "S"
            GROUP BY "S"."productId"
            ORDER BY COUNT("PRODUCTID") DESC
            """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_measure_in_order_by_invalid() {
        // @formatter:off
        String originalSql = """
            SELECT s.productId
            FROM ViewSales s
            ORDER BY s.goodSales DESC
            """;
        // @formatter:on

        assertTransformFailed(originalSql, "不支持直接引用 Measure 字段: S.goodSales");
    }

    @Test
    public void test_measure_in_join_conditions() {
        // @formatter:off
        String originalSql = """
            SELECT p.name, s.salesAmount
            FROM Products p
            JOIN ViewSales s ON s.productId = p.id AND s.goodSales > 10
            """;
        String expectedSql = """
            SELECT "prod_name" AS "NAME", "sales_amount" AS "SALESAMOUNT"
            FROM "Products" AS "P"
            INNER JOIN (SELECT *, (SELECT COUNT("PRODUCT_ID")
            FROM "SALES") AS "GOODSALES"
            FROM "SALES") AS "S" ON "S"."product_id" = "P"."id" AND "S"."goodSales" > 10
            """;
        // @formatter:on

        assertTransformFailed(originalSql, "不支持直接引用 Measure 字段: S.goodSales");
    }

    @Test
    public void test_calculated_measures() {
        // @formatter:off
        String originalSql = """
            SELECT s.productId, AGGREGATE(avgSales) AS avgSales
            FROM ViewSales s
            GROUP BY s.productId
            """;
        String expectedSql = """
            SELECT "S"."PRODUCTID", SUM("SALESAMOUNT") / COUNT("PRODUCTID") AS "AVGSALES"
            FROM (SELECT "SALES_AMOUNT" AS "SALESAMOUNT", "PRODUCT_ID" AS "PRODUCTID"
            FROM "SALES") AS "S"
            GROUP BY "S"."productId"
            """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    // TODO measure in group by
    @Disabled("研究此种场景")
    @Test
    public void test_dimension_target_in_group_by() {
        // @formatter:off
        String originalSql = """
            SELECT s.productId, s.goodSales
            FROM ViewSales s
            GROUP BY s.productId, s.goodSales
            """;
        String expectedSql = """
            SELECT "product_id" AS "PRODUCTID", "good_sales" AS "GOODSALES"
            FROM (
            SELECT
            "product_id" AS "PRODUCTID",
            (
                SELECT COUNT("PRODUCT_ID") AS "GOODSALES"
                FROM "Sales" AS "X_X"
                WHERE "X_X"."product_id" = "S"."product_id" AND "SALES_AMOUNT" > 100
            ) AS "GOODSALES"
            FROM "Sales" AS "S"
            WHERE "SALES_AMOUNT" > 100
            GROUP BY "product_id")
            """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_multiple_join() {
        // @formatter:off
        String originalSql =
                "SELECT o.orderId, p.name, oli.price " +
                "FROM Orders o " +
                "JOIN order_line_items oli ON o.orderId = oli.orderId " +
                "JOIN Products p ON oli.productId = p.id " +
                "WHERE o.status = 'completed'";
        // @formatter:on

        // @formatter:off
        String expectedSql = """
                SELECT "O"."ORDERID", "P"."NAME", "OLI"."PRICE"
                FROM (SELECT "ID" AS "ORDERID", "ORDER_STATUS" AS "STATUS"
                FROM "DB"."ORDERS") AS "O"
                INNER JOIN (SELECT "PRICE" AS "PRICE", "ORDER_ID" AS "ORDERID", "PRODUCT_ID" AS "PRODUCTID"
                FROM "DB"."ORDER_LINE_ITEMS") AS "OLI" ON "O"."orderId" = "OLI"."orderId"
                INNER JOIN (SELECT "ID" AS "ID", "PROD_NAME" AS "NAME"
                FROM "PRODUCTS") AS "P" ON "OLI"."productId" = "P"."id"
                WHERE "O"."status" = 'completed'\
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_simple_window_functions() {

        // @formatter:off
        String originalSql = """
                SELECT
                    productId,
                    sum(s.amount) over (partition by productId order by productId) as amount
                    FROM Sales s
                """;
        String expectedSql = """
                SELECT "PRODUCTID", SUM("S"."AMOUNT") OVER (PARTITION BY "PRODUCTID" ORDER BY "PRODUCTID") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    // TODO 优化
    @Test
    public void test_parse_calculated_dimension_in_window_functions() {
        // @formatter:off
        String originalSql = """
                SELECT
                    productId,
                    sum(s.amount) over (partition by quarter order by productId) as amount
                    FROM Sales s
                """;
        String expectedSql = """
                SELECT "PRODUCTID", SUM("S"."AMOUNT") OVER (PARTITION BY "QUARTER" ORDER BY "PRODUCTID") AS "AMOUNT"
                FROM (SELECT "AMOUNT", "PRODUCTID", "SALESDATE", "TO_CHAR"("SALESDATE", 'YYYY-MM') AS "QUARTER"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID", "SALES_DATE" AS "SALESDATE"
                FROM "SALES")) AS "S"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_union() {
        // @formatter:off
        String originalSql = """
                SELECT productId, sum(s.amount) as amount
                FROM Sales s
                GROUP BY productId
                UNION
                SELECT productId, sum(s.amount) as amount
                FROM Sales s
                GROUP BY productId
                """;
        String expectedSql = """
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                GROUP BY "S"."productId"
                UNION
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                GROUP BY "S"."productId"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_union_all() {
        // @formatter:off
        String originalSql = """
                SELECT productId, sum(s.amount) as amount
                FROM Sales s
                GROUP BY productId
                UNION ALL
                SELECT productId, sum(s.amount) as amount
                FROM Sales s
                GROUP BY productId
                """;
        String expectedSql = """
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                GROUP BY "S"."productId"
                UNION ALL
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                GROUP BY "S"."productId"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_intersect() {
        // @formatter:off
        String originalSql = """
                SELECT productId, sum(s.amount) as amount
                FROM Sales s
                GROUP BY productId
                INTERSECT
                SELECT productId, sum(s.amount) as amount
                FROM Sales s
                GROUP BY productId
                """;
        String expectedSql = """
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                GROUP BY "S"."productId"
                INTERSECT
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                GROUP BY "S"."productId"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_except() {
        // @formatter:off
        String originalSql = """
                SELECT productId, sum(s.amount) as amount
                FROM Sales s
                GROUP BY productId
                EXCEPT
                SELECT productId, sum(s.amount) as amount
                FROM Sales s
                GROUP BY productId
                """;
        String expectedSql = """
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                GROUP BY "S"."productId"
                EXCEPT
                SELECT "PRODUCTID", SUM("S"."AMOUNT") AS "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S"
                GROUP BY "S"."productId"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @DisplayName("测试维度值转换")
    @Nested
    class DimensionValueConversionTestCases {

        @BeforeEach
        void setup() {
            semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
                final Map<String, String> table = new HashMap<>() {{
                    put("Sales::IT", "0101");
                    put("Sales::IT-DEV", "010101");
                    put("Sales::OP", "0102");
                    put("Sales::HR", "0201");
                }};

                @Override
                public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                    if (isPatternMatching) {
                        String pattern = SqlParseUtil.likeToRegex(literalValue);
                        List<String> list = table.keySet().stream()
                                .filter(it -> {
                                    String[] split = it.split("::");
                                    return split[0].equalsIgnoreCase(modelName) && split[1].matches(pattern);
                                })
                                .map(table::get)
                                .toList();
                        return list.isEmpty() ? Optional.empty() : Optional.of(list);
                    }
                    return Optional.ofNullable(
                            table.getOrDefault(modelName + "::" + literalValue, null));
                }
            });
        }

        @Test
        public void test_replace_dimension_value_in_filter() {
            // @formatter:off
        String originalSql = """
                SELECT productId
                FROM Sales s
                WHERE s.salesDepartment = 'IT'
                """;
        String expectedSql = """
                SELECT "PRODUCTID"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "S"
                WHERE "S"."salesDepartment" = '0101'
                """;
        // @formatter:on

            semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
                @Override
                public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                    if ("Sales".equalsIgnoreCase(modelName) && "salesDepartment".equalsIgnoreCase(
                            dimName)) {
                        String dimLabel = "IT";
                        boolean isMatched = dimLabel.equalsIgnoreCase(literalValue);
                        String dimValue = isMatched ? "0101" : null;
                        return Optional.ofNullable(dimValue);
                    }
                    return Optional.empty();
                }
            });

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_replace_dimension_value_in_in_clause() {
            // @formatter:off
        String originalSql = """
                SELECT productId
                FROM Sales s
                WHERE s.salesDepartment in('IT', 'HR', 'OP')
                """;
        String expectedSql = """
                SELECT "PRODUCTID"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "S"
                WHERE "S"."salesDepartment" IN ('0101', '0201', '0102')\
                """;
        // @formatter:on
            semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
                final Map<String, String> table = new HashMap<>() {{
                    put("Sales::IT", "0101");
                    put("Sales::OP", "0102");
                    put("Sales::HR", "0201");
                }};

                @Override
                public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                    return Optional.ofNullable(
                            table.getOrDefault(modelName + "::" + literalValue, null));
                }
            });

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_replace_dimension_value_with_indirect_ref() {
            // @formatter:off
        String originalSql = """
                WITH s as (select productId AS prodId, salesDepartment AS dep from Sales)
                SELECT prodId, dep
                FROM s
                WHERE s.dep = 'IT'
                """;
        String expectedSql = """
                WITH "S" AS (SELECT "PRODUCTID" AS "PRODID", "SALESDEPARTMENT" AS "DEP"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales") SELECT "PRODID", "DEP"
                FROM "S"
                WHERE "S"."DEP" = '0101'
                """;
        // @formatter:on

            semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
                @Override
                public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                    if ("Sales".equalsIgnoreCase(modelName) && "salesDepartment".equalsIgnoreCase(
                            dimName)) {
                        String dimLabel = "IT";
                        boolean isMatched = dimLabel.equalsIgnoreCase(literalValue);
                        String dimValue = isMatched ? "0101" : null;
                        return Optional.ofNullable(dimValue);
                    }
                    return Optional.empty();
                }
            });

            assertTransformOk(originalSql, expectedSql);
        }

        /**
         * TODO 待定
         */
        @Disabled("待定")
        @Test
        public void test_replace_dimension_value_with_indirect_ref_wrapped() {
            // @formatter:off
        String originalSql = """
                WITH s as (select productId AS prodId, salesDepartment AS dep, MAX(salesDepartment) OVER () AS topDep from Sales)
                SELECT prodId, dep, topDep
                FROM s
                WHERE s.topDep = 'IT'
                """;
        String expectedSql = """
                WITH "S" AS (SELECT "product_id" AS "PRODID", "sales_department" AS "DEP", MAX("sales_department") OVER () AS "TOPDEP"
                FROM "Sales") \
                SELECT "S"."PRODID", "S"."DEP", "S"."TOPDEP"
                FROM "S"
                WHERE "S"."TOPDEP" = '0101'
                """;
        // @formatter:on

            semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
                @Override
                public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                    if ("Sales".equalsIgnoreCase(modelName) && "salesDepartment".equalsIgnoreCase(
                            dimName)) {
                        String dimLabel = "IT";
                        boolean isMatched = dimLabel.equalsIgnoreCase(literalValue);
                        String dimValue = isMatched ? "0101" : null;
                        return Optional.ofNullable(dimValue);
                    }
                    return Optional.empty();
                }
            });

            assertTransformOk(originalSql, expectedSql);
        }

        /**
         * TODO 这种情况如何处理？
         */
        @Disabled("待定")
        @Test
        public void test_replace_dimension_value_with_indirect_ref_union() {
            // @formatter:off
        String originalSql = """
                WITH s as (
                    select productId AS prodId, salesDepartment AS dep from Sales
                    union
                    select productId AS prodId, salesDepartment AS dep from Sales
                )
                SELECT prodId, dep
                FROM s
                WHERE s.dep = 'IT'
                """;
        String expectedSql = """
                WITH "S" AS (SELECT "product_id" AS "PRODID", "sales_department" AS "DEP"
                FROM "Sales") \
                SELECT "S"."PRODID", "S"."DEP", "S"."TOPDEP"
                FROM "S"
                WHERE "S"."TOPDEP" = '0101'
                """;
        // @formatter:on

            semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
                @Override
                public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                    if ("Sales".equalsIgnoreCase(modelName) && "salesDepartment".equalsIgnoreCase(
                            dimName)) {
                        String dimLabel = "IT";
                        boolean isMatched = dimLabel.equalsIgnoreCase(literalValue);
                        String dimValue = isMatched ? "0101" : null;
                        return Optional.ofNullable(dimValue);
                    }
                    return Optional.empty();
                }
            });

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_replace_dimension_value_remote_ref_nested() {
            // @formatter:off
        String originalSql = """
                SELECT prodId, dep
                FROM (
                    SELECT prodId, dep
                    FROM (
                        SELECT prodId, dep
                        FROM (
                           SELECT productId AS prodId, salesDepartment AS dep FROM Sales
                        ) AS s2
                    ) AS s1
                ) AS s
                WHERE s.dep = 'IT'
                """;
        String expectedSql = """
                SELECT "PRODID", "DEP"
                FROM (SELECT "PRODID", "DEP"
                FROM (SELECT "PRODID", "DEP"
                FROM (SELECT "PRODUCTID" AS "PRODID", "SALESDEPARTMENT" AS "DEP"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales") AS "S2") AS "S1") AS "S"
                WHERE "S"."DEP" = '0101'
                """;
        // @formatter:on

            semanticSqlParser.setDimensionValueConvertor(new DefaultDimensionValueConvertor() {
                @Override
                public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                    if ("Sales".equalsIgnoreCase(modelName) && "salesDepartment".equalsIgnoreCase(
                            dimName)) {
                        String dimLabel = "IT";
                        boolean isMatched = dimLabel.equalsIgnoreCase(literalValue);
                        String dimValue = isMatched ? "0101" : null;
                        return Optional.ofNullable(dimValue);
                    }
                    return Optional.empty();
                }
            });

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_replace_dimension_value_in_case_expression_1() {
            // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, CASE WHEN salesDepartment = 'IT' THEN 1 WHEN salesDepartment = 'HR' THEN 2 ELSE 0 END AS isIT
                FROM Sales
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT", CASE WHEN "SALESDEPARTMENT" = '0101' THEN 1 WHEN "SALESDEPARTMENT" = '0201' THEN 2 ELSE 0 END AS "ISIT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales"
                """;
        semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
            final Map<String, String> table = new HashMap<>() {{
                put("Sales::IT", "0101");
                put("Sales::OP", "0102");
                put("Sales::HR", "0201");
            }};

            @Override
            public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                return Optional.ofNullable(
                        table.getOrDefault(modelName + "::" + literalValue, null));
            }
        });
        // @formatter:on
            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_replace_dimension_value_in_case_expression_2() {
            // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, CASE salesDepartment WHEN 'IT' THEN 1 WHEN 'HR' THEN 2 ELSE 0 END AS isIT
                FROM Sales
                """;
//        String expectedSql = """
//                SELECT "product_id" AS "PRODUCTID", "sales_department" AS "SALESDEPARTMENT", CASE "Sales"."salesDepartment" WHEN '0101' THEN 1 WHEN '0201' THEN 2 ELSE 0 END AS "ISIT"
//                FROM "Sales"
//                """;
        // TODO 调查: SqlValidator 对 Case Expression 做了 Normalization?
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT", CASE WHEN "SALESDEPARTMENT" = '0101' THEN 1 WHEN "SALESDEPARTMENT" = '0201' THEN 2 ELSE 0 END AS "ISIT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales"
                """;
        semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
            final Map<String, String> table = new HashMap<>() {{
                put("Sales::IT", "0101");
                put("Sales::OP", "0102");
                put("Sales::HR", "0201");
            }};

            @Override
            public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                return Optional.ofNullable(
                        table.getOrDefault(modelName + "::" + literalValue, null));
            }
        });
        // @formatter:on
            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_replace_dimension_value_of_int() {
            // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment
                FROM (SELECT productId, (CASE salesDepartment WHEN '0101' THEN 1 ELSE 0 END) AS salesDepartment FROM Sales) Sales
                WHERE salesDepartment = 1
                """;
        // TODO select list item 的 alias 错误
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT"
                FROM (SELECT "PRODUCTID", CASE WHEN "SALESDEPARTMENT" = '0101' THEN 1 ELSE 0 END AS "SALESDEPARTMENT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales") AS "SALES"
                WHERE "SALES"."SALESDEPARTMENT" = 101
                """;
        // @formatter:on

            semanticSqlParser.setDimensionValueConvertor(new DimensionValueConvertor() {
                final Map<String, Integer> table = new HashMap<>() {{
                    put("Sales::1", 101);
                    put("Sales::2", 201);
                }};

                @Override
                public Optional<Object> convert(String modelName, String dimName, String literalValue, boolean isPatternMatching) {
                    return Optional.ofNullable(
                            table.getOrDefault(modelName + "::" + literalValue, null));
                }
            });

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_replace_dimension_value_of_date() {
            // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment
                FROM Sales
                WHERE salesDate = '2023-01-01'
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT", "SALES_DATE" AS "SALESDATE"
                FROM "SALES") AS "Sales"
                WHERE "SALES"."salesDate" = '2023-01-01'
                """;
        // @formatter:on

            assertTransformOk(originalSql, expectedSql);
        }

        @Disabled("维度值转换是否适用于 like 表达式？")
        @Test
        void test_like_expression() {
            // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment
                FROM Sales
                WHERE salesDepartment NOT LIKE 'IT%'
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales"
                WHERE "SALES"."salesDepartment" NOT IN ('0101', '010101')
                """;
        // @formatter:on

            assertTransformOk(originalSql, expectedSql);
        }
    }

    @Test
    public void test_dimensions_as_function_args_in_select_list() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, TO_CHAR(salesDate, 'yyyy-MM-dd')
                FROM Sales
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT", "SALES_DATE" AS "SALESDATE"
                FROM "SALES") AS "Sales"
                """;

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    void test_dimensions_as_function_args_in_filter() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment
                FROM Sales
                WHERE TO_CHAR(salesDate, 'yyyy-MM-DD') = '2023-01-01'
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT", "SALES_DATE" AS "SALESDATE"
                FROM "SALES") AS "Sales"
                WHERE TO_CHAR(CAST("SALES"."salesDate" AS DATE), 'yyyy-MM-DD') = '2023-01-01'
                """;

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_subquery_in_select_list() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, (SELECT AVG(cost) FROM Sales) AS avg_cost
                FROM Sales
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT", (((SELECT AVG("COST")
                FROM (SELECT "SALES_COST" AS "COST"
                FROM "SALES") AS "Sales"))) AS "AVG_COST"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_subquery_in_where_clause() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment
                FROM Sales
                WHERE cost > (SELECT AVG(cost) FROM Sales)
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT"
                FROM (SELECT "SALES_COST" AS "COST", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales"
                WHERE "SALES"."cost" > (((SELECT AVG("COST")
                FROM (SELECT "SALES_COST" AS "COST"
                FROM "SALES") AS "Sales")))
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_subquery_in_where_clause_exists() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment
                FROM Sales
                WHERE exists (SELECT 1 FROM Sales)
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales"
                WHERE EXISTS (SELECT 1
                FROM (SELECT ''
                FROM "SALES") AS "Sales")
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_subquery_in_where_clause_not_in() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment
                FROM Sales
                WHERE productId not in (SELECT productId FROM Sales)
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "Sales"
                WHERE "SALES"."productId" NOT IN (SELECT "PRODUCTID"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "Sales")
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_dimension_in_correlated_subquery() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, amount
                FROM Sales s
                WHERE s.amount > (SELECT AVG(cost) FROM Sales s2 WHERE s2.productId = s.productId)
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT", "AMOUNT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES") AS "S"
                WHERE "S"."amount" > (((SELECT AVG("COST")
                FROM (SELECT "SALES_COST" AS "COST", "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "S2"
                WHERE "S2"."productId" = "S"."productId")))
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, profit
                FROM Sales s
                WHERE s.profit > 0
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT", "PROFIT"
                FROM (SELECT "AMOUNT", "COST", "PRODUCTID", "SALESDEPARTMENT", "AMOUNT" - "COST" AS "PROFIT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "SALES_COST" AS "COST", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES")) AS "S"
                WHERE "S"."profit" > CAST(0 AS DOUBLE PRECISION)
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_calculated_dimension_in_correlated_subquery() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, profit
                FROM Sales s
                WHERE s.profit > (SELECT AVG(profit) FROM Sales s2 WHERE s2.quarter = s.quarter)
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT", "PROFIT"
                FROM (SELECT "AMOUNT", "COST", "PRODUCTID", "SALESDEPARTMENT", "AMOUNT" - "COST" AS "PROFIT", "TO_CHAR"("SALESDATE", 'YYYY-MM') AS "QUARTER"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "SALES_COST" AS "COST", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES")) AS "S"
                WHERE "S"."profit" > (((SELECT AVG("PROFIT")
                FROM (SELECT "AMOUNT", "COST", "SALESDATE", "AMOUNT" - "COST" AS "PROFIT", "TO_CHAR"("SALESDATE", 'YYYY-MM') AS "QUARTER"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "SALES_COST" AS "COST", "SALES_DATE" AS "SALESDATE"
                FROM "SALES")) AS "S2"
                WHERE "S2"."quarter" = "S"."quarter")))
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_parse_order_by_calculated_dimension() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, profit
                FROM Sales s
                ORDER BY s.profit
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT", "PROFIT"
                FROM (SELECT "AMOUNT", "COST", "PRODUCTID", "SALESDEPARTMENT", "AMOUNT" - "COST" AS "PROFIT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "SALES_COST" AS "COST", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES")) AS "S"
                ORDER BY "S"."profit"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @Test
    public void test_rewrite_table_reference() {
        // @formatter:off
        String originalSql = """
                SELECT productId, salesDepartment, profit
                FROM Sales
                ORDER BY Sales.profit
                """;
        String expectedSql = """
                SELECT "PRODUCTID", "SALESDEPARTMENT", "PROFIT"
                FROM (SELECT "AMOUNT", "COST", "PRODUCTID", "SALESDEPARTMENT", "AMOUNT" - "COST" AS "PROFIT"
                FROM (SELECT "SALES_AMOUNT" AS "AMOUNT", "SALES_COST" AS "COST", "PRODUCT_ID" AS "PRODUCTID", "SALES_DEPARTMENT" AS "SALESDEPARTMENT"
                FROM "SALES")) AS "Sales"
                ORDER BY "SALES"."profit"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    @DisplayName("* related tests")
    @Nested
    class StarTestCases {
        @Test
        public void test_rewrite_star_1() {
            // @formatter:off
        String originalSql = """
                SELECT *
                FROM ViewSales
                """;
        String expectedSql = """
                SELECT *
                FROM (SELECT *
                FROM (SELECT *
                FROM "SALES")) AS "ViewSales"
                """;
        // @formatter:on

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_rewrite_star_2() {
            // @formatter:off
        String originalSql = """
                SELECT s.*
                FROM ViewSales s
                """;
        String expectedSql = """
                SELECT "S".*
                FROM (SELECT *
                FROM (SELECT *
                FROM "SALES")) AS "S"
                """;
        // @formatter:on

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_rewrite_star_3() {
            // @formatter:off
        String originalSql = """
                SELECT s.*, p.*
                FROM ViewSales s
                INNER JOIN Products p ON p.id = s.productId
                """;
        String expectedSql = """
                SELECT "S".*, "P".*
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID", *
                FROM (SELECT *
                FROM "SALES")) AS "S"
                INNER JOIN (SELECT "ID" AS "ID", *
                FROM (SELECT *
                FROM "PRODUCTS")) AS "P" ON "P"."id" = "S"."productId"
                """;
        // @formatter:on

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        public void test_rewrite_count_star() {
            // @formatter:off
        String originalSql = """
                SELECT COUNT(*)
                FROM ViewSales s
                """;
        String expectedSql = """
                SELECT COUNT(*)
                FROM (SELECT ''
                FROM (SELECT *
                FROM "SALES")) AS "S"
                """;
        // @formatter:on

            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        void test_rewrite_count_start_in_group_by() {
            // @formatter:off
        String originalSql = """
                SELECT COUNT(*)
                FROM ViewSales s
                GROUP BY s.productId
                HAVING COUNT(*) > 10
                """;
        String expectedSql = """
                SELECT COUNT(*)
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID"
                FROM (SELECT *
                FROM "SALES")) AS "S"
                GROUP BY "S"."productId"
                HAVING COUNT(*) > CAST(10 AS BIGINT)
                """;
        // @formatter:on
            assertTransformOk(originalSql, expectedSql);
        }

        @Test
        void test_cte_with_select_star() {
            // @formatter:off
        String originalSql = """
                WITH ss as (select * from Sales),
                pp as (select * from Products)
                SELECT productId, amount
                FROM ss s
                JOIN pp p ON s.productId = p.id
                """;
        // @formatter:on

            assertTransformOk(originalSql, "");
        }

        @Test
        void test_subquery_with_select_star() {
            // @formatter:off
        String originalSql = """
                select amount from (select * from Sales) s
                """;
        // @formatter:on

            assertTransformOk(originalSql, "");
        }
    }

    @DisplayName("分页测试")
    @Nested
    class PaginationTestCases {
        @Test
        void test_pagination_no_change() {
            // @formatter:off
            String originalSql = """
                select productId from Sales limit 10 offset 5
                """;
            String expectedSql = """
                SELECT "PRODUCTID"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "Sales"
                OFFSET 5 ROWS
                FETCH NEXT 10 ROWS ONLY
                """.trim();
            // @formatter:on

            ParseResult parse = semanticSqlParser.parse(originalSql);

            assertEquals(expectedSql, parse.getOutputSql());
        }

        @Test
        void test_pagination_ok() {
            // @formatter:off
            String originalSql = """
                select productId from Sales
                """;
            String expectedSql = """
                SELECT "PRODUCTID"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "Sales"
                OFFSET 5 ROWS
                FETCH NEXT 10 ROWS ONLY
                """.trim();
            // @formatter:on

            int offset = 5;
            int limit = 10;
            ParseResult parse = semanticSqlParser.parse(originalSql, withPagination(offset, limit, true));

            assertEquals(expectedSql, parse.getOutputSql());
        }

        @Test
        void test_pagination_force() {
            // @formatter:off
            String originalSql = """
                select productId from Sales
                limit 100 offset 100
                """;
            String expectedSql = """
                SELECT "PRODUCTID"
                FROM (SELECT "PRODUCT_ID" AS "PRODUCTID"
                FROM "SALES") AS "Sales"
                OFFSET 5 ROWS
                FETCH NEXT 10 ROWS ONLY
                """.trim();
            // @formatter:on

            int offset = 5;
            int limit = 10;
            ParseResult parse = semanticSqlParser.parse(originalSql, withPagination(offset, limit, true));

            assertEquals(expectedSql, parse.getOutputSql());
        }
    }

    @DisplayName("DML related tests")
    @Nested
    class DMLTestCases {
        @Test
        void test_dml_delete() {
            String originalSql = "delete from Sales";
            RuntimeException runtimeException = assertThrows(RuntimeException.class, () -> {
                semanticSqlParser.parse(originalSql);
            });
            Assertions.assertEquals("不支持的语句类型: DELETE", runtimeException.getMessage());
        }
    }

    @Test
    void test_parse_system_function() {
        // @formatter:off
        String originalSql = """
                SELECT CURRENT_TIMESTAMP
                FROM Sales
                """;
        String expectedSql = """
                SELECT CURRENT_TIMESTAMP
                FROM "SALES") AS "Sales"
                """;
        // @formatter:on

        assertTransformOk(originalSql, expectedSql);
    }

    void test_cycle() {
        String originalSql = """
                SELECT l_shipdate
                FROM order_line_items
                """;
        assertTransformFailed(originalSql, "发现循环依赖: ORDER_LINE_ITEMS.l_shipdate -> ORDER_LINE_ITEMS.l_shipdate");
    }
}
