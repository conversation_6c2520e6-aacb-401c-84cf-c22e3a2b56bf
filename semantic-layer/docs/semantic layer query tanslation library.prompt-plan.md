### **Step 1: Schema Parsing and Validation**

```
Prompt 1: Schema Parsing and Validation
Context: Parse and validate the JSON schema defining semantic models, dimensions, and metrics.
Requirements:
  - Create a SchemaParser class to load and validate the JSON schema.
  - Validate required fields for semantic models (name, type, table/sql_query).
  - Ensure dimensions/metrics are correctly nested within models.
  - Throw errors for invalid schema structures.
Test Cases:
  - Valid schema with table-based model.
  - Valid schema with SQL-based model.
  - Invalid schema (missing 'table' or 'sql_query').
  - Invalid dimension/metric references.
Code Structure:
  - schema_parser.py
  - test_schema_parser.py
```

### **Step 2: Query Parsing with Apache Calcite**

```
Prompt 2: Query Parsing with Apache Calcite
Context: Set up Apache Calcite to parse PostgreSQL input queries.
Requirements:
  - Integrate Calcite to parse SQL into an AST.
  - Extract semantic model references from the FROM clause.
  - Identify dimensions/metrics in SELECT/GROUP BY clauses.
Test Cases:
  - Parse a simple SELECT query.
  - Detect semantic model references.
  - Handle invalid SQL syntax.
Code Structure:
  - QueryParser.java
  - QueryParserTest.java
```

### **Step 3: Basic Semantic Model Translation**

```python
# Prompt 3: Basic Semantic Model Translation
# Context: Replace semantic models with their underlying table/SQL in the query.
# Requirements:
#   - Substitute table-based models with their table name.
#   - Substitute SQL-based models with their query as a subquery.
# Test Cases:
#   - Translate a query using a table-based model.
#   - Translate a query using a SQL-based model.
# Code Structure:
#   - model_translator.py
#   - test_model_translator.py
```

### **Step 4: Dimension and Metric Translation**

```python
# Prompt 4: Dimension and Metric Translation
# Context: Replace dimensions/metrics with their mapped columns/aggregations.
# Requirements:
#   - Map dimensions to source columns/expressions.
#   - Map metrics to aggregation functions (SUM, AVG).
#   - Handle the MEASURE keyword syntax.
# Test Cases:
#   - Translate a dimension reference.
#   - Translate a metric aggregation.
#   - Handle invalid metric references.
# Code Structure:
#   - metric_dimension_translator.py
#   - test_metric_dimension_translator.py
```

### **Step 5: Filter and WHERE Clause Translation**

```python
# Prompt 5: Filter and WHERE Clause Translation
# Context: Translate semantic filters to database-specific syntax.
# Requirements:
#   - Parse filters in WHERE clauses.
#   - Substitute semantic references with actual columns.
#   - Handle cross-database syntax differences (e.g., date functions).
# Test Cases:
#   - Translate a simple filter (e.g., `order_date > '2023-01-01`).
#   - Handle database-specific date truncation.
# Code Structure:
#   - filter_translator.py
#   - test_filter_translator.py
```

### **Step 6: JOIN Generation for Relationships**

```python
# Prompt 6: JOIN Generation for Relationships
# Context: Automatically generate JOINs for relationships between models.
# Requirements:
#   - Infer relationships from the schema.
#   - Generate LEFT/INNER JOINs based on cardinality.
#   - Deduplicate redundant joins.
# Test Cases:
#   - Generate a JOIN for a many-to-one relationship.
#   - Deduplicate joins in a query with multiple references.
# Code Structure:
#   - join_generator.py
#   - test_join_generator.py
```

### **Step 7: Database-Specific SQL Generation**

```python
# Prompt 7: Database-Specific SQL Generation
# Context: Adapt translated SQL to target database syntax.
# Requirements:
#   - Use Calcite's optimizer for database-specific rewrites.
#   - Handle function substitutions (e.g., PostgreSQL `ILIKE` → MySQL `LIKE`).
# Test Cases:
#   - Translate a query to MySQL syntax.
#   - Handle Oracle-specific pagination.
# Code Structure:
#   - dialect_adapter.py
#   - test_dialect_adapter.py
```

### **Step 8: Error Handling and Validation**

```python
# Prompt 8: Error Handling and Validation
# Context: Implement validation for schema and query compatibility.
# Requirements:
#   - Validate that all referenced models/dimensions exist.
#   - Check for unsupported database features (e.g., window functions in MySQL).
# Test Cases:
#   - Detect invalid model reference in query.
#   - Throw error for unsupported aggregation.
# Code Structure:
#   - validator.py
#   - test_validator.py
```

### **Step 9: Logging and Debugging Infrastructure**

```python
# Prompt 9: Logging and Debugging Infrastructure
# Context: Add detailed logging for translation steps.
# Requirements:
#   - Log original query, intermediate steps, and final SQL.
#   - Include DEBUG/INFO/ERROR levels.
# Test Cases:
#   - Verify logs capture translation steps.
#   - Test error log output.
# Code Structure:
#   - logger.py
#   - test_logger.py
```

### **Step 10: Integration Testing**

```python
# Prompt 10: Integration Testing
# Context: Test end-to-end translation across components.
# Requirements:
#   - Simulate full translation pipeline.
#   - Test cross-database compatibility.
# Test Cases:
#   - Translate a complex query with JOINs, filters, and metrics.
#   - Verify output matches expected SQL for MySQL/Oracle.
# Code Structure:
#   - test_integration.py
```

---

### **Final Assembly Prompt**

```python
# Prompt 11: Final Assembly
# Context: Wire all components into a cohesive library.
# Requirements:
#   - Create a Translator class that orchestrates parsing, validation, and translation.
#   - Expose a simple API: `translate_query(sql: str, target_db: str) -> str`.
# Test Cases:
#   - End-to-end translation using the public API.
# Code Structure:
#   - translator.py
#   - test_translator.py
```

---

### **Testing and Validation Prompts**

```python
# Prompt 12: Unit Test Coverage
# Context: Ensure all components have unit tests.
# Requirements:
#   - Achieve >90% coverage for schema parsing, query translation, and validation.
# Test Cases:
#   - Run coverage reports and fix gaps.

# Prompt 13: Performance Testing
# Context: Optimize translation performance.
# Requirements:
#   - Benchmark translation latency for large schemas/queries.
# Test Cases:
#   - Simulate high-concurrency translation requests.
```

---

### **Deployment and Documentation Prompts**

```python
# Prompt 14: Documentation
# Context: Generate API and developer documentation.
# Requirements:
#   - Use Sphinx/Doxygen to auto-generate docs from code.
#   - Include examples for schema definitions and query syntax.

# Prompt 15: Packaging and Release
# Context: Package the library for distribution.
# Requirements:
#   - Create PyPI/packagist setup.
#   - Publish initial version (v0.1.0).
```
