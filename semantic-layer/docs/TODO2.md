# TODO 补充

## Tasks

* 定义并使用 parser 特定的异常
    * parser 应返回明确的 SqlValidationException 或 SqlParseException 错误信息.
    * 异常分类: AI 可恢复; AI 没有希望恢复
* 聚合表达式展开时, 应该检查字段引用的正确性
* 检查别名是否与关键字冲突，报出相应错误
* 处理 lateral 语句
* 测试 lead 等函数
* 彻底解决 `COUNT(*)` 问题, ~~目前出现在 group by 语句中会报错~~
* ColumnInfo 字段中加入 qualifiedIdentifier
* 处理 like 等含模式匹配的表达式
    * [x] 维度值替换 like 表达式, 将 like 改写为 in 表达式?
    * 优化: 根据匹配上的维度值的数量, 可以考虑将 like 改为 not in,

## Inbox

* 支持 Catalog, Schema 等 namespace
* dim value 不一定是 string 类型: double, integer, date, 函数?
    * 当 Dimension 字段被作为函数参数时,根据函数的返回值类型做不同的处理: 对齐左值和右值数据类型?
* 处理 "时间" 维度的字面量. 应该与维度值别名的处理类似.
* 区分 refSQL 和 计算表达式?
* 支持 Dim value 多种匹配规则: 精确匹配, 模糊匹配?
* Dimension value 转换
    * 作为函数参数时如何处理？
    * 只处理值为 bool 类型的函数?
* 支持在 dimension 定义中使用 measure, 更进一步地 "A Measure as a Dimension"? Measure 是计算的定义, 不是一个值.
* 支持多种 SQL 方言
* Parser 应该汇报更详尽,精确,有启发性的错误信息
* JoinValidator 应该检查 "SELECT * FROM A, B" 形式的 Join
* 支持 Metric 并明确定义其行为
    * Metric 定义: 代表一个特殊视图, 其中有且仅有一个聚合参数, 可以有多个维度查询
* 在一次对话内缓存维度值匹配的结果
* 传给 LLM 的 schema prompt 应该移除数据类型不合的字段, 与 Parser 保持一致.
* 引入维度值类型, 以复用维度值相关的设置

## 优化

* Dimension in Window Functions
* CTE 化
* 如何处理 `SELECT *`: 展开? 保持原样?
    * 目前保持原样
* ModelRender 应该只查询必要的字段
* ModelRender 简单优化: 消减明显不比较的嵌套
* [x] 子查询的 select list 不需要加别名?
* 将 Group BY 的字段列表替换为数字(select 列表的字段序号)?

## 待定

* ~~遇到"裸 Measure"时, 应该报错(目前可能是 BUG)~~; 或者明确地支持"裸 Measure"
* **Metrics in Window Functions 是否合法？** 例如: `AGGREGATE(<METRICS>) OVER(PARTITION BY <Dimentions>)`
* count(*) 需要什么样的特殊处理?
* 支持枚举值
* 支持 Relationship 引用. 如: `"t"."member1"."dimension1"`.
* 考虑 Filter BY <Measure>. 即把 Measure 作为 Dimension 使用
    * 不支持?
    * 在 SQL 内不支持 Filter By <Measure>, 但是可以在语义层定义? 然后, 在转义层等价于在 SQL 内应用 Having 语句?
* 考虑 Measure 的 Filter.
    * 支持在 Measure filter 中使用 dimensions
    * 支持在 Measure filter 中使用 metrics?
* 支持 Enum 定义和使用
* 明确定义 metrics, measure 的行为
    * Metric 相当与视图?
    * Measure 只能出现在可以使用聚合函数的位置?
    * 解析 Metric 或 Measure 时, 需要隐式地或显式地指定 Evaluation Context
    * Measure 作为 Dimension 使用
    * Metric 作为 Dimension 使用
* Cumulative Metric
* Rollup Metric
* 在执行引擎侧进行检测, 是哪些过滤条件或关联条件导致数据匹配失败
* 支持在表达式内引用语义模型的成员或底层表的字段. "{member1}".
* **处理 Metrics Filter 的解析上下文**. 定义 Metrics Filter 时的上下文应该与解析时正确匹配
* 自动添加 order by?

## Done

* [x] Window Functions
    * OVER()
    * OVER(PARTITION BY <Dimentions>)
* 处理 `COUNT(*)`, 现在会把 `*` 作为 Dimension 去查, 在 Oracle 下会报错.
* strictJoin. 检查 join 关系的正当性. SQL 中只允许使用明确定义了的关联关系?
    * 对于间接 join 如何处理? 以 select 为单位进行分析?
* 支持批量地进行维度值转换
* 统一标识符大小写. 表标识符, 表别名, 字段标识符, 字段别名
* parser 的结果包含最外层查询字段的元数据: 字段别名等.
    * [x] 对于 select 列表中的字典码字段, 不做替换, 而是在最外层查询增加一个对应的字典值字段?
        * 无需在 SQL 上做修改, 只需在结果集上处理. Parser 应该提供哪些字段需要替换及如何替换的信息.
* 支持 limit 和 分页
* refSql 中避免使用 `select *`, 只 select 必要的字段
* [x] SemanticColumn. 合并 sql 和 expr 字段, 通过 isCalculated 区分.

## Archived

* [x] 除了 Filter 和 Case Expression, 还有哪些位置需要考虑 Dimension Literal 的转换?
    * Calcite 的 SqlValidator 貌似把 Case Expression 正规化了, 所以可以按 Function 统一处理?
        * YES
* [x] dimensions in GROUP BY
* [x] 多个 dimensions in where
* [x] 多个 dimensions in join condition
* [x] 处理 Grouping Sets, Rollup, Cube
* [x] Union, Intersect, Except
* [x] 子查询内引用外部 dimension
* [x] Subqueries
    * [x] Scalar Subquery
    * [x] Exists Subquery
    * [x] Qualified Subquery
* [x] Order by calculated dimension
* SqlFunction in Filters
* 解析标识符时需要更精确的参考作用域
* [x] Derived Measure: 支持在 Measure 中使用 Measure
* [x] ORDER BY <Measure>
* 合并重复的转换
* 先 Analyze 再 Transform
