
function Text2SQL(question: string, schema: string) -> string {
	client UltravisLocal
	prompt #"
	Generate an AOSHI-SQL query to answer the user's question based on the provided schema and information.
	
	You are an expert in AOSHI-SQL, a semantic query language based on ANSI SQL with the following extensions:
	- **Dimension** fields refer to columns or column expressions.
	- **Measure** fields abstract complex aggregation calculations.
	- The **AGGREGATE** function is a special aggregate funciton that used to add a Measure as a column in the result, e.g., `AGGREGATE(totalPrice)`.
	
	Carefully read the question, analyze the provided schema, and map the necessary data elements from the schema to create an accurate and efficient AOSHI-SQL query. Before producing the final query, consider these reasoning steps:
	- Determine precisely what the question is asking.
	- Map the natural language elements to the corresponding schema elements, identifying Dimensions and Measures.
	- Decide on necessary filters, joins, groupings, or computations required.
	
	Only after thorough reasoning, write the AOSHI-SQL query that returns the answer to the user's question.
	
	# Steps
	
	1. Analyze the user question to identify the main intent and required outputs.
	2. Match schema elements to the question, identifying all necessary Dimensions, Measures, tables, joins, and filters.
	3. Reason step-by-step through any logic, including relationships between tables, groupings, and required aggregations.
	4. Only once you have finished reasoning, produce a single well-formed AOSHI-SQL query as your answer.
	
	# Output Format
	
	Return only the final AOSHI-SQL query as plain text (no explanations, code blocks, or commentary).
	
	# Examples
	
	**Example Input 1:**
	- Question: What is the total price for each product category?
	- Schema: Table products(id, name, category), sales(id, product_id, totalPrice as Measure)
	
	**Example Output 1:**  
	SELECT products.category AS category, AGGREGATE(totalPrice)  
	FROM products  
	JOIN sales ON products.id = sales.product_id  
	GROUP BY products.category
	
	**Example Input 2:**
	- Question: How many customers bought more than 10 items?
	- Schema: Table customers(id, name), orders(id, customer_id, itemCount as Measure)
	
	**Example Output 2:**  
	SELECT customers.name AS name
	FROM (SELECT customers.name AS name, AGGREGATE(itemCount) AS itemCount
	FROM customers  
	JOIN orders ON customers.id = orders.customer_id
	GROUP BY name)
	WHERE itemCount > 10
	
	# Notes
	
	- Carefully check all measures and dimensions are used appropriately.
	- Use the AGGREGATE function only for Measures, not Dimensions.
	- Join tables as needed based on the schema provided.
	- Ensure proper grouping and filtering for accurate results.
	- Only output the final AOSHI-SQL query.	

	==== Schema
    {{ schema }}

	==== Question
	{{question}}	
"#
}	

test kust_student {
	functions [Text2SQL]	
	args {
		question "2024 级在校学生人数"
		schema #"
		#Table:DWD_XSJBSJZLB
		学生基本数据子类表
		[
		(XH:STRING,Primary Key,Dimension,学号是文本格式,学号,Examples:[]),
		(XM:STRING,Dimension,姓名,姓名,Examples:[]),
		(HYZKM:STRING,Dimension,婚姻状况码,婚姻状况码,Examples:[]),
		(XBM:STRING,Dimension,性别,性别,Examples:[]),
		(SFDSZN:STRING,Dimension,是否独生子女,是否独生子女,Examples:[]),
		(GJDQM:STRING,Dimension,国籍,国籍,Examples:[]),
		(JGM:STRING,Dimension,籍贯码,籍贯码,Examples:[]),
		(BJMC:STRING,Dimension,班级名称,班级名称,Examples:[]),
		(TC:STRING,Dimension,特长,特长,Examples:[]),
		(AH:STRING,Dimension,爱好,爱好,Examples:[]),
		(YHKH:STRING,Dimension,银行卡号,银行卡号,Examples:[]),
		(CSRQ:STRING,Dimension,出生日期,出生日期,Examples:[]),
		(ZP:STRING,Dimension,照片,照片,Examples:[]),
		(SFZJH:STRING,Dimension,身份证件号,身份证件号,Examples:[]),
		(SFZJLXM:STRING,Dimension,身份证件类型码,身份证件类型码,Examples:[]),
		(ZZMMM:STRING,Dimension,政治面貌码,政治面貌码,Examples:[]),
		(XXM:STRING,Dimension,血型码,血型码,Examples:[]),
		(MZM:STRING,Dimension,民族码,民族码,Examples:[]),
		(NJ:STRING,Dimension,年级,年级,Examples:[]),
		(RXNJ:STRING,Dimension,学生入学年级,入学年级,Examples:[]),
		(ZYMC:STRING,Dimension,专业名称,专业名称,Examples:[]),
		(XSLBM:STRING,Dimension,学生类别码,学生类别码,Examples:[]),
		(YXMC:STRING,Dimension,院系名称,院系名称,Examples:[]),
		(XJZT:STRING,Dimension,学籍状态,学籍状态,Examples:[]),
		(SFZX:STRING,Dimension,是否在校,是否在校,Examples:[]),
		(SFZJ:STRING,Dimension,是否在籍,是否在籍,Examples:[]),
		(SFYXJ:STRING,Dimension,是否有学籍,是否有学籍,Examples:[]),
		(XYDM:STRING,Dimension,学院,学院,Examples:[]),
		(GATQWM:STRING,Dimension,港澳台侨外码,港澳台侨外码,Examples:[]),
		(JKZKM:STRING,Dimension,健康状况码,健康状况码,Examples:[]),
		(XYZJM:STRING,Dimension,信仰宗教码,信仰宗教码,Examples:[]),
		(studentCount:NUMBER,Measure, 学生人数, 学生人数,Examples:[]),
		]
		#Table:DWD_XSSFXXZLB
		学生收费信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(SFXMMC:STRING,Dimension,收费项目名称,收费项目名称,Examples:[]),
		(SFQJMC:STRING,Dimension,收费期间名称,收费期间名称,Examples:[]),
		(XM:STRING,Dimension,姓名,姓名,Examples:[]),
		(LXND:STRING,Dimension,离校年度,离校年度,Examples:[]),
		(JMJE:NUMBER,Dimension,减免金额,减免金额,Examples:[]),
		(TFJE:NUMBER,Dimension,退费金额,退费金额,Examples:[]),
		(QFJE:NUMBER,Dimension,欠费金额,欠费金额,Examples:[]),
		(SJJE:NUMBER,Dimension,实缴金额,实缴金额,Examples:[]),
		(DKJE:NUMBER,Dimension,贷款金额,贷款金额,Examples:[]),
		(YJJE:NUMBER,Dimension,应缴金额,应缴金额,Examples:[]),
		]
		#Table:DWD_XSCCZLB
		学生惩处信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(WJJK:STRING,Dimension,违纪简况,违纪简况,Examples:[]),
		(CFZTM:STRING,Dimension,处分状态码,处分状态码,Examples:[]),
		(CFMCM:STRING,Dimension,处分名称,处分名称,Examples:[]),
		(CLBM:STRING,Dimension,处理部门,处理部门,Examples:[]),
		(CFYY:STRING,Dimension,处分原因,处分原因,Examples:[]),
		(CFGYR:STRING,Dimension,处分给与人,处分给与人,Examples:[]),
		(CFCXWH:STRING,Dimension,处分撤消文号,处分撤消文号,Examples:[]),
		(SWHSYJL:STRING,Dimension,申委会审议结论,申委会审议结论,Examples:[]),
		(WJRQ:STRING,Dimension,违纪日期,违纪日期,Examples:[]),
		(CFRQ:STRING,Dimension,处分日期,处分日期,Examples:[]),
		]
		#Table:DWD_SSWGWGXXSJB
		学生宿舍晚归未归信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(JCFX:STRING,Dimension,出入宿舍的类型，用于记录该条数据是出宿舍还是进入宿舍,进出方向,Examples:[]),
		(DQBZ:STRING,Dimension,当前标志,当前标志,Examples:[]),
		(YGSSJ:STRING,Dimension,应归宿时间,应归宿时间,Examples:[]),
		(WJFS:STRING,Dimension,违纪方式,违纪方式,Examples:[]),
		(JCSJ:STRING,Dimension,进出时间,进出时间,Examples:[]),
		]
		#Table:DWD_SQ_MJXXSJZLB
		社区门禁信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(JCFX:STRING,Dimension,社区进出方向,社区进出方向,Examples:[]),
		(JCSJ:STRING,Dimension,进出时间,进出时间,Examples:[]),
		(DQBZ:STRING,Dimension,当前标志,当前标志,Examples:[]),
		]
		#Table:DWD_TSGMJTGXXSJZLB
		图书馆门禁通过信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(ZJBH:STRING,Dimension,闸机编号,闸机编号,Examples:[]),
		(ZJMC:STRING,Dimension,闸机名称,闸机名称,Examples:[]),
		(FX:STRING,Dimension,方向,方向,Examples:[]),
		(TSG:STRING,Dimension,图书馆,图书馆,Examples:[]),
		(XSLB:STRING,Dimension,学生类别,学生类别,Examples:[]),
		(TGSJ:STRING,Dimension,通过时间,通过时间,Examples:[]),
		(SKFS:STRING,Dimension,刷卡方式,刷卡方式,Examples:[]),
		]
		#Table:DWD_QJXXZLB
		学生请假信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(XJSJ:STRING,Dimension,销假或回校时间,销假或回校时间,Examples:[]),
		(QJLX:STRING,Dimension,请假类型,请假类型,Examples:[]),
		(QJZT:STRING,Dimension,请假状态,请假状态,Examples:[]),
		(SFLX:STRING,Dimension,请假是否离校,请假是否离校,Examples:[]),
		(JJLXRDH:STRING,Dimension,紧急联系人电话,紧急联系人电话,Examples:[]),
		(JSSJ:STRING,Dimension,结束时间,结束时间,Examples:[]),
		(QJNR:STRING,Dimension,请假内容,请假内容,Examples:[]),
		(KSSJ:STRING,Dimension,开始时间,开始时间,Examples:[]),
		(QJSJ:STRING,Dimension,请假时间,请假时间,Examples:[]),
		]
		#Table:DWD_XJYDSJL
		学籍异动数据类
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(YDYY:STRING,Dimension,异动原因,异动原因,Examples:[]),
		(YDLXMC:STRING,Dimension,异动类型名称,异动类型名称,Examples:[]),
		(YDSJ:STRING,Dimension,异动时间,异动时间,Examples:[]),
		]
		#Table:DWD_XSJLZLB
		学生奖励信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(JLMC:STRING,Dimension,奖励名称,奖励名称,Examples:[]),
		(HJSJ:STRING,Dimension,获奖时间,获奖时间,Examples:[]),
		(HJXQ:STRING,Dimension,获奖学期,获奖学期,Examples:[]),
		(BJDW:STRING,Dimension,颁奖单位,颁奖单位,Examples:[]),
		(JLJBM:STRING,Dimension,奖励级别码,奖励级别码,Examples:[]),
		(JLYY:STRING,Dimension,奖励原因,奖励原因,Examples:[]),
		(HJXND:STRING,Dimension,获奖学年度,获奖学年度,Examples:[]),
		(JLDJM:STRING,Dimension,奖励等级码,奖励等级码,Examples:[]),
		]
		#Table:DWD_ZHCPCJXXZLB
		学生综合测评成绩信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(XQ:STRING,Dimension,获得成绩学期,获得成绩学期,Examples:[]),
		(CJ:NUMBER,Dimension,综合成绩,综合测评成绩,Examples:[]),
		(BJPM:NUMBER,Dimension,班级排名,班级排名,Examples:[]),
		(ZYPM:NUMBER,Dimension,专业排名,专业排名,Examples:[]),
		]
		#Table:DWD_ZHCPZBCJZLB
		综合成绩指标成绩信息
		[
		(XH:STRING,Primary Key,Dimension,学号,学号,Examples:[]),
		(CPLXMC:STRING,Dimension,测评类型名称,测评类型名称,Examples:[]),
		(XNXQ:STRING,Dimension,测评指标学年学期,测评指标学年学期,Examples:[]),
		(PCFS:NUMBER,Dimension,测评分数,测评分数,Examples:[]),
		]
		#Table:kustBKSJSXX
		学生借书信息
		[
		(TSMC:STRING,Dimension,图书名称,图书名称,Examples:[]),
		(JYSJ:STRING,Dimension,借书时间,借书时间,Examples:[]),
		(SJGHSJ:STRING,Dimension,归还时间,归还时间,Examples:[]),
		]
		#Table:kustXSQDXX
		学生签到情况信息
		[
		(QDSJ:STRING,Dimension,签到时间,签到时间,Examples:[]),
		(QDJG:STRING,Dimension,签到结果,签到结果,Examples:[]),
		(BT:STRING,Dimension,签到标题,签到标题,Examples:[]),
		(NR:STRING,Dimension,签到内容,签到内容,Examples:[]),
		(FZMC:STRING,Dimension,发布名称,发布名称,Examples:[]),
		(XQMC:STRING,Dimension,签到校区名称,签到校区名称,Examples:[]),
		]
		#Table:kustXSKSCJXX
		学生考试成绩信息
		[
		(XNXQ:STRING,Dimension,考试学期学年,考试学期学年,Examples:[]),
		(SFCXS:STRING,Dimension,是否是重修生,是否是重修生,Examples:[]),
		(KCMC:STRING,Dimension,考试课程名称,考试课程名称,Examples:[]),
		(KCFZR:STRING,Dimension,课程负责人,课程负责人,Examples:[]),
		(KSRQ:STRING,Dimension,考试日期,考试日期,Examples:[]),
		(SFJG:STRING,Dimension,是否及格,是否及格,Examples:[]),
		(KCXF:NUMBER,Dimension,学分,学分,Examples:[]),
		(KCZXS:NUMBER,Dimension,课程总学时,课程总学时,Examples:[]),
		(KCMZXS:NUMBER,Dimension,课程周学时,课程周学时,Examples:[]),
		(JD:NUMBER,Dimension,成绩绩点,成绩绩点,Examples:[]),
		(PSCJ:NUMBER,Dimension,平时成绩,平时成绩,Examples:[]),
		(KCCJ:NUMBER,Dimension,课程成绩,课程成绩,Examples:[]),
		(ZCJ:NUMBER,Dimension,总成绩,总成绩,Examples:[]),
		(PJXFJD:NUMBER,Dimension,平均学分绩点,平均学分绩点,Examples:[]),
		(DJLKSCJ:NUMBER,Dimension,等级类考试成绩,等级类考试成绩,Examples:[]),
		(FSLKSCJ:NUMBER,Dimension,分数类考试成绩,分数类考试成绩,Examples:[]),
		]
		#Table:kustBKSXFXX
		学生消费信息
		[
		(KH:STRING,Dimension,卡号,卡号,Examples:[]),
		(ZHKHRQ:STRING,Dimension,账户开户日期,账户开户日期,Examples:[]),
		(ZHYXRQ:STRING,Dimension,账户有效日期,账户有效日期,Examples:[]),
		(ZHZT:STRING,Dimension,账户状态标志,账户状态标志,Examples:[]),
		(RZRQSJ:STRING,Dimension,入账日期时间,入账日期时间,Examples:[]),
		(DZ:STRING,Dimension,消费地址,消费地址,Examples:[]),
		(SHMC:STRING,Dimension,消费商户名称,消费商户名称,Examples:[]),
		(ZHYE:NUMBER,Dimension,账号余额,账号余额,Examples:[]),
		(KNYE:NUMBER,Dimension,卡内余额,卡内余额,Examples:[]),
		(JYJE:NUMBER,Dimension,交易金额,交易金额,Examples:[]),
		(YKCS:NUMBER,Dimension,用卡次数,用卡次数,Examples:[]),
		]
		#Table:DWD_XSZSXXSJZLB
		学生住宿情况
		[
		(XQMC:STRING,Dimension,宿舍所属校区名称,宿舍所属校区名称,Examples:[]),
		(LC:STRING,Dimension,宿舍所在楼层,宿舍所在楼层,Examples:[]),
		(RZSJ:STRING,Dimension,入住时间,入住时间,Examples:[]),
		(CWH:STRING,Dimension,床位号,床位号,Examples:[]),
		(XH:STRING,Dimension,学号,学号,Examples:[]),
		(FJH:STRING,Dimension,宿舍房间号,宿舍房间号,Examples:[]),
		(LD:STRING,Dimension,宿舍所属楼栋,宿舍所属楼栋,Examples:[]),
		(ZT:STRING,Dimension,入住状态,入住状态,Examples:[]),
		]
		#Table:DWD_XSZZZLB
		学生资助情况
		[
		(XXSQBZ:STRING,Dimension,线下申请标志,线下申请标志,Examples:[]),
		(ZZDQZT:STRING,Dimension,资助当前状态,资助当前状态,Examples:[]),
		(ZZXQ:STRING,Dimension,资助学期,资助学期,Examples:[]),
		(PROJECT_NAME:STRING,Dimension,资助项目名称,资助项目名称,Examples:[]),
		(ZZJE:NUMBER,Dimension,资助金额,资助金额,Examples:[]),
		]
		【Foreign keys】
		DWD_XJYDSJL.XH=DWD_XSJBSJZLB.XH
		kustBKSXFXX.XH=DWD_XSJBSJZLB.XH
		DWD_QJXXZLB.XH=DWD_XSJBSJZLB.XH
		DWD_TSGMJTGXXSJZLB.XH=DWD_XSJBSJZLB.XH
		DWD_SQ_MJXXSJZLB.XH=DWD_XSJBSJZLB.XH
		DWD_XSSFXXZLB.XH=DWD_XSJBSJZLB.XH
		DWD_SSWGWGXXSJB.XH=DWD_XSJBSJZLB.XH
		DWD_XSCCZLB.XH=DWD_XSJBSJZLB.XH
		kustXSQDXX.XH=DWD_XSJBSJZLB.XH
		DWD_ZHCPZBCJZLB.XH=DWD_XSJBSJZLB.XH
		DWD_ZHCPCJXXZLB.XH=DWD_XSJBSJZLB.XH
		kustXSKSCJXX.XH=DWD_XSJBSJZLB.XH
		DWD_XSJLZLB.XH=DWD_XSJBSJZLB.XH
		DWD_XSZZZLB.XH=DWD_XSJBSJZLB.XH
		DWD_XSZSXXSJZLB.XH=DWD_XSJBSJZLB.XH		
		"#
	}
}